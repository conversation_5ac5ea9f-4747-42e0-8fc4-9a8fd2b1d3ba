import fs from 'fs-extra';
import path from 'path';
import { promisify } from 'util';

export interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified?: Date;
  content?: string;
}

export interface BackupInfo {
  id: string;
  timestamp: Date;
  files: string[];
  description: string;
}

export class FileSystemManager {
  private projectRoot: string;
  private backupDir: string;

  constructor(projectRoot?: string) {
    this.projectRoot = projectRoot || process.cwd();
    this.backupDir = path.join(this.projectRoot, '.self-improving-backups');
    this.ensureBackupDir();
  }

  private async ensureBackupDir(): Promise<void> {
    await fs.ensureDir(this.backupDir);
  }

  async readFile(filePath: string): Promise<string> {
    const fullPath = path.resolve(this.projectRoot, filePath);
    return await fs.readFile(fullPath, 'utf-8');
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    const fullPath = path.resolve(this.projectRoot, filePath);
    await fs.ensureDir(path.dirname(fullPath));
    await fs.writeFile(fullPath, content, 'utf-8');
  }

  async fileExists(filePath: string): Promise<boolean> {
    try {
      const fullPath = path.resolve(this.projectRoot, filePath);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  async getFileInfo(filePath: string): Promise<FileInfo> {
    const fullPath = path.resolve(this.projectRoot, filePath);
    const stats = await fs.stat(fullPath);
    
    const info: FileInfo = {
      name: path.basename(filePath),
      path: filePath,
      type: stats.isDirectory() ? 'directory' : 'file',
      size: stats.size,
      lastModified: stats.mtime,
    };

    if (info.type === 'file' && this.isTextFile(filePath)) {
      try {
        info.content = await this.readFile(filePath);
      } catch (error) {
        console.warn(`Could not read file content for ${filePath}:`, error);
      }
    }

    return info;
  }

  async listDirectory(dirPath: string = '', recursive: boolean = false): Promise<FileInfo[]> {
    const fullPath = path.resolve(this.projectRoot, dirPath);
    const items = await fs.readdir(fullPath);
    const fileInfos: FileInfo[] = [];

    for (const item of items) {
      // Skip node_modules, .git, and backup directories
      if (this.shouldSkipPath(item)) continue;

      const itemPath = path.join(dirPath, item);
      try {
        const info = await this.getFileInfo(itemPath);
        fileInfos.push(info);

        if (recursive && info.type === 'directory') {
          const subItems = await this.listDirectory(itemPath, true);
          fileInfos.push(...subItems);
        }
      } catch (error) {
        console.warn(`Could not get info for ${itemPath}:`, error);
      }
    }

    return fileInfos;
  }

  async createBackup(files: string[], description: string): Promise<string> {
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const backupPath = path.join(this.backupDir, backupId);
    
    await fs.ensureDir(backupPath);

    const backupInfo: BackupInfo = {
      id: backupId,
      timestamp: new Date(),
      files: files,
      description: description
    };

    // Copy files to backup directory
    for (const file of files) {
      try {
        const sourcePath = path.resolve(this.projectRoot, file);
        const targetPath = path.join(backupPath, file);
        
        await fs.ensureDir(path.dirname(targetPath));
        await fs.copy(sourcePath, targetPath);
      } catch (error) {
        console.warn(`Could not backup file ${file}:`, error);
      }
    }

    // Save backup metadata
    await fs.writeJSON(path.join(backupPath, 'backup-info.json'), backupInfo, { spaces: 2 });

    return backupId;
  }

  async restoreBackup(backupId: string): Promise<void> {
    const backupPath = path.join(this.backupDir, backupId);
    const backupInfoPath = path.join(backupPath, 'backup-info.json');
    
    if (!await fs.pathExists(backupInfoPath)) {
      throw new Error(`Backup ${backupId} not found`);
    }

    const backupInfo: BackupInfo = await fs.readJSON(backupInfoPath);

    for (const file of backupInfo.files) {
      try {
        const sourcePath = path.join(backupPath, file);
        const targetPath = path.resolve(this.projectRoot, file);
        
        if (await fs.pathExists(sourcePath)) {
          await fs.ensureDir(path.dirname(targetPath));
          await fs.copy(sourcePath, targetPath);
        }
      } catch (error) {
        console.warn(`Could not restore file ${file}:`, error);
      }
    }
  }

  async listBackups(): Promise<BackupInfo[]> {
    const backups: BackupInfo[] = [];
    
    if (!await fs.pathExists(this.backupDir)) {
      return backups;
    }

    const backupDirs = await fs.readdir(this.backupDir);
    
    for (const dir of backupDirs) {
      try {
        const backupInfoPath = path.join(this.backupDir, dir, 'backup-info.json');
        if (await fs.pathExists(backupInfoPath)) {
          const backupInfo = await fs.readJSON(backupInfoPath);
          // Convert timestamp string back to Date object
          if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {
            backupInfo.timestamp = new Date(backupInfo.timestamp);
          }
          backups.push(backupInfo);
        }
      } catch (error) {
        console.warn(`Could not read backup info for ${dir}:`, error);
      }
    }

    return backups.sort((a, b) => {
      // Handle case where timestamp might be missing or invalid
      const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;
      const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;
      return timeB - timeA;
    });
  }

  private isTextFile(filePath: string): boolean {
    const textExtensions = [
      '.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.txt', '.css', '.scss',
      '.html', '.xml', '.yml', '.yaml', '.env', '.gitignore', '.eslintrc',
      '.prettierrc', '.config', '.lock'
    ];
    
    const ext = path.extname(filePath).toLowerCase();
    return textExtensions.includes(ext) || !ext;
  }

  private shouldSkipPath(pathName: string): boolean {
    const skipPatterns = [
      'node_modules',
      '.git',
      '.next',
      'dist',
      'build',
      '.self-improving-backups',
      '.env.local',
      '.DS_Store',
      'Thumbs.db'
    ];
    
    return skipPatterns.some(pattern => pathName.includes(pattern));
  }

  getProjectRoot(): string {
    return this.projectRoot;
  }
}

export const fileSystem = new FileSystemManager();

