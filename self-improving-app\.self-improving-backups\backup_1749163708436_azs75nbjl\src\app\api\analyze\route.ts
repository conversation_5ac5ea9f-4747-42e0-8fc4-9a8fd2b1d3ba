import { NextRequest, NextResponse } from 'next/server';
import { codeAnalyzer } from '@/lib/analyzer';
import { fileSystem } from '@/lib/filesystem';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const file = searchParams.get('file');
    const full = searchParams.get('full') === 'true';

    if (file) {
      // Analyze specific file
      const cacheKey = `fileInfo:${file}`;
const cachedFileInfo = cache.get(cacheKey);
if (cachedFileInfo) {
  // use cached
} else {
  const fileInfo = await fileSystem.getFileInfo(file);
  cache.set(cacheKey, fileInfo);
}
      const analysis = await codeAnalyzer.analyzeFile(fileInfo);
      
      return NextResponse.json({
        success: true,
        data: analysis
      });
    } else if (full) {
      // Full project analysis
      const analysis = await codeAnalyzer.analyzeProject();
      
      return NextResponse.json({
        success: true,
        data: analysis
      });
    } else {
      // Quick project overview
      const files = await fileSystem.listDirectory('src', true);
      const codeFiles = files.filter(f => 
        f.type === 'file' && 
        (f.name.endsWith('.ts') || f.name.endsWith('.tsx') || 
         f.name.endsWith('.js') || f.name.endsWith('.jsx'))
      );

      return NextResponse.json({
        success: true,
        data: {
          totalFiles: codeFiles.length,
          files: codeFiles.map(f => ({
            name: f.name,
            path: f.path,
            size: f.size,
            lastModified: f.lastModified
          }))
        }
      });
    }
  } catch (error) {
    console.error('Analysis error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'generate-improvements':
        const { file, types } = data;
        const fileInfo = await fileSystem.getFileInfo(file);
        const improvements = await codeAnalyzer.generateImprovements(fileInfo, types);
        
        return NextResponse.json({
          success: true,
          data: improvements
        });

      case 'generate-feature':
        const { description, targetFile } = data;
        const feature = await codeAnalyzer.generateNewFeature(description, targetFile);
        
        return NextResponse.json({
          success: true,
          data: feature
        });

      case 'clear-cache':
        codeAnalyzer.clearCache();
        
        return NextResponse.json({
          success: true,
          message: 'Analysis cache cleared'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Analysis POST error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
