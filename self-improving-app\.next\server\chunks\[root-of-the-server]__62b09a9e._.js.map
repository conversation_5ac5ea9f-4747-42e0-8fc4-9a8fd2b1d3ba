{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/app/api/config/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport fs from 'fs-extra';\nimport path from 'path';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action');\n\n    switch (action) {\n      case 'model':\n        const currentModel = process.env.OPENROUTER_MODEL || 'anthropic/claude-3.5-sonnet';\n        return NextResponse.json({\n          success: true,\n          data: { model: currentModel }\n        });\n\n      case 'status':\n        const hasApiKey = !!process.env.OPENROUTER_API_KEY;\n        const model = process.env.OPENROUTER_MODEL || 'anthropic/claude-3.5-sonnet';\n        \n        return NextResponse.json({\n          success: true,\n          data: {\n            hasApiKey,\n            model,\n            configured: hasApiKey\n          }\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Unknown action'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Config GET error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, data } = body;\n\n    switch (action) {\n      case 'update-model':\n        const { model } = data;\n        if (!model) {\n          return NextResponse.json({\n            success: false,\n            error: 'Model is required'\n          }, { status: 400 });\n        }\n\n        // Update the .env.local file\n        const envPath = path.join(process.cwd(), '.env.local');\n        let envContent = '';\n        \n        try {\n          envContent = await fs.readFile(envPath, 'utf-8');\n        } catch (error) {\n          // File doesn't exist, create new content\n          envContent = '';\n        }\n\n        // Update or add the OPENROUTER_MODEL line\n        const lines = envContent.split('\\n');\n        let modelLineFound = false;\n        \n        for (let i = 0; i < lines.length; i++) {\n          if (lines[i].startsWith('OPENROUTER_MODEL=')) {\n            lines[i] = `OPENROUTER_MODEL=${model}`;\n            modelLineFound = true;\n            break;\n          }\n        }\n        \n        if (!modelLineFound) {\n          lines.push(`OPENROUTER_MODEL=${model}`);\n        }\n        \n        await fs.writeFile(envPath, lines.join('\\n'));\n        \n        return NextResponse.json({\n          success: true,\n          message: 'Model configuration updated. Restart the application to apply changes.'\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Unknown action'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Config POST error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,MAAM,eAAe,QAAQ,GAAG,CAAC,gBAAgB,IAAI;gBACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE,OAAO;oBAAa;gBAC9B;YAEF,KAAK;gBACH,MAAM,YAAY,CAAC,CAAC,QAAQ,GAAG,CAAC,kBAAkB;gBAClD,MAAM,QAAQ,QAAQ,GAAG,CAAC,gBAAgB,IAAI;gBAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBACJ;wBACA;wBACA,YAAY;oBACd;gBACF;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,MAAM,EAAE,KAAK,EAAE,GAAG;gBAClB,IAAI,CAAC,OAAO;oBACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,6BAA6B;gBAC7B,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;gBACzC,IAAI,aAAa;gBAEjB,IAAI;oBACF,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,SAAS;gBAC1C,EAAE,OAAO,OAAO;oBACd,yCAAyC;oBACzC,aAAa;gBACf;gBAEA,0CAA0C;gBAC1C,MAAM,QAAQ,WAAW,KAAK,CAAC;gBAC/B,IAAI,iBAAiB;gBAErB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;oBACrC,IAAI,KAAK,CAAC,EAAE,CAAC,UAAU,CAAC,sBAAsB;wBAC5C,KAAK,CAAC,EAAE,GAAG,CAAC,iBAAiB,EAAE,OAAO;wBACtC,iBAAiB;wBACjB;oBACF;gBACF;gBAEA,IAAI,CAAC,gBAAgB;oBACnB,MAAM,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO;gBACxC;gBAEA,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,SAAS,MAAM,IAAI,CAAC;gBAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;gBACX;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}