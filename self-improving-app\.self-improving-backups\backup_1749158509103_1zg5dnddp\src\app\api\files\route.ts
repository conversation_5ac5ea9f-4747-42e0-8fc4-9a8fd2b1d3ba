import { NextRequest, NextResponse } from 'next/server';
import { fileSystem } from '@/lib/filesystem';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const path = searchParams.get('path') || '';
    const recursive = searchParams.get('recursive') === 'true';
    const action = searchParams.get('action');

    switch (action) {
      case 'list':
        const files = await fileSystem.listDirectory(path, recursive);
        return NextResponse.json({
          success: true,
          data: files
        });

      case 'read':
        if (!path) {
          return NextResponse.json({
            success: false,
            error: 'Path parameter required for read action'
          }, { status: 400 });
        }
        
        const content = await fileSystem.readFile(path);
        return NextResponse.json({
          success: true,
          data: { content, path }
        });

      case 'info':
        if (!path) {
          return NextResponse.json({
            success: false,
            error: 'Path parameter required for info action'
          }, { status: 400 });
        }
        
        const fileInfo = await fileSystem.getFileInfo(path);
        return NextResponse.json({
          success: true,
          data: fileInfo
        });

      case 'backups':
        const backups = await fileSystem.listBackups();
        return NextResponse.json({
          success: true,
          data: backups
        });

      default:
        // Default to listing files
        const defaultFiles = await fileSystem.listDirectory(path, recursive);
        return NextResponse.json({
          success: true,
          data: defaultFiles
        });
    }
  } catch (error) {
    console.error('Files GET error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'write':
        const { path, content } = data;
        if (!path || content === undefined) {
          return NextResponse.json({
            success: false,
            error: 'Path and content are required for write action'
          }, { status: 400 });
        }
        
        await fileSystem.writeFile(path, content);
        return NextResponse.json({
          success: true,
          message: `File ${path} written successfully`
        });

      case 'backup':
        const { files, description } = data;
        if (!files || !Array.isArray(files)) {
          return NextResponse.json({
            success: false,
            error: 'Files array is required for backup action'
          }, { status: 400 });
        }
        
        const backupId = await fileSystem.createBackup(files, description || 'Manual backup');
        return NextResponse.json({
          success: true,
          data: { backupId }
        });

      case 'restore':
        const { backupId: restoreBackupId } = data;
        if (!restoreBackupId) {
          return NextResponse.json({
            success: false,
            error: 'Backup ID is required for restore action'
          }, { status: 400 });
        }
        
        await fileSystem.restoreBackup(restoreBackupId);
        return NextResponse.json({
          success: true,
          message: `Backup ${restoreBackupId} restored successfully`
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Files POST error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
