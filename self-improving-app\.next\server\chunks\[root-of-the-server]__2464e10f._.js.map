{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/filesystem.ts"], "sourcesContent": ["import fs from 'fs-extra';\nimport path from 'path';\nimport { promisify } from 'util';\n\nexport interface FileInfo {\n  name: string;\n  path: string;\n  type: 'file' | 'directory';\n  size?: number;\n  lastModified?: Date;\n  content?: string;\n}\n\nexport interface BackupInfo {\n  id: string;\n  timestamp: Date;\n  files: string[];\n  description: string;\n}\n\nexport class FileSystemManager {\n  private projectRoot: string;\n  private backupDir: string;\n\n  constructor(projectRoot?: string) {\n    this.projectRoot = projectRoot || process.cwd();\n    this.backupDir = path.join(this.projectRoot, '.self-improving-backups');\n    this.ensureBackupDir();\n  }\n\n  private async ensureBackupDir(): Promise<void> {\n    await fs.ensureDir(this.backupDir);\n  }\n\n  async readFile(filePath: string): Promise<string> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    return await fs.readFile(fullPath, 'utf-8');\n  }\n\n  async writeFile(filePath: string, content: string): Promise<void> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    await fs.ensureDir(path.dirname(fullPath));\n    await fs.writeFile(fullPath, content, 'utf-8');\n  }\n\n  async fileExists(filePath: string): Promise<boolean> {\n    try {\n      const fullPath = path.resolve(this.projectRoot, filePath);\n      await fs.access(fullPath);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  async getFileInfo(filePath: string): Promise<FileInfo> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    const stats = await fs.stat(fullPath);\n    \n    const info: FileInfo = {\n      name: path.basename(filePath),\n      path: filePath,\n      type: stats.isDirectory() ? 'directory' : 'file',\n      size: stats.size,\n      lastModified: stats.mtime,\n    };\n\n    if (info.type === 'file' && this.isTextFile(filePath)) {\n      try {\n        info.content = await this.readFile(filePath);\n      } catch (error) {\n        console.warn(`Could not read file content for ${filePath}:`, error);\n      }\n    }\n\n    return info;\n  }\n\n  async listDirectory(dirPath: string = '', recursive: boolean = false): Promise<FileInfo[]> {\n    const fullPath = path.resolve(this.projectRoot, dirPath);\n    const items = await fs.readdir(fullPath);\n    const fileInfos: FileInfo[] = [];\n\n    for (const item of items) {\n      // Skip node_modules, .git, and backup directories\n      if (this.shouldSkipPath(item)) continue;\n\n      const itemPath = path.join(dirPath, item);\n      try {\n        const info = await this.getFileInfo(itemPath);\n        fileInfos.push(info);\n\n        if (recursive && info.type === 'directory') {\n          const subItems = await this.listDirectory(itemPath, true);\n          fileInfos.push(...subItems);\n        }\n      } catch (error) {\n        console.warn(`Could not get info for ${itemPath}:`, error);\n      }\n    }\n\n    return fileInfos;\n  }\n\n  async createBackup(files: string[], description: string): Promise<string> {\n    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    const backupPath = path.join(this.backupDir, backupId);\n    \n    await fs.ensureDir(backupPath);\n\n    const backupInfo: BackupInfo = {\n      id: backupId,\n      timestamp: new Date(),\n      files: files,\n      description: description\n    };\n\n    // Copy files to backup directory\n    for (const file of files) {\n      try {\n        const sourcePath = path.resolve(this.projectRoot, file);\n        const targetPath = path.join(backupPath, file);\n        \n        await fs.ensureDir(path.dirname(targetPath));\n        await fs.copy(sourcePath, targetPath);\n      } catch (error) {\n        console.warn(`Could not backup file ${file}:`, error);\n      }\n    }\n\n    // Save backup metadata\n    await fs.writeJSON(path.join(backupPath, 'backup-info.json'), backupInfo, { spaces: 2 });\n\n    return backupId;\n  }\n\n  async restoreBackup(backupId: string): Promise<void> {\n    const backupPath = path.join(this.backupDir, backupId);\n    const backupInfoPath = path.join(backupPath, 'backup-info.json');\n    \n    if (!await fs.pathExists(backupInfoPath)) {\n      throw new Error(`Backup ${backupId} not found`);\n    }\n\n    const backupInfo: BackupInfo = await fs.readJSON(backupInfoPath);\n\n    for (const file of backupInfo.files) {\n      try {\n        const sourcePath = path.join(backupPath, file);\n        const targetPath = path.resolve(this.projectRoot, file);\n        \n        if (await fs.pathExists(sourcePath)) {\n          await fs.ensureDir(path.dirname(targetPath));\n          await fs.copy(sourcePath, targetPath);\n        }\n      } catch (error) {\n        console.warn(`Could not restore file ${file}:`, error);\n      }\n    }\n  }\n\n  async listBackups(): Promise<BackupInfo[]> {\n    const backups: BackupInfo[] = [];\n    \n    if (!await fs.pathExists(this.backupDir)) {\n      return backups;\n    }\n\n    const backupDirs = await fs.readdir(this.backupDir);\n    \n    for (const dir of backupDirs) {\n      try {\n        const backupInfoPath = path.join(this.backupDir, dir, 'backup-info.json');\n        if (await fs.pathExists(backupInfoPath)) {\n          const backupInfo = await fs.readJSON(backupInfoPath);\n          // Convert timestamp string back to Date object\n          if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {\n            backupInfo.timestamp = new Date(backupInfo.timestamp);\n          }\n          backups.push(backupInfo);\n        }\n      } catch (error) {\n        console.warn(`Could not read backup info for ${dir}:`, error);\n      }\n    }\n\n    return backups.sort((a, b) => {\n      // Handle case where timestamp might be missing or invalid\n      const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;\n      const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;\n      return timeB - timeA;\n    });\n  }\n\n  private isTextFile(filePath: string): boolean {\n    const textExtensions = [\n      '.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.txt', '.css', '.scss',\n      '.html', '.xml', '.yml', '.yaml', '.env', '.gitignore', '.eslintrc',\n      '.prettierrc', '.config', '.lock'\n    ];\n    \n    const ext = path.extname(filePath).toLowerCase();\n    return textExtensions.includes(ext) || !ext;\n  }\n\n  private shouldSkipPath(pathName: string): boolean {\n    const skipPatterns = [\n      'node_modules',\n      '.git',\n      '.next',\n      'dist',\n      'build',\n      '.self-improving-backups',\n      '.env.local',\n      '.DS_Store',\n      'Thumbs.db'\n    ];\n    \n    return skipPatterns.some(pattern => pathName.includes(pattern));\n  }\n\n  getProjectRoot(): string {\n    return this.projectRoot;\n  }\n}\n\nexport const fileSystem = new FileSystemManager();\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAmBO,MAAM;IACH,YAAoB;IACpB,UAAkB;IAE1B,YAAY,WAAoB,CAAE;QAChC,IAAI,CAAC,WAAW,GAAG,eAAe,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QAC7C,IAAI,CAAC,eAAe;IACtB;IAEA,MAAc,kBAAiC;QAC7C,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;IACnC;IAEA,MAAM,SAAS,QAAgB,EAAmB;QAChD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,OAAO,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,UAAU;IACrC;IAEA,MAAM,UAAU,QAAgB,EAAE,OAAe,EAAiB;QAChE,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU,SAAS;IACxC;IAEA,MAAM,WAAW,QAAgB,EAAoB;QACnD,IAAI;YACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAChD,MAAM,6IAAA,CAAA,UAAE,CAAC,MAAM,CAAC;YAChB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,YAAY,QAAgB,EAAqB;QACrD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,QAAQ,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC;QAE5B,MAAM,OAAiB;YACrB,MAAM,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;YACpB,MAAM;YACN,MAAM,MAAM,WAAW,KAAK,cAAc;YAC1C,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,KAAK;QAC3B;QAEA,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACrD,IAAI;gBACF,KAAK,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;YAC/D;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAE,EAAE,YAAqB,KAAK,EAAuB;QACzF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,QAAQ,MAAM,6IAAA,CAAA,UAAE,CAAC,OAAO,CAAC;QAC/B,MAAM,YAAwB,EAAE;QAEhC,KAAK,MAAM,QAAQ,MAAO;YACxB,kDAAkD;YAClD,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO;YAE/B,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;YACpC,IAAI;gBACF,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;gBACpC,UAAU,IAAI,CAAC;gBAEf,IAAI,aAAa,KAAK,IAAI,KAAK,aAAa;oBAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU;oBACpD,UAAU,IAAI,IAAI;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;YACtD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,aAAa,KAAe,EAAE,WAAmB,EAAmB;QACxE,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAClF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE7C,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC;QAEnB,MAAM,aAAyB;YAC7B,IAAI;YACJ,WAAW,IAAI;YACf,OAAO;YACP,aAAa;QACf;QAEA,iCAAiC;QACjC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;gBAClD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;gBAEzC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC,YAAY;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC,EAAE;YACjD;QACF;QAEA,uBAAuB;QACvB,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY,qBAAqB,YAAY;YAAE,QAAQ;QAAE;QAEtF,OAAO;IACT;IAEA,MAAM,cAAc,QAAgB,EAAiB;QACnD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7C,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;QAE7C,IAAI,CAAC,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;YACxC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,SAAS,UAAU,CAAC;QAChD;QAEA,MAAM,aAAyB,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;QAEjD,KAAK,MAAM,QAAQ,WAAW,KAAK,CAAE;YACnC,IAAI;gBACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;gBACzC,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;gBAElD,IAAI,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;oBACnC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC,YAAY;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,EAAE;YAClD;QACF;IACF;IAEA,MAAM,cAAqC;QACzC,MAAM,UAAwB,EAAE;QAEhC,IAAI,CAAC,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YACxC,OAAO;QACT;QAEA,MAAM,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS;QAElD,KAAK,MAAM,OAAO,WAAY;YAC5B,IAAI;gBACF,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK;gBACtD,IAAI,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;oBACvC,MAAM,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;oBACrC,+CAA+C;oBAC/C,IAAI,WAAW,SAAS,IAAI,OAAO,WAAW,SAAS,KAAK,UAAU;wBACpE,WAAW,SAAS,GAAG,IAAI,KAAK,WAAW,SAAS;oBACtD;oBACA,QAAQ,IAAI,CAAC;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC,EAAE;YACzD;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG;YACtB,0DAA0D;YAC1D,MAAM,QAAQ,EAAE,SAAS,YAAY,OAAO,EAAE,SAAS,CAAC,OAAO,KAAK;YACpE,MAAM,QAAQ,EAAE,SAAS,YAAY,OAAO,EAAE,SAAS,CAAC,OAAO,KAAK;YACpE,OAAO,QAAQ;QACjB;IACF;IAEQ,WAAW,QAAgB,EAAW;QAC5C,MAAM,iBAAiB;YACrB;YAAO;YAAQ;YAAO;YAAQ;YAAS;YAAO;YAAQ;YAAQ;YAC9D;YAAS;YAAQ;YAAQ;YAAS;YAAQ;YAAc;YACxD;YAAe;YAAW;SAC3B;QAED,MAAM,MAAM,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;QAC9C,OAAO,eAAe,QAAQ,CAAC,QAAQ,CAAC;IAC1C;IAEQ,eAAe,QAAgB,EAAW;QAChD,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,SAAS,QAAQ,CAAC;IACxD;IAEA,iBAAyB;QACvB,OAAO,IAAI,CAAC,WAAW;IACzB;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/app/api/files/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { fileSystem } from '@/lib/filesystem';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const path = searchParams.get('path') || '';\n    const recursive = searchParams.get('recursive') === 'true';\n    const action = searchParams.get('action');\n\n    switch (action) {\n      case 'list':\n        const files = await fileSystem.listDirectory(path, recursive);\n        return NextResponse.json({\n          success: true,\n          data: files\n        });\n\n      case 'read':\n        if (!path) {\n          return NextResponse.json({\n            success: false,\n            error: 'Path parameter required for read action'\n          }, { status: 400 });\n        }\n        \n        const content = await fileSystem.readFile(path);\n        return NextResponse.json({\n          success: true,\n          data: { content, path }\n        });\n\n      case 'info':\n        if (!path) {\n          return NextResponse.json({\n            success: false,\n            error: 'Path parameter required for info action'\n          }, { status: 400 });\n        }\n        \n        const fileInfo = await fileSystem.getFileInfo(path);\n        return NextResponse.json({\n          success: true,\n          data: fileInfo\n        });\n\n      case 'backups':\n        const backups = await fileSystem.listBackups();\n        return NextResponse.json({\n          success: true,\n          data: backups\n        });\n\n      default:\n        // Default to listing files\n        const defaultFiles = await fileSystem.listDirectory(path, recursive);\n        return NextResponse.json({\n          success: true,\n          data: defaultFiles\n        });\n    }\n  } catch (error) {\n    console.error('Files GET error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, data } = body;\n\n    switch (action) {\n      case 'write':\n        const { path, content } = data;\n        if (!path || content === undefined) {\n          return NextResponse.json({\n            success: false,\n            error: 'Path and content are required for write action'\n          }, { status: 400 });\n        }\n        \n        await fileSystem.writeFile(path, content);\n        return NextResponse.json({\n          success: true,\n          message: `File ${path} written successfully`\n        });\n\n      case 'backup':\n        const { files, description } = data;\n        if (!files || !Array.isArray(files)) {\n          return NextResponse.json({\n            success: false,\n            error: 'Files array is required for backup action'\n          }, { status: 400 });\n        }\n        \n        const backupId = await fileSystem.createBackup(files, description || 'Manual backup');\n        return NextResponse.json({\n          success: true,\n          data: { backupId }\n        });\n\n      case 'restore':\n        const { backupId: restoreBackupId } = data;\n        if (!restoreBackupId) {\n          return NextResponse.json({\n            success: false,\n            error: 'Backup ID is required for restore action'\n          }, { status: 400 });\n        }\n        \n        await fileSystem.restoreBackup(restoreBackupId);\n        return NextResponse.json({\n          success: true,\n          message: `Backup ${restoreBackupId} restored successfully`\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Unknown action'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Files POST error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;QACzC,MAAM,YAAY,aAAa,GAAG,CAAC,iBAAiB;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,MAAM,QAAQ,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,MAAM;gBACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,IAAI,CAAC,MAAM;oBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,UAAU,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;gBAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE;wBAAS;oBAAK;gBACxB;YAEF,KAAK;gBACH,IAAI,CAAC,MAAM;oBACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,WAAW,CAAC;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,UAAU,MAAM,0HAAA,CAAA,aAAU,CAAC,WAAW;gBAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF;gBACE,2BAA2B;gBAC3B,MAAM,eAAe,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,MAAM;gBAC1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;gBAC1B,IAAI,CAAC,QAAQ,YAAY,WAAW;oBAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,0HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,MAAM;gBACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS,CAAC,KAAK,EAAE,KAAK,qBAAqB,CAAC;gBAC9C;YAEF,KAAK;gBACH,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG;gBAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,QAAQ;oBACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,OAAO,eAAe;gBACrE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE;oBAAS;gBACnB;YAEF,KAAK;gBACH,MAAM,EAAE,UAAU,eAAe,EAAE,GAAG;gBACtC,IAAI,CAAC,iBAAiB;oBACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,SAAS;wBACT,OAAO;oBACT,GAAG;wBAAE,QAAQ;oBAAI;gBACnB;gBAEA,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC;gBAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS,CAAC,OAAO,EAAE,gBAAgB,sBAAsB,CAAC;gBAC5D;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}