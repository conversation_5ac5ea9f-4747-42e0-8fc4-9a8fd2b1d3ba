import { NextRequest, NextResponse } from 'next/server';
import { aiClient } from '@/lib/ai-client';

export async function GET(request: NextRequest) {
  try {
    const testCode = `
function hello(name: string): string {
  console.log("Hello " + name);
  return name;
}
`;

    console.log('Testing AI client with simple code...');
    const result = await aiClient.analyzeCode(testCode, 'test.js');
    
    return NextResponse.json({
      success: true,
      data: {
        testCode,
        analysis: result,
        message: 'AI client is working correctly'
      }
    });
  } catch (error) {
    console.error('AI test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'AI client test failed'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { code, fileName } = body;
if (typeof code !== 'string' || typeof fileName !== 'string') {
  return NextResponse.json({ success: false, error: 'Invalid input types' }, { status: 400 });
}

    if (!code || !fileName) {
      return NextResponse.json({
        success: false,
        error: 'Code and fileName are required'
      }, { status: 400 });
    }

    console.log(`Testing AI client with custom code: ${fileName}`);
    const result = await aiClient.analyzeCode(code, fileName);
    
    return NextResponse.json({
      success: true,
      data: {
        analysis: result,
        message: 'Custom code analysis completed'
      }
    });
  } catch (error) {
    console.error('AI test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'AI client test failed'
    }, { status: 500 });
  }
}
