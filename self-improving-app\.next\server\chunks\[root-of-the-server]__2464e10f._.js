module.exports = {

"[project]/.next-internal/server/app/api/files/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/constants [external] (constants, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("constants", () => require("constants"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/filesystem.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileSystemManager": (()=>FileSystemManager),
    "fileSystem": (()=>fileSystem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fs-extra/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileSystemManager {
    projectRoot;
    backupDir;
    constructor(projectRoot){
        this.projectRoot = projectRoot || process.cwd();
        this.backupDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.projectRoot, '.self-improving-backups');
        this.ensureBackupDir();
    }
    async ensureBackupDir() {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(this.backupDir);
    }
    async readFile(filePath) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readFile(fullPath, 'utf-8');
    }
    async writeFile(filePath, content) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(fullPath));
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].writeFile(fullPath, content, 'utf-8');
    }
    async fileExists(filePath) {
        try {
            const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].access(fullPath);
            return true;
        } catch  {
            return false;
        }
    }
    async getFileInfo(filePath) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].stat(fullPath);
        const info = {
            name: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath),
            path: filePath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            lastModified: stats.mtime
        };
        if (info.type === 'file' && this.isTextFile(filePath)) {
            try {
                info.content = await this.readFile(filePath);
            } catch (error) {
                console.warn(`Could not read file content for ${filePath}:`, error);
            }
        }
        return info;
    }
    async listDirectory(dirPath = '', recursive = false) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, dirPath);
        const items = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readdir(fullPath);
        const fileInfos = [];
        for (const item of items){
            // Skip node_modules, .git, and backup directories
            if (this.shouldSkipPath(item)) continue;
            const itemPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, item);
            try {
                const info = await this.getFileInfo(itemPath);
                fileInfos.push(info);
                if (recursive && info.type === 'directory') {
                    const subItems = await this.listDirectory(itemPath, true);
                    fileInfos.push(...subItems);
                }
            } catch (error) {
                console.warn(`Could not get info for ${itemPath}:`, error);
            }
        }
        return fileInfos;
    }
    async createBackup(files, description) {
        const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, backupId);
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(backupPath);
        const backupInfo = {
            id: backupId,
            timestamp: new Date(),
            files: files,
            description: description
        };
        // Copy files to backup directory
        for (const file of files){
            try {
                const sourcePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, file);
                const targetPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, file);
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(targetPath));
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].copy(sourcePath, targetPath);
            } catch (error) {
                console.warn(`Could not backup file ${file}:`, error);
            }
        }
        // Save backup metadata
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].writeJSON(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, 'backup-info.json'), backupInfo, {
            spaces: 2
        });
        return backupId;
    }
    async restoreBackup(backupId) {
        const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, backupId);
        const backupInfoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, 'backup-info.json');
        if (!await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(backupInfoPath)) {
            throw new Error(`Backup ${backupId} not found`);
        }
        const backupInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readJSON(backupInfoPath);
        for (const file of backupInfo.files){
            try {
                const sourcePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, file);
                const targetPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, file);
                if (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(sourcePath)) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(targetPath));
                    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].copy(sourcePath, targetPath);
                }
            } catch (error) {
                console.warn(`Could not restore file ${file}:`, error);
            }
        }
    }
    async listBackups() {
        const backups = [];
        if (!await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(this.backupDir)) {
            return backups;
        }
        const backupDirs = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readdir(this.backupDir);
        for (const dir of backupDirs){
            try {
                const backupInfoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, dir, 'backup-info.json');
                if (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(backupInfoPath)) {
                    const backupInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readJSON(backupInfoPath);
                    // Convert timestamp string back to Date object
                    if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {
                        backupInfo.timestamp = new Date(backupInfo.timestamp);
                    }
                    backups.push(backupInfo);
                }
            } catch (error) {
                console.warn(`Could not read backup info for ${dir}:`, error);
            }
        }
        return backups.sort((a, b)=>{
            // Handle case where timestamp might be missing or invalid
            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;
            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;
            return timeB - timeA;
        });
    }
    isTextFile(filePath) {
        const textExtensions = [
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.json',
            '.md',
            '.txt',
            '.css',
            '.scss',
            '.html',
            '.xml',
            '.yml',
            '.yaml',
            '.env',
            '.gitignore',
            '.eslintrc',
            '.prettierrc',
            '.config',
            '.lock'
        ];
        const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath).toLowerCase();
        return textExtensions.includes(ext) || !ext;
    }
    shouldSkipPath(pathName) {
        const skipPatterns = [
            'node_modules',
            '.git',
            '.next',
            'dist',
            'build',
            '.self-improving-backups',
            '.env.local',
            '.DS_Store',
            'Thumbs.db'
        ];
        return skipPatterns.some((pattern)=>pathName.includes(pattern));
    }
    getProjectRoot() {
        return this.projectRoot;
    }
}
const fileSystem = new FileSystemManager();
}}),
"[project]/src/app/api/files/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/filesystem.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const path = searchParams.get('path') || '';
        const recursive = searchParams.get('recursive') === 'true';
        const action = searchParams.get('action');
        switch(action){
            case 'list':
                const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory(path, recursive);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: files
                });
            case 'read':
                if (!path) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Path parameter required for read action'
                    }, {
                        status: 400
                    });
                }
                const content = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(path);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        content,
                        path
                    }
                });
            case 'info':
                if (!path) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Path parameter required for info action'
                    }, {
                        status: 400
                    });
                }
                const fileInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].getFileInfo(path);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: fileInfo
                });
            case 'backups':
                const backups = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listBackups();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: backups
                });
            default:
                // Default to listing files
                const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory(path, recursive);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: files
                });
        }
    } catch (error) {
        console.error('Files GET error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        if (!body || typeof body !== 'object') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid request body'
            }, {
                status: 400
            });
        }
        const { action, data } = body;
        switch(action){
            case 'write':
                const { path, content } = data;
                if (!path || content === undefined) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Path and content are required for write action'
                    }, {
                        status: 400
                    });
                }
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].writeFile(path, content);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    message: `File ${path} written successfully`
                });
            case 'backup':
                const { files, description } = data;
                if (!files || !Array.isArray(files)) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Files array is required for backup action'
                    }, {
                        status: 400
                    });
                }
                const backupId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].createBackup(files, description || 'Manual backup');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        backupId
                    }
                });
            case 'restore':
                const { backupId: restoreBackupId } = data;
                if (!restoreBackupId) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        success: false,
                        error: 'Backup ID is required for restore action'
                    }, {
                        status: 400
                    });
                }
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].restoreBackup(restoreBackupId);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    message: `Backup ${restoreBackupId} restored successfully`
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Unknown action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Files POST error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2464e10f._.js.map