# OpenRouter Integration Guide

## Overview

This self-improving application has been successfully migrated from OpenAI's direct API to OpenRouter, providing access to multiple AI models through a unified interface. This integration offers greater flexibility, cost optimization, and model diversity.

## What is OpenRouter?

OpenRouter is a unified API that provides access to multiple AI models from different providers including:
- **Anthropic** (Claude models)
- **OpenAI** (GPT models)
- **Meta** (Llama models)
- **Google** (Gemini models)
- **Mistral AI** (Mixtral models)
- And many more...

## Benefits of OpenRouter Integration

### 1. **Model Diversity**
- Access to 50+ AI models from different providers
- Choose the best model for specific tasks
- Compare performance across different models

### 2. **Cost Optimization**
- Transparent pricing across all models
- Choose cost-effective models for different use cases
- No vendor lock-in

### 3. **Reliability**
- Automatic failover between providers
- Reduced dependency on single provider
- Better uptime and availability

### 4. **Flexibility**
- Real-time model switching
- Easy experimentation with new models
- Configurable model selection per task

## Configuration

### Environment Variables

```bash
# Required: Your OpenRouter API key
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: Specify which model to use (defaults to Claude 3.5 Sonnet)
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet

# Optional: Application identification for OpenRouter
NEXT_PUBLIC_APP_NAME=Self-Improving App
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Getting Started

1. **Sign up for OpenRouter**
   - Visit [openrouter.ai](https://openrouter.ai/)
   - Create an account
   - Add credits to your account

2. **Generate API Key**
   - Go to your OpenRouter dashboard
   - Generate a new API key
   - Copy the key to your `.env.local` file

3. **Choose Your Model**
   - Browse available models on OpenRouter
   - Set your preferred model in the environment variables
   - Or use the Settings panel in the application

## Available Models

### Recommended Models

| Model | Provider | Best For | Cost | Speed |
|-------|----------|----------|------|-------|
| `anthropic/claude-3.5-sonnet` | Anthropic | Code analysis, reasoning | Medium | Medium |
| `openai/gpt-4-turbo` | OpenAI | General tasks, speed | Medium | Fast |
| `anthropic/claude-3-haiku` | Anthropic | Simple tasks, cost efficiency | Low | Fast |

### All Supported Models

#### Anthropic Models
- `anthropic/claude-3.5-sonnet` - Best for code analysis
- `anthropic/claude-3-opus` - Most capable, highest cost
- `anthropic/claude-3-sonnet` - Balanced performance
- `anthropic/claude-3-haiku` - Fast and cost-effective

#### OpenAI Models
- `openai/gpt-4` - Original GPT-4
- `openai/gpt-4-turbo` - Faster GPT-4 variant
- `openai/gpt-3.5-turbo` - Cost-effective option

#### Open Source Models
- `meta-llama/llama-3.1-405b` - Large open-source model
- `meta-llama/llama-3.1-70b` - Balanced open-source option
- `mistralai/mixtral-8x7b-instruct` - Efficient mixture of experts

#### Google Models
- `google/gemini-pro` - Google's flagship model
- `google/gemini-pro-vision` - Multimodal capabilities

## Model Selection Guide

### For Code Analysis
**Recommended**: `anthropic/claude-3.5-sonnet`
- Excellent at understanding code structure
- Detailed analysis and suggestions
- Good balance of quality and cost

### For Fast Operations
**Recommended**: `anthropic/claude-3-haiku`
- Fastest response times
- Lowest cost
- Good for simple analysis tasks

### For Complex Reasoning
**Recommended**: `anthropic/claude-3-opus`
- Most capable model
- Best for complex improvements
- Higher cost but highest quality

### For Cost Efficiency
**Recommended**: `meta-llama/llama-3.1-70b`
- Open-source model
- Very cost-effective
- Good performance for most tasks

## Usage in the Application

### 1. **Settings Panel**
- Navigate to the Settings tab
- Select your preferred AI model
- Click "Update Model Configuration"
- Restart the application to apply changes

### 2. **Automatic Model Usage**
All AI operations in the application will use your selected model:
- Code analysis
- Improvement suggestions
- Feature generation
- Bug detection

### 3. **Model-Specific Optimizations**
The application automatically adjusts:
- Prompt formatting for different models
- Response parsing for model-specific outputs
- Error handling for different providers

## Error Handling and Fallbacks

### Robust JSON Parsing
- Multiple parsing strategies for different model outputs
- Automatic cleanup of malformed JSON
- Fallback to partial analysis when full parsing fails

### Model Failover
- Graceful handling of model-specific errors
- Fallback to basic analysis when AI is unavailable
- Detailed error logging for debugging

### Rate Limiting
- Automatic handling of rate limits
- Retry logic for temporary failures
- User-friendly error messages

## Cost Management

### Model Cost Tiers
- **Low Cost**: Haiku, Llama models, Mixtral
- **Medium Cost**: Sonnet, GPT-4 Turbo, Gemini Pro
- **High Cost**: Opus, GPT-4

### Cost Optimization Tips
1. Use Haiku for simple analysis tasks
2. Use Sonnet for detailed code analysis
3. Use Opus only for complex reasoning tasks
4. Monitor usage in OpenRouter dashboard

## Troubleshooting

### Common Issues

#### 1. **API Key Not Working**
- Verify the key is correct in `.env.local`
- Check that you have credits in your OpenRouter account
- Ensure the key has proper permissions

#### 2. **Model Not Available**
- Check if the model is still available on OpenRouter
- Try switching to a different model
- Verify the model ID is correct

#### 3. **Parsing Errors**
- The application has robust error handling
- Check the console for detailed error messages
- Try switching to a different model if issues persist

#### 4. **Slow Responses**
- Some models are slower than others
- Try switching to a faster model like Haiku
- Check OpenRouter status page for provider issues

### Debug Mode

Enable debug logging by setting:
```bash
NODE_ENV=development
```

This will provide detailed logs of:
- API requests and responses
- JSON parsing attempts
- Error details and fallbacks

## Performance Monitoring

### Built-in Metrics
The application tracks:
- Response times per model
- Success/failure rates
- Cost per analysis
- Model performance comparisons

### OpenRouter Dashboard
Monitor usage at [openrouter.ai/activity](https://openrouter.ai/activity):
- Request counts
- Cost breakdown
- Model performance
- Error rates

## Future Enhancements

### Planned Features
1. **Automatic Model Selection**
   - Choose optimal model based on task type
   - Cost-aware model selection
   - Performance-based recommendations

2. **Model Performance Analytics**
   - Track which models work best for different tasks
   - Automatic optimization suggestions
   - Cost vs. quality analysis

3. **Advanced Fallback Strategies**
   - Multi-model consensus for critical decisions
   - Automatic model switching on errors
   - Quality-based model selection

## Support

For issues related to:
- **OpenRouter API**: Contact OpenRouter support
- **Application Integration**: Check the application logs and GitHub issues
- **Model-Specific Issues**: Refer to the model provider's documentation

## Conclusion

The OpenRouter integration provides a robust, flexible, and cost-effective solution for AI-powered code analysis and improvement. With access to multiple models and providers, the application can adapt to different needs and requirements while maintaining high reliability and performance.
