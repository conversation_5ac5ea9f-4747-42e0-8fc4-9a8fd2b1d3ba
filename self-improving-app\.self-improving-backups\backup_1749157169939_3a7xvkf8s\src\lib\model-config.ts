export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  description: string;
  strengths: string[];
  costTier: 'low' | 'medium' | 'high';
  speed: 'fast' | 'medium' | 'slow';
  recommended: boolean;
}

export const AVAILABLE_MODELS: ModelInfo[] = [
  {
    id: 'openai/gpt-4.1-nano',
    name: 'GPT-4.1 Nano',
    provider: 'OpenAI',
    description: 'The REQUIRED model for this application - fast, efficient, and cost-effective',
    strengths: ['Required model', 'Fast responses', 'Cost effective', 'Code analysis'],
    costTier: 'low',
    speed: 'fast',
    recommended: true
  },
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: 'Excellent for code analysis, reasoning, and complex problem-solving',
    strengths: ['Code analysis', 'Complex reasoning', 'Detailed explanations'],
    costTier: 'medium',
    speed: 'medium',
    recommended: false
  },
  {
    id: 'openai/gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    description: 'Strong general-purpose model with broad knowledge',
    strengths: ['General knowledge', 'Creative tasks', 'Code generation'],
    costTier: 'high',
    speed: 'slow',
    recommended: false
  },
  {
    id: 'openai/gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    description: 'Faster variant of GPT-4 with good performance',
    strengths: ['Speed', 'Code generation', 'General tasks'],
    costTier: 'medium',
    speed: 'fast',
    recommended: false
  },
  {
    id: 'anthropic/claude-3-haiku',
    name: 'Claude 3 Haiku',
    provider: 'Anthropic',
    description: 'Fast and cost-effective for simple tasks',
    strengths: ['Speed', 'Cost efficiency', 'Simple analysis'],
    costTier: 'low',
    speed: 'fast',
    recommended: false
  },
  {
    id: 'meta-llama/llama-3.1-405b',
    name: 'Llama 3.1 405B',
    provider: 'Meta',
    description: 'Open-source alternative with strong performance',
    strengths: ['Open source', 'Code understanding', 'Cost effective'],
    costTier: 'low',
    speed: 'medium',
    recommended: false
  },
  {
    id: 'google/gemini-pro',
    name: 'Gemini Pro',
    provider: 'Google',
    description: 'Google\'s flagship model with multimodal capabilities',
    strengths: ['Multimodal', 'Reasoning', 'Code analysis'],
    costTier: 'medium',
    speed: 'medium',
    recommended: false
  },
  {
    id: 'mistralai/mixtral-8x7b-instruct',
    name: 'Mixtral 8x7B',
    provider: 'Mistral AI',
    description: 'Efficient mixture of experts model',
    strengths: ['Efficiency', 'Code tasks', 'Multilingual'],
    costTier: 'low',
    speed: 'fast',
    recommended: false
  }
];

export function getModelById(modelId: string): ModelInfo | undefined {
  return AVAILABLE_MODELS.find(model => model.id === modelId);
}

export function getRecommendedModel(): ModelInfo {
  return AVAILABLE_MODELS.find(model => model.recommended) || AVAILABLE_MODELS[0];
}

export function getModelsByProvider(provider: string): ModelInfo[] {
  return AVAILABLE_MODELS.filter(model => model.provider === provider);
}

export function getModelsByCostTier(costTier: 'low' | 'medium' | 'high'): ModelInfo[] {
  return AVAILABLE_MODELS.filter(model => model.costTier === costTier);
}

export function getModelsBySpeed(speed: 'fast' | 'medium' | 'slow'): ModelInfo[] {
  return AVAILABLE_MODELS.filter(model => model.speed === speed);
}

export class ModelManager {
  private currentModel: string;

  constructor(defaultModel?: string) {
    this.currentModel = defaultModel || getRecommendedModel().id;
  }

  getCurrentModel(): string {
    return this.currentModel;
  }

  getCurrentModelInfo(): ModelInfo | undefined {
    return getModelById(this.currentModel);
  }

  setModel(modelId: string): boolean {
    const model = getModelById(modelId);
    if (model) {
      this.currentModel = modelId;
      return true;
    }
    return false;
  }

  getAvailableModels(): ModelInfo[] {
    return AVAILABLE_MODELS;
  }

  getModelRecommendations(task: 'analysis' | 'generation' | 'speed' | 'cost'): ModelInfo[] {
    switch (task) {
      case 'analysis':
        return AVAILABLE_MODELS.filter(model => 
          model.strengths.some(strength => 
            strength.toLowerCase().includes('analysis') || 
            strength.toLowerCase().includes('reasoning')
          )
        );
      case 'generation':
        return AVAILABLE_MODELS.filter(model => 
          model.strengths.some(strength => 
            strength.toLowerCase().includes('generation') || 
            strength.toLowerCase().includes('creative')
          )
        );
      case 'speed':
        return getModelsBySpeed('fast');
      case 'cost':
        return getModelsByCostTier('low');
      default:
        return [getRecommendedModel()];
    }
  }
}

export const modelManager = new ModelManager();
