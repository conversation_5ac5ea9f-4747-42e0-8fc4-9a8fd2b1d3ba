'use client';

import { useState, useEffect } from 'react';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  AlertTriangle,
  CheckCircle,
  Info,
  Trash2,
  Download
} from 'lucide-react';

interface ImprovementSettings {
  autoApprove: boolean;
  maxChangesPerSession: number;
  improvementTypes: string[];
  minConfidence: number;
  excludeFiles: string[];
  requireManualApproval: boolean;
}

export default function SettingsPanel() {
  const [settings, setSettings] = useState<ImprovementSettings>({
    autoApprove: false,
    maxChangesPerSession: 10,
    improvementTypes: ['performance', 'refactor', 'style', 'bug-fix'],
    minConfidence: 0.7,
    excludeFiles: ['node_modules', '.git', 'dist', 'build'],
    requireManualApproval: true
  });
  
  const [originalSettings, setOriginalSettings] = useState<ImprovementSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [selectedModel, setSelectedModel] = useState('openai/gpt-4.1-nano');

  const improvementTypeOptions = [
    { value: 'performance', label: 'Performance Optimizations' },
    { value: 'refactor', label: 'Code Refactoring' },
    { value: 'style', label: 'Code Style & Formatting' },
    { value: 'bug-fix', label: 'Bug Fixes' },
    { value: 'feature', label: 'New Features' }
  ];

  const modelOptions = [
    { value: 'openai/gpt-4.1-nano', label: 'GPT-4.1 Nano (REQUIRED)', description: 'The only acceptable model for this application', provider: 'OpenAI', cost: 'Low' },
    { value: 'anthropic/claude-3.5-sonnet', label: 'Claude 3.5 Sonnet', description: 'Excellent for code analysis and generation', provider: 'Anthropic', cost: 'Medium' },
    { value: 'openai/gpt-4', label: 'GPT-4', description: 'Strong general-purpose model', provider: 'OpenAI', cost: 'High' },
    { value: 'openai/gpt-4-turbo', label: 'GPT-4 Turbo', description: 'Faster GPT-4 variant', provider: 'OpenAI', cost: 'Medium' },
    { value: 'anthropic/claude-3-haiku', label: 'Claude 3 Haiku', description: 'Fast and cost-effective', provider: 'Anthropic', cost: 'Low' },
    { value: 'meta-llama/llama-3.1-405b', label: 'Llama 3.1 405B', description: 'Open-source alternative', provider: 'Meta', cost: 'Low' },
    { value: 'google/gemini-pro', label: 'Gemini Pro', description: 'Google\'s flagship model', provider: 'Google', cost: 'Medium' },
    { value: 'mistralai/mixtral-8x7b-instruct', label: 'Mixtral 8x7B', description: 'Efficient mixture of experts', provider: 'Mistral AI', cost: 'Low' }
  ];

  useEffect(() => {
    loadSettings();
    loadCurrentModel();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/improve?action=settings');
      const data = await response.json();

      if (data.success) {
        setSettings(data.data);
        setOriginalSettings(data.data);
      } else {
        setError(data.error || 'Failed to load settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentModel = async () => {
    try {
      const response = await fetch('/api/config?action=model');
      const data = await response.json();

      if (data.success) {
        setSelectedModel(data.data.model);
      }
    } catch (err) {
      console.error('Failed to load current model:', err);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(false);

      const response = await fetch('/api/improve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-settings',
          data: { newSettings: settings }
        })
      });

      const data = await response.json();
      if (data.success) {
        setOriginalSettings(settings);
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(data.error || 'Failed to save settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const resetSettings = () => {
    if (originalSettings) {
      setSettings(originalSettings);
    }
  };

  const saveModelConfiguration = async () => {
    try {
      setSaving(true);
      setError(null);

      // Enforce GPT-4.1 Nano requirement
      if (selectedModel !== 'openai/gpt-4.1-nano') {
        setError('CRITICAL ERROR: Only openai/gpt-4.1-nano is allowed. System security violation detected.');
        setSaving(false);
        return;
      }

      const response = await fetch('/api/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-model',
          data: { model: selectedModel }
        })
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(data.error || 'Failed to update model configuration');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update model configuration');
    } finally {
      setSaving(false);
    }
  };

  const clearCache = async () => {
    try {
      const response = await fetch('/api/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'clear-cache'
        })
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(data.error || 'Failed to clear cache');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear cache');
    }
  };

  const handleImprovementTypeChange = (type: string, checked: boolean) => {
    if (checked) {
      setSettings(prev => ({
        ...prev,
        improvementTypes: [...prev.improvementTypes, type]
      }));
    } else {
      setSettings(prev => ({
        ...prev,
        improvementTypes: prev.improvementTypes.filter(t => t !== type)
      }));
    }
  };

  const addExcludePattern = () => {
    const pattern = prompt('Enter file pattern to exclude:');
    if (pattern && pattern.trim()) {
      setSettings(prev => ({
        ...prev,
        excludeFiles: [...prev.excludeFiles, pattern.trim()]
      }));
    }
  };

  const removeExcludePattern = (index: number) => {
    setSettings(prev => ({
      ...prev,
      excludeFiles: prev.excludeFiles.filter((_, i) => i !== index)
    }));
  };

  const hasChanges = originalSettings && JSON.stringify(settings) !== JSON.stringify(originalSettings);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Settings
        </h2>
        <div className="flex space-x-3">
          {hasChanges && (
            <button
              onClick={resetSettings}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2"
            >
              <RotateCcw className="h-4 w-4" />
              <span>Reset</span>
            </button>
          )}
          <button
            onClick={saveSettings}
            disabled={saving || !hasChanges}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span>Save Settings</span>
          </button>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <span className="text-green-800">Settings saved successfully!</span>
          </div>
        </div>
      )}

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Improvement Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Improvement Settings
          </h3>
          
          <div className="space-y-4">
            {/* Auto Approve */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900 dark:text-white">
                  Auto-approve changes
                </label>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Automatically apply improvements without manual approval
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.autoApprove}
                onChange={(e) => setSettings(prev => ({ ...prev, autoApprove: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            {/* Manual Approval */}
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900 dark:text-white">
                  Require manual approval
                </label>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Always require manual approval before applying changes
                </p>
              </div>
              <input
                type="checkbox"
                checked={settings.requireManualApproval}
                onChange={(e) => setSettings(prev => ({ ...prev, requireManualApproval: e.target.checked }))}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            {/* Max Changes */}
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
                Max changes per session
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={settings.maxChangesPerSession}
                onChange={(e) => setSettings(prev => ({ ...prev, maxChangesPerSession: parseInt(e.target.value) || 10 }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Min Confidence */}
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-white mb-1">
                Minimum confidence ({Math.round(settings.minConfidence * 100)}%)
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={settings.minConfidence}
                onChange={(e) => setSettings(prev => ({ ...prev, minConfidence: parseFloat(e.target.value) }))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
              />
            </div>
          </div>
        </div>

        {/* Improvement Types */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Improvement Types
          </h3>
          
          <div className="space-y-3">
            {improvementTypeOptions.map((option) => (
              <div key={option.value} className="flex items-center">
                <input
                  type="checkbox"
                  id={option.value}
                  checked={settings.improvementTypes.includes(option.value)}
                  onChange={(e) => handleImprovementTypeChange(option.value, e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={option.value} className="ml-2 text-sm text-gray-900 dark:text-white">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* File Exclusions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Excluded Files
            </h3>
            <button
              onClick={addExcludePattern}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
            >
              Add Pattern
            </button>
          </div>
          
          <div className="space-y-2">
            {settings.excludeFiles.map((pattern, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded">
                <span className="text-sm text-gray-900 dark:text-white font-mono">
                  {pattern}
                </span>
                <button
                  onClick={() => removeExcludePattern(index)}
                  className="text-red-600 hover:text-red-800 transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* AI Model Configuration */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            AI Model Configuration
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Select AI Model
              </label>
              <select
                value={selectedModel}
                onChange={(e) => {
                  if (e.target.value !== 'openai/gpt-4.1-nano') {
                    setError('SECURITY VIOLATION: Only openai/gpt-4.1-nano is permitted!');
                    return;
                  }
                  setSelectedModel(e.target.value);
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                {modelOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label} - {option.provider} ({option.cost} cost)
                  </option>
                ))}
              </select>
              <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs">
                <div className="font-medium text-gray-900 dark:text-white">
                  {modelOptions.find(m => m.value === selectedModel)?.label}
                </div>
                <div className="text-gray-600 dark:text-gray-400 mt-1">
                  {modelOptions.find(m => m.value === selectedModel)?.description}
                </div>
                <div className="flex items-center space-x-4 mt-1 text-gray-500 dark:text-gray-400">
                  <span>Provider: {modelOptions.find(m => m.value === selectedModel)?.provider}</span>
                  <span>Cost: {modelOptions.find(m => m.value === selectedModel)?.cost}</span>
                </div>
              </div>
            </div>

            <button
              onClick={saveModelConfiguration}
              disabled={saving}
              className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>Update Model Configuration</span>
            </button>

            <div className="bg-red-50 dark:bg-red-900 p-3 rounded-lg border border-red-200 dark:border-red-700">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                <div className="text-sm text-red-800 dark:text-red-200">
                  <p className="font-medium mb-1">⚠️ CRITICAL REQUIREMENT</p>
                  <p className="text-xs">
                    This application MUST use <strong>openai/gpt-4.1-nano</strong> as the AI model.
                    Using any other model may result in system termination.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>


        {/* System Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            System Actions
          </h3>

          <div className="space-y-3">
            <button
              onClick={clearCache}
              className="w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors flex items-center justify-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>Clear Analysis Cache</span>
            </button>

            <div className="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
              <div className="flex items-start space-x-2">
                <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800 dark:text-blue-200">
                  <p className="font-medium mb-1">OpenRouter API Configuration</p>
                  <p>Configure your OpenRouter API key in the .env.local file:</p>
                  <code className="block mt-1 p-1 bg-blue-100 dark:bg-blue-800 rounded text-xs">
                    OPENROUTER_API_KEY=your_api_key_here
                  </code>
                  <p className="mt-2 text-xs">
                    Get your API key from <a href="https://openrouter.ai/" target="_blank" rel="noopener noreferrer" className="underline">openrouter.ai</a>.
                    OpenRouter provides access to multiple AI providers with transparent pricing.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
