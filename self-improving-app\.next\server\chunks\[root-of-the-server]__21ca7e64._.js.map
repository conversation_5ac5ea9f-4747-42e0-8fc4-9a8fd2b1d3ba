{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/filesystem.ts"], "sourcesContent": ["import fs from 'fs-extra';\nimport path from 'path';\nimport { promisify } from 'util';\n\nexport interface FileInfo {\n  name: string;\n  path: string;\n  type: 'file' | 'directory';\n  size?: number;\n  lastModified?: Date;\n  content?: string;\n}\n\nexport interface BackupInfo {\n  id: string;\n  timestamp: Date;\n  files: string[];\n  description: string;\n}\n\nexport class FileSystemManager {\n  private projectRoot: string;\n  private backupDir: string;\n\n  constructor(projectRoot?: string) {\n    this.projectRoot = projectRoot || process.cwd();\n    this.backupDir = path.join(this.projectRoot, '.self-improving-backups');\n    this.ensureBackupDir();\n  }\n\n  private async ensureBackupDir(): Promise<void> {\n    await fs.ensureDir(this.backupDir);\n  }\n\n  async readFile(filePath: string): Promise<string> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    return await fs.readFile(fullPath, 'utf-8');\n  }\n\n  async writeFile(filePath: string, content: string): Promise<void> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    await fs.ensureDir(path.dirname(fullPath));\n    await fs.writeFile(fullPath, content, 'utf-8');\n  }\n\n  async fileExists(filePath: string): Promise<boolean> {\n    try {\n      const fullPath = path.resolve(this.projectRoot, filePath);\n      await fs.access(fullPath);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  async getFileInfo(filePath: string): Promise<FileInfo> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    const stats = await fs.stat(fullPath);\n    \n    const info: FileInfo = {\n      name: path.basename(filePath),\n      path: filePath,\n      type: stats.isDirectory() ? 'directory' : 'file',\n      size: stats.size,\n      lastModified: stats.mtime,\n    };\n\n    if (info.type === 'file' && this.isTextFile(filePath)) {\n      try {\n        info.content = await this.readFile(filePath);\n      } catch (error) {\n        console.warn(`Could not read file content for ${filePath}:`, error);\n      }\n    }\n\n    return info;\n  }\n\n  async listDirectory(dirPath: string = '', recursive: boolean = false): Promise<FileInfo[]> {\n    const fullPath = path.resolve(this.projectRoot, dirPath);\n    const items = await fs.readdir(fullPath);\n    const fileInfos: FileInfo[] = [];\n\n    for (const item of items) {\n      // Skip node_modules, .git, and backup directories\n      if (this.shouldSkipPath(item)) continue;\n\n      const itemPath = path.join(dirPath, item);\n      try {\n        const info = await this.getFileInfo(itemPath);\n        fileInfos.push(info);\n\n        if (recursive && info.type === 'directory') {\n          const subItems = await this.listDirectory(itemPath, true);\n          fileInfos.push(...subItems);\n        }\n      } catch (error) {\n        console.warn(`Could not get info for ${itemPath}:`, error);\n      }\n    }\n\n    return fileInfos;\n  }\n\n  async createBackup(files: string[], description: string): Promise<string> {\n    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    const backupPath = path.join(this.backupDir, backupId);\n    \n    await fs.ensureDir(backupPath);\n\n    const backupInfo: BackupInfo = {\n      id: backupId,\n      timestamp: new Date(),\n      files: files,\n      description: description\n    };\n\n    // Copy files to backup directory\n    for (const file of files) {\n      try {\n        const sourcePath = path.resolve(this.projectRoot, file);\n        const targetPath = path.join(backupPath, file);\n        \n        await fs.ensureDir(path.dirname(targetPath));\n        await fs.copy(sourcePath, targetPath);\n      } catch (error) {\n        console.warn(`Could not backup file ${file}:`, error);\n      }\n    }\n\n    // Save backup metadata\n    await fs.writeJSON(path.join(backupPath, 'backup-info.json'), backupInfo, { spaces: 2 });\n\n    return backupId;\n  }\n\n  async restoreBackup(backupId: string): Promise<void> {\n    const backupPath = path.join(this.backupDir, backupId);\n    const backupInfoPath = path.join(backupPath, 'backup-info.json');\n    \n    if (!await fs.pathExists(backupInfoPath)) {\n      throw new Error(`Backup ${backupId} not found`);\n    }\n\n    const backupInfo: BackupInfo = await fs.readJSON(backupInfoPath);\n\n    for (const file of backupInfo.files) {\n      try {\n        const sourcePath = path.join(backupPath, file);\n        const targetPath = path.resolve(this.projectRoot, file);\n        \n        if (await fs.pathExists(sourcePath)) {\n          await fs.ensureDir(path.dirname(targetPath));\n          await fs.copy(sourcePath, targetPath);\n        }\n      } catch (error) {\n        console.warn(`Could not restore file ${file}:`, error);\n      }\n    }\n  }\n\n  async listBackups(): Promise<BackupInfo[]> {\n    const backups: BackupInfo[] = [];\n    \n    if (!await fs.pathExists(this.backupDir)) {\n      return backups;\n    }\n\n    const backupDirs = await fs.readdir(this.backupDir);\n    \n    for (const dir of backupDirs) {\n      try {\n        const backupInfoPath = path.join(this.backupDir, dir, 'backup-info.json');\n        if (await fs.pathExists(backupInfoPath)) {\n          const backupInfo = await fs.readJSON(backupInfoPath);\n          // Convert timestamp string back to Date object\n          if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {\n            backupInfo.timestamp = new Date(backupInfo.timestamp);\n          }\n          backups.push(backupInfo);\n        }\n      } catch (error) {\n        console.warn(`Could not read backup info for ${dir}:`, error);\n      }\n    }\n\n    return backups.sort((a, b) => {\n      // Handle case where timestamp might be missing or invalid\n      const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;\n      const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;\n      return timeB - timeA;\n    });\n  }\n\n  private isTextFile(filePath: string): boolean {\n    const textExtensions = [\n      '.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.txt', '.css', '.scss',\n      '.html', '.xml', '.yml', '.yaml', '.env', '.gitignore', '.eslintrc',\n      '.prettierrc', '.config', '.lock'\n    ];\n    \n    const ext = path.extname(filePath).toLowerCase();\n    return textExtensions.includes(ext) || !ext;\n  }\n\n  private shouldSkipPath(pathName: string): boolean {\n    const skipPatterns = [\n      'node_modules',\n      '.git',\n      '.next',\n      'dist',\n      'build',\n      '.self-improving-backups',\n      '.env.local',\n      '.DS_Store',\n      'Thumbs.db'\n    ];\n    \n    return skipPatterns.some(pattern => pathName.includes(pattern));\n  }\n\n  getProjectRoot(): string {\n    return this.projectRoot;\n  }\n}\n\nexport const fileSystem = new FileSystemManager();\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAmBO,MAAM;IACH,YAAoB;IACpB,UAAkB;IAE1B,YAAY,WAAoB,CAAE;QAChC,IAAI,CAAC,WAAW,GAAG,eAAe,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QAC7C,IAAI,CAAC,eAAe;IACtB;IAEA,MAAc,kBAAiC;QAC7C,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;IACnC;IAEA,MAAM,SAAS,QAAgB,EAAmB;QAChD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,OAAO,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,UAAU;IACrC;IAEA,MAAM,UAAU,QAAgB,EAAE,OAAe,EAAiB;QAChE,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU,SAAS;IACxC;IAEA,MAAM,WAAW,QAAgB,EAAoB;QACnD,IAAI;YACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAChD,MAAM,6IAAA,CAAA,UAAE,CAAC,MAAM,CAAC;YAChB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,YAAY,QAAgB,EAAqB;QACrD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,QAAQ,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC;QAE5B,MAAM,OAAiB;YACrB,MAAM,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;YACpB,MAAM;YACN,MAAM,MAAM,WAAW,KAAK,cAAc;YAC1C,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,KAAK;QAC3B;QAEA,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACrD,IAAI;gBACF,KAAK,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;YAC/D;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAE,EAAE,YAAqB,KAAK,EAAuB;QACzF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,QAAQ,MAAM,6IAAA,CAAA,UAAE,CAAC,OAAO,CAAC;QAC/B,MAAM,YAAwB,EAAE;QAEhC,KAAK,MAAM,QAAQ,MAAO;YACxB,kDAAkD;YAClD,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO;YAE/B,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;YACpC,IAAI;gBACF,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;gBACpC,UAAU,IAAI,CAAC;gBAEf,IAAI,aAAa,KAAK,IAAI,KAAK,aAAa;oBAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU;oBACpD,UAAU,IAAI,IAAI;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;YACtD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,aAAa,KAAe,EAAE,WAAmB,EAAmB;QACxE,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAClF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE7C,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC;QAEnB,MAAM,aAAyB;YAC7B,IAAI;YACJ,WAAW,IAAI;YACf,OAAO;YACP,aAAa;QACf;QAEA,iCAAiC;QACjC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;gBAClD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;gBAEzC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC,YAAY;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC,EAAE;YACjD;QACF;QAEA,uBAAuB;QACvB,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY,qBAAqB,YAAY;YAAE,QAAQ;QAAE;QAEtF,OAAO;IACT;IAEA,MAAM,cAAc,QAAgB,EAAiB;QACnD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7C,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;QAE7C,IAAI,CAAC,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;YACxC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,SAAS,UAAU,CAAC;QAChD;QAEA,MAAM,aAAyB,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;QAEjD,KAAK,MAAM,QAAQ,WAAW,KAAK,CAAE;YACnC,IAAI;gBACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;gBACzC,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;gBAElD,IAAI,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;oBACnC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC,YAAY;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,EAAE;YAClD;QACF;IACF;IAEA,MAAM,cAAqC;QACzC,MAAM,UAAwB,EAAE;QAEhC,IAAI,CAAC,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YACxC,OAAO;QACT;QAEA,MAAM,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS;QAElD,KAAK,MAAM,OAAO,WAAY;YAC5B,IAAI;gBACF,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK;gBACtD,IAAI,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;oBACvC,MAAM,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;oBACrC,+CAA+C;oBAC/C,IAAI,WAAW,SAAS,IAAI,OAAO,WAAW,SAAS,KAAK,UAAU;wBACpE,WAAW,SAAS,GAAG,IAAI,KAAK,WAAW,SAAS;oBACtD;oBACA,QAAQ,IAAI,CAAC;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC,EAAE;YACzD;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG;YACtB,0DAA0D;YAC1D,MAAM,QAAQ,EAAE,SAAS,YAAY,OAAO,EAAE,SAAS,CAAC,OAAO,KAAK;YACpE,MAAM,QAAQ,EAAE,SAAS,YAAY,OAAO,EAAE,SAAS,CAAC,OAAO,KAAK;YACpE,OAAO,QAAQ;QACjB;IACF;IAEQ,WAAW,QAAgB,EAAW;QAC5C,MAAM,iBAAiB;YACrB;YAAO;YAAQ;YAAO;YAAQ;YAAS;YAAO;YAAQ;YAAQ;YAC9D;YAAS;YAAQ;YAAQ;YAAS;YAAQ;YAAc;YACxD;YAAe;YAAW;SAC3B;QAED,MAAM,MAAM,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;QAC9C,OAAO,eAAe,QAAQ,CAAC,QAAQ,CAAC;IAC1C;IAEQ,eAAe,QAAgB,EAAW;QAChD,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,SAAS,QAAQ,CAAC;IACxD;IAEA,iBAAyB;QACvB,OAAO,IAAI,CAAC,WAAW;IACzB;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/ai-client.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nexport interface CodeAnalysisResult {\n  issues: CodeIssue[];\n  suggestions: CodeSuggestion[];\n  metrics: CodeMetrics;\n}\n\nexport interface CodeIssue {\n  type: 'error' | 'warning' | 'info';\n  message: string;\n  file: string;\n  line?: number;\n  column?: number;\n  severity: number; // 1-10\n}\n\nexport interface CodeSuggestion {\n  id: string;\n  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';\n  title: string;\n  description: string;\n  file: string;\n  originalCode: string;\n  suggestedCode: string;\n  confidence: number; // 0-1\n  impact: 'low' | 'medium' | 'high';\n  estimatedBenefit: string;\n}\n\nexport interface CodeMetrics {\n  linesOfCode: number;\n  complexity: number;\n  maintainability: number;\n  testCoverage?: number;\n  performance: number;\n}\n\nexport class AIClient {\n  private client: OpenAI;\n  private model: string;\n\n  constructor() {\n    if (!process.env.OPENROUTER_API_KEY) {\n      throw new Error('OPENROUTER_API_KEY environment variable is required');\n    }\n\n    this.client = new OpenAI({\n      baseURL: 'https://openrouter.ai/api/v1',\n      apiKey: process.env.OPENROUTER_API_KEY,\n      defaultHeaders: {\n        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n        'X-Title': process.env.NEXT_PUBLIC_APP_NAME || 'Self-Improving App',\n      },\n    });\n\n    // Default to GPT-4.1 Nano as specified\n    this.model = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-nano';\n  }\n\n  async analyzeCode(code: string, fileName: string): Promise<CodeAnalysisResult> {\n    const prompt = this.createAnalysisPrompt(code, fileName);\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert code analyzer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.2,\n        max_tokens: 3000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        console.warn('No response content from AI model');\n        return this.createEmptyAnalysis();\n      }\n\n      const result = this.parseAnalysisResponse(content, fileName);\n\n      // If parsing failed, create a basic analysis with some default metrics\n      if (result.issues.length === 0 && result.suggestions.length === 0 && result.metrics.linesOfCode === 0) {\n        return this.createBasicAnalysis(code, fileName);\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error analyzing code:', error);\n      return this.createBasicAnalysis(code, fileName);\n    }\n  }\n\n  async generateImprovement(\n    code: string,\n    fileName: string,\n    improvementType: string,\n    context?: string\n  ): Promise<CodeSuggestion | null> {\n    const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert software engineer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.3,\n        max_tokens: 2000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        console.warn('No response content from AI model for improvement generation');\n        return null;\n      }\n\n      const result = this.parseImprovementResponse(content, fileName, improvementType);\n\n      // If parsing failed but we have content, try to create a basic suggestion\n      if (!result && content.length > 10) {\n        console.warn('Failed to parse improvement response, creating fallback suggestion');\n        return this.createFallbackSuggestion(fileName, improvementType, content);\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error generating improvement:', error);\n      return null;\n    }\n  }\n\n  async generateNewFeature(\n    description: string,\n    existingCode: string,\n    fileName: string\n  ): Promise<CodeSuggestion | null> {\n    const prompt = `\nGenerate a new feature based on this description: \"${description}\"\n\nExisting code context:\n\\`\\`\\`${this.getFileExtension(fileName)}\n${existingCode}\n\\`\\`\\`\n\nPlease provide:\n1. A clear implementation plan\n2. The new code to add\n3. Any modifications needed to existing code\n4. Potential impacts and considerations\n\nReturn your response as JSON with this structure:\n{\n  \"title\": \"Feature title\",\n  \"description\": \"Detailed description\",\n  \"originalCode\": \"existing code that needs modification\",\n  \"suggestedCode\": \"new/modified code\",\n  \"confidence\": 0.8,\n  \"impact\": \"medium\",\n  \"estimatedBenefit\": \"description of benefits\"\n}\n`;\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert software engineer specializing in feature development.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.5,\n        max_tokens: 2000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        return null;\n      }\n\n      return this.parseImprovementResponse(content, fileName, 'feature');\n    } catch (error) {\n      console.error('Error generating new feature:', error);\n      return null;\n    }\n  }\n\n  private createAnalysisPrompt(code: string, fileName: string): string {\n    return `\nAnalyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:\n\nFile: ${fileName}\n\\`\\`\\`${this.getFileExtension(fileName)}\n${code}\n\\`\\`\\`\n\nIMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text before or after the JSON.\n\nRequired JSON structure:\n{\n  \"issues\": [\n    {\n      \"type\": \"error\",\n      \"message\": \"Issue description\",\n      \"line\": 10,\n      \"severity\": 5\n    }\n  ],\n  \"suggestions\": [\n    {\n      \"type\": \"performance\",\n      \"title\": \"Suggestion title\",\n      \"description\": \"Detailed description\",\n      \"originalCode\": \"code to replace\",\n      \"suggestedCode\": \"replacement code\",\n      \"confidence\": 0.8,\n      \"impact\": \"medium\",\n      \"estimatedBenefit\": \"Description of benefits\"\n    }\n  ],\n  \"metrics\": {\n    \"linesOfCode\": 50,\n    \"complexity\": 3,\n    \"maintainability\": 8,\n    \"performance\": 7\n  }\n}\n\nEnsure all string values are properly quoted and the JSON is valid.`;\n  }\n\n  private createImprovementPrompt(\n    code: string,\n    fileName: string,\n    improvementType: string,\n    context?: string\n  ): string {\n    return `\nGenerate a ${improvementType} improvement for this code:\n\nFile: ${fileName}\n${context ? `Context: ${context}` : ''}\n\n\\`\\`\\`${this.getFileExtension(fileName)}\n${code}\n\\`\\`\\`\n\nIMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text.\n\nFocus on ${improvementType} improvements and return this exact JSON structure:\n{\n  \"title\": \"Improvement title\",\n  \"description\": \"Detailed description of the improvement\",\n  \"originalCode\": \"Exact code to be replaced\",\n  \"suggestedCode\": \"Improved replacement code\",\n  \"confidence\": 0.8,\n  \"impact\": \"medium\",\n  \"estimatedBenefit\": \"Description of expected benefits\"\n}\n\nEnsure all strings are properly quoted and the JSON is valid.`;\n  }\n\n  private parseAnalysisResponse(content: string, fileName: string): CodeAnalysisResult {\n    try {\n      // GPT-4.1 Nano returns clean JSON, so try direct parsing first\n      const trimmedContent = content.trim();\n      let parsed: any;\n\n      try {\n        // Direct parsing - this should work for GPT-4.1 Nano\n        parsed = JSON.parse(trimmedContent);\n        console.log('Direct JSON parsing successful');\n      } catch (directParseError) {\n        console.log('Direct JSON parsing failed, trying extraction methods...');\n        console.log('Parse error:', directParseError);\n\n        // If direct parsing fails, try to extract JSON from response\n        let jsonStr = this.extractJSON(content);\n        if (!jsonStr) {\n          console.warn('No valid JSON found in AI response, using fallback analysis');\n          return this.createBasicAnalysis(content, fileName);\n        }\n\n        try {\n          // Try parsing the extracted JSON without cleaning first\n          parsed = JSON.parse(jsonStr);\n          console.log('Extracted JSON parsing successful');\n        } catch (extractedParseError) {\n          console.log('Extracted JSON parsing failed, trying cleanup...');\n          console.log('Extracted parse error:', extractedParseError);\n          console.log('JSON string before cleanup:', jsonStr.substring(0, 100) + '...');\n\n          // Only clean if extraction also fails\n          jsonStr = this.cleanupJSON(jsonStr);\n          console.log('JSON string after cleanup:', jsonStr.substring(0, 100) + '...');\n          parsed = JSON.parse(jsonStr);\n          console.log('Cleaned JSON parsing successful');\n        }\n      }\n\n      // Validate and normalize the response structure\n      const normalized = this.normalizeAnalysisResponse(parsed, fileName);\n\n      return normalized;\n    } catch (error) {\n      console.error('Error parsing analysis response:', error);\n      console.error('Raw content preview:', content.substring(0, 500) + '...');\n\n      // Try to extract partial information if full JSON parsing fails\n      const fallback = this.extractPartialAnalysis(content, fileName);\n      if (fallback) {\n        console.log('Using fallback partial analysis');\n        return fallback;\n      }\n\n      console.log('Using basic analysis fallback');\n      return this.createBasicAnalysis(content, fileName);\n    }\n  }\n\n  private extractPartialAnalysis(content: string, fileName: string): CodeAnalysisResult | null {\n    try {\n      // Try to extract individual sections even if the full JSON is malformed\n      const result: CodeAnalysisResult = {\n        issues: [],\n        suggestions: [],\n        metrics: {\n          linesOfCode: 0,\n          complexity: 0,\n          maintainability: 5,\n          performance: 5\n        }\n      };\n\n      // Try to extract issues\n      const issuesMatch = content.match(/\"issues\"\\s*:\\s*\\[([\\s\\S]*?)\\]/);\n      if (issuesMatch) {\n        try {\n          const issuesJson = `[${issuesMatch[1]}]`;\n          const issues = JSON.parse(issuesJson);\n          if (Array.isArray(issues)) {\n            result.issues = issues.map((issue: any) => ({\n              type: issue.type || 'info',\n              message: issue.message || 'Issue detected',\n              file: fileName,\n              line: issue.line,\n              severity: issue.severity || 1\n            }));\n          }\n        } catch (e) {\n          console.warn('Could not parse issues section');\n        }\n      }\n\n      // Try to extract metrics\n      const metricsMatch = content.match(/\"metrics\"\\s*:\\s*\\{([^}]*)\\}/);\n      if (metricsMatch) {\n        try {\n          const metricsJson = `{${metricsMatch[1]}}`;\n          const metrics = JSON.parse(metricsJson);\n          result.metrics = {\n            linesOfCode: metrics.linesOfCode || 0,\n            complexity: metrics.complexity || 0,\n            maintainability: metrics.maintainability || 5,\n            performance: metrics.performance || 5\n          };\n        } catch (e) {\n          console.warn('Could not parse metrics section');\n        }\n      }\n\n      return result;\n    } catch (error) {\n      console.warn('Could not extract partial analysis');\n      return null;\n    }\n  }\n\n  private extractJSON(content: string): string | null {\n    // Try multiple patterns to find JSON\n    const patterns = [\n      /\\{[\\s\\S]*\\}/,  // Basic JSON object\n      /```json\\s*(\\{[\\s\\S]*?\\})\\s*```/,  // JSON in code blocks\n      /```\\s*(\\{[\\s\\S]*?\\})\\s*```/,  // JSON in generic code blocks\n    ];\n\n    for (const pattern of patterns) {\n      const match = content.match(pattern);\n      if (match) {\n        return match[1] || match[0];\n      }\n    }\n\n    return null;\n  }\n\n  private cleanupJSON(jsonStr: string): string {\n    // Remove common formatting issues\n    let cleaned = jsonStr\n      .replace(/,\\s*}/g, '}')  // Remove trailing commas in objects\n      .replace(/,\\s*]/g, ']')  // Remove trailing commas in arrays\n      .replace(/'/g, '\"')      // Replace single quotes with double quotes\n      .replace(/(\\w+):/g, '\"$1\":')  // Quote unquoted keys\n      .replace(/\"\\s*\"/g, '\"\"')  // Fix empty strings\n      .replace(/\\\\'/g, \"'\")    // Fix escaped single quotes\n      .replace(/\\\\\"/g, '\\\\\"')  // Ensure double quotes are properly escaped\n      .replace(/\\n/g, '\\\\n')   // Escape newlines in strings\n      .replace(/\\r/g, '\\\\r')   // Escape carriage returns\n      .replace(/\\t/g, '\\\\t')   // Escape tabs\n      .trim();\n\n    // Fix common issues with property names that got over-quoted\n    cleaned = cleaned.replace(/\"(\\w+)\":/g, '\"$1\":');\n\n    // Fix issues where quotes inside strings break JSON\n    // This is a simple approach - find strings and escape internal quotes\n    cleaned = this.fixQuotesInStrings(cleaned);\n\n    return cleaned;\n  }\n\n  private fixQuotesInStrings(jsonStr: string): string {\n    // This is a simplified approach to fix quotes in string values\n    // It's not perfect but handles most common cases\n    try {\n      // Try to parse as-is first\n      JSON.parse(jsonStr);\n      return jsonStr;\n    } catch (error) {\n      // If parsing fails, try to fix common quote issues\n      let fixed = jsonStr;\n\n      // Replace unescaped quotes in string values (basic heuristic)\n      fixed = fixed.replace(/\"([^\"]*)\"([^\"]*)\"([^\"]*)\":/g, '\"$1\\\\\"$2\\\\\"$3\":');\n      fixed = fixed.replace(/:\\s*\"([^\"]*)\"([^\"]*)\"([^\"]*)\"(?=\\s*[,}])/g, ': \"$1\\\\\"$2\\\\\"$3\"');\n\n      return fixed;\n    }\n  }\n\n  private normalizeAnalysisResponse(parsed: any, fileName: string): CodeAnalysisResult {\n    const result: CodeAnalysisResult = {\n      issues: [],\n      suggestions: [],\n      metrics: {\n        linesOfCode: 0,\n        complexity: 0,\n        maintainability: 0,\n        performance: 0\n      }\n    };\n\n    // Normalize issues\n    if (Array.isArray(parsed.issues)) {\n      result.issues = parsed.issues.map((issue: any) => ({\n        type: issue.type || 'info',\n        message: issue.message || 'No message provided',\n        file: fileName,\n        line: issue.line || undefined,\n        column: issue.column || undefined,\n        severity: issue.severity || 1\n      }));\n    }\n\n    // Normalize suggestions\n    if (Array.isArray(parsed.suggestions)) {\n      result.suggestions = parsed.suggestions.map((suggestion: any) => ({\n        id: this.generateId(),\n        type: suggestion.type || 'refactor',\n        title: suggestion.title || 'Improvement suggestion',\n        description: suggestion.description || 'No description provided',\n        file: fileName,\n        originalCode: suggestion.originalCode || '',\n        suggestedCode: suggestion.suggestedCode || '',\n        confidence: Math.min(Math.max(suggestion.confidence || 0.5, 0), 1),\n        impact: suggestion.impact || 'medium',\n        estimatedBenefit: suggestion.estimatedBenefit || 'Improved code quality'\n      }));\n    }\n\n    // Normalize metrics\n    if (parsed.metrics && typeof parsed.metrics === 'object') {\n      result.metrics = {\n        linesOfCode: Math.max(parsed.metrics.linesOfCode || 0, 0),\n        complexity: Math.max(parsed.metrics.complexity || 0, 0),\n        maintainability: Math.min(Math.max(parsed.metrics.maintainability || 5, 0), 10),\n        performance: Math.min(Math.max(parsed.metrics.performance || 5, 0), 10)\n      };\n    }\n\n    return result;\n  }\n\n  private parseImprovementResponse(\n    content: string,\n    fileName: string,\n    type: string = 'refactor'\n  ): CodeSuggestion | null {\n    try {\n      const trimmedContent = content.trim();\n      let parsed: any;\n\n      try {\n        // Direct parsing for GPT-4.1 Nano clean JSON\n        parsed = JSON.parse(trimmedContent);\n      } catch (directParseError) {\n        console.log('Direct improvement JSON parsing failed, trying extraction...');\n\n        let jsonStr = this.extractJSON(content);\n        if (!jsonStr) {\n          console.warn('No valid JSON found in improvement response');\n          return null;\n        }\n\n        try {\n          parsed = JSON.parse(jsonStr);\n        } catch (extractedParseError) {\n          jsonStr = this.cleanupJSON(jsonStr);\n          parsed = JSON.parse(jsonStr);\n        }\n      }\n\n      // Normalize the improvement response\n      return {\n        id: this.generateId(),\n        type: type as any,\n        file: fileName,\n        title: parsed.title || 'Code Improvement',\n        description: parsed.description || 'No description provided',\n        originalCode: parsed.originalCode || '',\n        suggestedCode: parsed.suggestedCode || '',\n        confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1),\n        impact: parsed.impact || 'medium',\n        estimatedBenefit: parsed.estimatedBenefit || 'Improved code quality'\n      };\n    } catch (error) {\n      console.error('Error parsing improvement response:', error);\n      console.error('Raw content preview:', content.substring(0, 200) + '...');\n      return null;\n    }\n  }\n\n  private createEmptyAnalysis(): CodeAnalysisResult {\n    return {\n      issues: [],\n      suggestions: [],\n      metrics: {\n        linesOfCode: 0,\n        complexity: 0,\n        maintainability: 0,\n        performance: 0\n      }\n    };\n  }\n\n  private createBasicAnalysis(code: string, fileName: string): CodeAnalysisResult {\n    const lines = code.split('\\n').length;\n    const complexity = Math.min(Math.floor(lines / 10), 10);\n\n    return {\n      issues: [\n        {\n          type: 'info',\n          message: 'AI analysis temporarily unavailable - showing basic metrics only',\n          file: fileName,\n          severity: 1\n        }\n      ],\n      suggestions: [],\n      metrics: {\n        linesOfCode: lines,\n        complexity: complexity,\n        maintainability: Math.max(10 - complexity, 1),\n        performance: 5\n      }\n    };\n  }\n\n  private createFallbackSuggestion(fileName: string, improvementType: string, content: string): CodeSuggestion {\n    return {\n      id: this.generateId(),\n      type: improvementType as any,\n      title: `${improvementType.charAt(0).toUpperCase() + improvementType.slice(1)} Improvement`,\n      description: 'AI generated an improvement suggestion but the response format was invalid. Please review the raw suggestion.',\n      file: fileName,\n      originalCode: '// Original code section',\n      suggestedCode: content.substring(0, 500) + (content.length > 500 ? '...' : ''),\n      confidence: 0.3,\n      impact: 'low',\n      estimatedBenefit: 'Manual review required due to parsing issues'\n    };\n  }\n\n  private getFileExtension(fileName: string): string {\n    const ext = fileName.split('.').pop()?.toLowerCase();\n    switch (ext) {\n      case 'ts':\n      case 'tsx':\n        return 'typescript';\n      case 'js':\n      case 'jsx':\n        return 'javascript';\n      case 'css':\n        return 'css';\n      case 'json':\n        return 'json';\n      default:\n        return 'text';\n    }\n  }\n\n  private generateId(): string {\n    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }\n}\n\nexport const aiClient = new AIClient();\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAsCO,MAAM;IACH,OAAe;IACf,MAAc;IAEtB,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,MAAM,GAAG,IAAI,wKAAA,CAAA,UAAM,CAAC;YACvB,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACtC,gBAAgB;gBACd,gBAAgB,6DAAmC;gBACnD,WAAW,0DAAoC;YACjD;QACF;QAEA,uCAAuC;QACvC,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAC/C;IAEA,MAAM,YAAY,IAAY,EAAE,QAAgB,EAA+B;QAC7E,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,MAAM;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,mBAAmB;YACjC;YAEA,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAEnD,uEAAuE;YACvE,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,KAAK,OAAO,WAAW,CAAC,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,WAAW,KAAK,GAAG;gBACrG,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACxC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACxC;IACF;IAEA,MAAM,oBACJ,IAAY,EACZ,QAAgB,EAChB,eAAuB,EACvB,OAAgB,EACgB;QAChC,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC,MAAM,UAAU,iBAAiB;QAE7E,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,MAAM,SAAS,IAAI,CAAC,wBAAwB,CAAC,SAAS,UAAU;YAEhE,0EAA0E;YAC1E,IAAI,CAAC,UAAU,QAAQ,MAAM,GAAG,IAAI;gBAClC,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,wBAAwB,CAAC,UAAU,iBAAiB;YAClE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,mBACJ,WAAmB,EACnB,YAAoB,EACpB,QAAgB,EACgB;QAChC,MAAM,SAAS,CAAC;mDAC+B,EAAE,YAAY;;;MAG3D,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,aAAa;;;;;;;;;;;;;;;;;;;AAmBf,CAAC;QAEG,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,OAAO;YACT;YAEA,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,UAAU;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEQ,qBAAqB,IAAY,EAAE,QAAgB,EAAU;QACnE,OAAO,CAAC;aACC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;;MAEzC,EAAE,SAAS;MACX,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mEAmC4D,CAAC;IAClE;IAEQ,wBACN,IAAY,EACZ,QAAgB,EAChB,eAAuB,EACvB,OAAgB,EACR;QACR,OAAO,CAAC;WACD,EAAE,gBAAgB;;MAEvB,EAAE,SAAS;AACjB,EAAE,UAAU,CAAC,SAAS,EAAE,SAAS,GAAG,GAAG;;MAEjC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,KAAK;;;;;SAKE,EAAE,gBAAgB;;;;;;;;;;;6DAWkC,CAAC;IAC5D;IAEQ,sBAAsB,OAAe,EAAE,QAAgB,EAAsB;QACnF,IAAI;YACF,+DAA+D;YAC/D,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI;YAEJ,IAAI;gBACF,qDAAqD;gBACrD,SAAS,KAAK,KAAK,CAAC;gBACpB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,gBAAgB;gBAE5B,6DAA6D;gBAC7D,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC/B,IAAI,CAAC,SAAS;oBACZ,QAAQ,IAAI,CAAC;oBACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;gBAC3C;gBAEA,IAAI;oBACF,wDAAwD;oBACxD,SAAS,KAAK,KAAK,CAAC;oBACpB,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,qBAAqB;oBAC5B,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,0BAA0B;oBACtC,QAAQ,GAAG,CAAC,+BAA+B,QAAQ,SAAS,CAAC,GAAG,OAAO;oBAEvE,sCAAsC;oBACtC,UAAU,IAAI,CAAC,WAAW,CAAC;oBAC3B,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,SAAS,CAAC,GAAG,OAAO;oBACtE,SAAS,KAAK,KAAK,CAAC;oBACpB,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA,gDAAgD;YAChD,MAAM,aAAa,IAAI,CAAC,yBAAyB,CAAC,QAAQ;YAE1D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAElE,gEAAgE;YAChE,MAAM,WAAW,IAAI,CAAC,sBAAsB,CAAC,SAAS;YACtD,IAAI,UAAU;gBACZ,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAC3C;IACF;IAEQ,uBAAuB,OAAe,EAAE,QAAgB,EAA6B;QAC3F,IAAI;YACF,wEAAwE;YACxE,MAAM,SAA6B;gBACjC,QAAQ,EAAE;gBACV,aAAa,EAAE;gBACf,SAAS;oBACP,aAAa;oBACb,YAAY;oBACZ,iBAAiB;oBACjB,aAAa;gBACf;YACF;YAEA,wBAAwB;YACxB,MAAM,cAAc,QAAQ,KAAK,CAAC;YAClC,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxC,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,OAAO,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,QAAe,CAAC;gCAC1C,MAAM,MAAM,IAAI,IAAI;gCACpB,SAAS,MAAM,OAAO,IAAI;gCAC1B,MAAM;gCACN,MAAM,MAAM,IAAI;gCAChB,UAAU,MAAM,QAAQ,IAAI;4BAC9B,CAAC;oBACH;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,yBAAyB;YACzB,MAAM,eAAe,QAAQ,KAAK,CAAC;YACnC,IAAI,cAAc;gBAChB,IAAI;oBACF,MAAM,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1C,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,OAAO,GAAG;wBACf,aAAa,QAAQ,WAAW,IAAI;wBACpC,YAAY,QAAQ,UAAU,IAAI;wBAClC,iBAAiB,QAAQ,eAAe,IAAI;wBAC5C,aAAa,QAAQ,WAAW,IAAI;oBACtC;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;IACF;IAEQ,YAAY,OAAe,EAAiB;QAClD,qCAAqC;QACrC,MAAM,WAAW;YACf;YACA;YACA;SACD;QAED,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YAC7B;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,OAAe,EAAU;QAC3C,kCAAkC;QAClC,IAAI,UAAU,QACX,OAAO,CAAC,UAAU,KAAM,oCAAoC;SAC5D,OAAO,CAAC,UAAU,KAAM,mCAAmC;SAC3D,OAAO,CAAC,MAAM,KAAU,2CAA2C;SACnE,OAAO,CAAC,WAAW,SAAU,sBAAsB;SACnD,OAAO,CAAC,UAAU,MAAO,oBAAoB;SAC7C,OAAO,CAAC,QAAQ,KAAQ,4BAA4B;SACpD,OAAO,CAAC,QAAQ,OAAQ,4CAA4C;SACpE,OAAO,CAAC,OAAO,OAAS,6BAA6B;SACrD,OAAO,CAAC,OAAO,OAAS,0BAA0B;SAClD,OAAO,CAAC,OAAO,OAAS,cAAc;SACtC,IAAI;QAEP,6DAA6D;QAC7D,UAAU,QAAQ,OAAO,CAAC,aAAa;QAEvC,oDAAoD;QACpD,sEAAsE;QACtE,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAElC,OAAO;IACT;IAEQ,mBAAmB,OAAe,EAAU;QAClD,+DAA+D;QAC/D,iDAAiD;QACjD,IAAI;YACF,2BAA2B;YAC3B,KAAK,KAAK,CAAC;YACX,OAAO;QACT,EAAE,OAAO,OAAO;YACd,mDAAmD;YACnD,IAAI,QAAQ;YAEZ,8DAA8D;YAC9D,QAAQ,MAAM,OAAO,CAAC,+BAA+B;YACrD,QAAQ,MAAM,OAAO,CAAC,6CAA6C;YAEnE,OAAO;QACT;IACF;IAEQ,0BAA0B,MAAW,EAAE,QAAgB,EAAsB;QACnF,MAAM,SAA6B;YACjC,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB;gBACjB,aAAa;YACf;QACF;QAEA,mBAAmB;QACnB,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;YAChC,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAe,CAAC;oBACjD,MAAM,MAAM,IAAI,IAAI;oBACpB,SAAS,MAAM,OAAO,IAAI;oBAC1B,MAAM;oBACN,MAAM,MAAM,IAAI,IAAI;oBACpB,QAAQ,MAAM,MAAM,IAAI;oBACxB,UAAU,MAAM,QAAQ,IAAI;gBAC9B,CAAC;QACH;QAEA,wBAAwB;QACxB,IAAI,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG;YACrC,OAAO,WAAW,GAAG,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,aAAoB,CAAC;oBAChE,IAAI,IAAI,CAAC,UAAU;oBACnB,MAAM,WAAW,IAAI,IAAI;oBACzB,OAAO,WAAW,KAAK,IAAI;oBAC3B,aAAa,WAAW,WAAW,IAAI;oBACvC,MAAM;oBACN,cAAc,WAAW,YAAY,IAAI;oBACzC,eAAe,WAAW,aAAa,IAAI;oBAC3C,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,WAAW,UAAU,IAAI,KAAK,IAAI;oBAChE,QAAQ,WAAW,MAAM,IAAI;oBAC7B,kBAAkB,WAAW,gBAAgB,IAAI;gBACnD,CAAC;QACH;QAEA,oBAAoB;QACpB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;YACxD,OAAO,OAAO,GAAG;gBACf,aAAa,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvD,YAAY,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,UAAU,IAAI,GAAG;gBACrD,iBAAiB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,eAAe,IAAI,GAAG,IAAI;gBAC5E,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,GAAG,IAAI;YACtE;QACF;QAEA,OAAO;IACT;IAEQ,yBACN,OAAe,EACf,QAAgB,EAChB,OAAe,UAAU,EACF;QACvB,IAAI;YACF,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI;YAEJ,IAAI;gBACF,6CAA6C;gBAC7C,SAAS,KAAK,KAAK,CAAC;YACtB,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,GAAG,CAAC;gBAEZ,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC/B,IAAI,CAAC,SAAS;oBACZ,QAAQ,IAAI,CAAC;oBACb,OAAO;gBACT;gBAEA,IAAI;oBACF,SAAS,KAAK,KAAK,CAAC;gBACtB,EAAE,OAAO,qBAAqB;oBAC5B,UAAU,IAAI,CAAC,WAAW,CAAC;oBAC3B,SAAS,KAAK,KAAK,CAAC;gBACtB;YACF;YAEA,qCAAqC;YACrC,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,MAAM;gBACN,OAAO,OAAO,KAAK,IAAI;gBACvB,aAAa,OAAO,WAAW,IAAI;gBACnC,cAAc,OAAO,YAAY,IAAI;gBACrC,eAAe,OAAO,aAAa,IAAI;gBACvC,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,UAAU,IAAI,KAAK,IAAI;gBAC5D,QAAQ,OAAO,MAAM,IAAI;gBACzB,kBAAkB,OAAO,gBAAgB,IAAI;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAClE,OAAO;QACT;IACF;IAEQ,sBAA0C;QAChD,OAAO;YACL,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB;gBACjB,aAAa;YACf;QACF;IACF;IAEQ,oBAAoB,IAAY,EAAE,QAAgB,EAAsB;QAC9E,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM;QACrC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK;QAEpD,OAAO;YACL,QAAQ;gBACN;oBACE,MAAM;oBACN,SAAS;oBACT,MAAM;oBACN,UAAU;gBACZ;aACD;YACD,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB,KAAK,GAAG,CAAC,KAAK,YAAY;gBAC3C,aAAa;YACf;QACF;IACF;IAEQ,yBAAyB,QAAgB,EAAE,eAAuB,EAAE,OAAe,EAAkB;QAC3G,OAAO;YACL,IAAI,IAAI,CAAC,UAAU;YACnB,MAAM;YACN,OAAO,GAAG,gBAAgB,MAAM,CAAC,GAAG,WAAW,KAAK,gBAAgB,KAAK,CAAC,GAAG,YAAY,CAAC;YAC1F,aAAa;YACb,MAAM;YACN,cAAc;YACd,eAAe,QAAQ,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,EAAE;YAC7E,YAAY;YACZ,QAAQ;YACR,kBAAkB;QACpB;IACF;IAEQ,iBAAiB,QAAgB,EAAU;QACjD,MAAM,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QACvC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEQ,aAAqB;QAC3B,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;IACvE;AACF;AAEO,MAAM,WAAW,IAAI", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/analyzer.ts"], "sourcesContent": ["import * as acorn from 'acorn';\nimport { parse as babelParse } from '@babel/parser';\nimport { fileSystem, FileInfo } from './filesystem';\nimport { aiClient, CodeAnalysisResult, CodeSuggestion } from './ai-client';\n\nexport interface ProjectAnalysis {\n  files: FileAnalysis[];\n  overallMetrics: ProjectMetrics;\n  suggestions: CodeSuggestion[];\n  lastAnalyzed: Date;\n}\n\nexport interface FileAnalysis {\n  file: FileInfo;\n  analysis: CodeAnalysisResult;\n  ast?: any;\n  dependencies: string[];\n  exports: string[];\n}\n\nexport interface ProjectMetrics {\n  totalFiles: number;\n  totalLinesOfCode: number;\n  averageComplexity: number;\n  averageMaintainability: number;\n  issueCount: number;\n  suggestionCount: number;\n  testCoverage?: number;\n}\n\nexport class CodeAnalyzer {\n  private analysisCache: Map<string, FileAnalysis> = new Map();\n\n  async analyzeProject(): Promise<ProjectAnalysis> {\n    console.log('Starting project analysis...');\n    \n    const files = await fileSystem.listDirectory('src', true);\n    const codeFiles = files.filter(file => \n      file.type === 'file' && this.isAnalyzableFile(file.name)\n    );\n\n    const fileAnalyses: FileAnalysis[] = [];\n    const allSuggestions: CodeSuggestion[] = [];\n\n    for (const file of codeFiles) {\n      try {\n        const analysis = await this.analyzeFile(file);\n        fileAnalyses.push(analysis);\n        allSuggestions.push(...analysis.analysis.suggestions);\n      } catch (error) {\n        console.error(`Error analyzing file ${file.path}:`, error);\n      }\n    }\n\n    const overallMetrics = this.calculateProjectMetrics(fileAnalyses);\n\n    return {\n      files: fileAnalyses,\n      overallMetrics,\n      suggestions: allSuggestions,\n      lastAnalyzed: new Date()\n    };\n  }\n\n  async analyzeFile(file: FileInfo): Promise<FileAnalysis> {\n    // Check cache first\n    const cacheKey = `${file.path}_${file.lastModified?.getTime()}`;\n    if (this.analysisCache.has(cacheKey)) {\n      return this.analysisCache.get(cacheKey)!;\n    }\n\n    console.log(`Analyzing file: ${file.path}`);\n\n    const content = file.content || await fileSystem.readFile(file.path);\n    \n    // Parse AST for JavaScript/TypeScript files\n    let ast: any = null;\n    let dependencies: string[] = [];\n    let exports: string[] = [];\n\n    if (this.isJavaScriptFile(file.name)) {\n      try {\n        ast = this.parseAST(content, file.name);\n        dependencies = this.extractDependencies(ast);\n        exports = this.extractExports(ast);\n      } catch (error) {\n        console.warn(`Could not parse AST for ${file.path}:`, error);\n        // Continue without AST - the AI analysis will still work\n        ast = null;\n        dependencies = [];\n        exports = [];\n      }\n    }\n\n    // Get AI analysis\n    const analysis = await aiClient.analyzeCode(content, file.path);\n\n    const fileAnalysis: FileAnalysis = {\n      file: { ...file, content },\n      analysis,\n      ast,\n      dependencies,\n      exports\n    };\n\n    // Cache the result\n    this.analysisCache.set(cacheKey, fileAnalysis);\n\n    return fileAnalysis;\n  }\n\n  async generateImprovements(\n    file: FileInfo, \n    improvementTypes: string[] = ['performance', 'refactor', 'style']\n  ): Promise<CodeSuggestion[]> {\n    const content = file.content || await fileSystem.readFile(file.path);\n    const suggestions: CodeSuggestion[] = [];\n\n    for (const type of improvementTypes) {\n      try {\n        const suggestion = await aiClient.generateImprovement(\n          content, \n          file.path, \n          type,\n          `Improve ${type} aspects of this code`\n        );\n        \n        if (suggestion) {\n          suggestions.push(suggestion);\n        }\n      } catch (error) {\n        console.error(`Error generating ${type} improvement for ${file.path}:`, error);\n      }\n    }\n\n    return suggestions;\n  }\n\n  async generateNewFeature(description: string, targetFile?: string): Promise<CodeSuggestion | null> {\n    let contextFile = targetFile;\n    let contextCode = '';\n\n    if (!contextFile) {\n      // Find the most relevant file based on the description\n      const files = await fileSystem.listDirectory('src', true);\n      const mainFiles = files.filter(f => \n        f.name.includes('page.tsx') || \n        f.name.includes('index.tsx') ||\n        f.name.includes('App.tsx')\n      );\n      \n      contextFile = mainFiles[0]?.path || 'src/app/page.tsx';\n    }\n\n    try {\n      contextCode = await fileSystem.readFile(contextFile);\n    } catch (error) {\n      console.warn(`Could not read context file ${contextFile}:`, error);\n    }\n\n    return await aiClient.generateNewFeature(description, contextCode, contextFile);\n  }\n\n  private parseAST(code: string, fileName: string): any {\n    const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');\n    const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');\n\n    if (isTypeScript) {\n      // Use Babel parser for TypeScript files\n      try {\n        return babelParse(code, {\n          sourceType: 'module',\n          allowImportExportEverywhere: true,\n          allowReturnOutsideFunction: true,\n          plugins: [\n            'typescript',\n            ...(isJSX ? ['jsx'] : []),\n            'decorators-legacy',\n            'classProperties',\n            'objectRestSpread',\n            'asyncGenerators',\n            'functionBind',\n            'exportDefaultFrom',\n            'exportNamespaceFrom',\n            'dynamicImport',\n            'nullishCoalescingOperator',\n            'optionalChaining'\n          ]\n        });\n      } catch (error) {\n        throw new Error(`Could not parse TypeScript file ${fileName}: ${error}`);\n      }\n    } else {\n      // Use Acorn for JavaScript files\n      try {\n        return acorn.parse(code, {\n          ecmaVersion: 2022,\n          sourceType: 'module',\n          allowImportExportEverywhere: true,\n          allowReturnOutsideFunction: true,\n        });\n      } catch (error) {\n        // If parsing fails, try with different options\n        try {\n          return acorn.parse(code, {\n            ecmaVersion: 2022,\n            sourceType: 'script',\n          });\n        } catch (secondError) {\n          throw new Error(`Could not parse JavaScript file ${fileName}: ${error}`);\n        }\n      }\n    }\n  }\n\n  private extractDependencies(ast: any): string[] {\n    const dependencies: string[] = [];\n    \n    if (!ast || !ast.body) return dependencies;\n\n    for (const node of ast.body) {\n      if (node.type === 'ImportDeclaration' && node.source?.value) {\n        dependencies.push(node.source.value);\n      }\n    }\n\n    return [...new Set(dependencies)]; // Remove duplicates\n  }\n\n  private extractExports(ast: any): string[] {\n    const exports: string[] = [];\n    \n    if (!ast || !ast.body) return exports;\n\n    for (const node of ast.body) {\n      if (node.type === 'ExportNamedDeclaration') {\n        if (node.declaration) {\n          if (node.declaration.type === 'FunctionDeclaration' && node.declaration.id) {\n            exports.push(node.declaration.id.name);\n          } else if (node.declaration.type === 'VariableDeclaration') {\n            for (const declarator of node.declaration.declarations) {\n              if (declarator.id?.name) {\n                exports.push(declarator.id.name);\n              }\n            }\n          }\n        }\n        \n        if (node.specifiers) {\n          for (const specifier of node.specifiers) {\n            if (specifier.exported?.name) {\n              exports.push(specifier.exported.name);\n            }\n          }\n        }\n      } else if (node.type === 'ExportDefaultDeclaration') {\n        exports.push('default');\n      }\n    }\n\n    return [...new Set(exports)]; // Remove duplicates\n  }\n\n  private calculateProjectMetrics(fileAnalyses: FileAnalysis[]): ProjectMetrics {\n    const totalFiles = fileAnalyses.length;\n    let totalLinesOfCode = 0;\n    let totalComplexity = 0;\n    let totalMaintainability = 0;\n    let issueCount = 0;\n    let suggestionCount = 0;\n\n    for (const analysis of fileAnalyses) {\n      const metrics = analysis.analysis.metrics;\n      totalLinesOfCode += metrics.linesOfCode;\n      totalComplexity += metrics.complexity;\n      totalMaintainability += metrics.maintainability;\n      issueCount += analysis.analysis.issues.length;\n      suggestionCount += analysis.analysis.suggestions.length;\n    }\n\n    return {\n      totalFiles,\n      totalLinesOfCode,\n      averageComplexity: totalFiles > 0 ? totalComplexity / totalFiles : 0,\n      averageMaintainability: totalFiles > 0 ? totalMaintainability / totalFiles : 0,\n      issueCount,\n      suggestionCount\n    };\n  }\n\n  private isAnalyzableFile(fileName: string): boolean {\n    const analyzableExtensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.json'];\n    return analyzableExtensions.some(ext => fileName.endsWith(ext));\n  }\n\n  private isJavaScriptFile(fileName: string): boolean {\n    return fileName.endsWith('.js') || \n           fileName.endsWith('.jsx') || \n           fileName.endsWith('.ts') || \n           fileName.endsWith('.tsx');\n  }\n\n  clearCache(): void {\n    this.analysisCache.clear();\n  }\n\n  getCacheSize(): number {\n    return this.analysisCache.size;\n  }\n}\n\nexport const codeAnalyzer = new CodeAnalyzer();\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AA2BO,MAAM;IACH,gBAA2C,IAAI,MAAM;IAE7D,MAAM,iBAA2C;QAC/C,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,OAAO;QACpD,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAC7B,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAGzD,MAAM,eAA+B,EAAE;QACvC,MAAM,iBAAmC,EAAE;QAE3C,KAAK,MAAM,QAAQ,UAAW;YAC5B,IAAI;gBACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;gBACxC,aAAa,IAAI,CAAC;gBAClB,eAAe,IAAI,IAAI,SAAS,QAAQ,CAAC,WAAW;YACtD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YACtD;QACF;QAEA,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;QAEpD,OAAO;YACL,OAAO;YACP;YACA,aAAa;YACb,cAAc,IAAI;QACpB;IACF;IAEA,MAAM,YAAY,IAAc,EAAyB;QACvD,oBAAoB;QACpB,MAAM,WAAW,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE,WAAW;QAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACpC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAChC;QAEA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QAE1C,MAAM,UAAU,KAAK,OAAO,IAAI,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK,IAAI;QAEnE,4CAA4C;QAC5C,IAAI,MAAW;QACf,IAAI,eAAyB,EAAE;QAC/B,IAAI,UAAoB,EAAE;QAE1B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,GAAG;YACpC,IAAI;gBACF,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,IAAI;gBACtC,eAAe,IAAI,CAAC,mBAAmB,CAAC;gBACxC,UAAU,IAAI,CAAC,cAAc,CAAC;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;gBACtD,yDAAyD;gBACzD,MAAM;gBACN,eAAe,EAAE;gBACjB,UAAU,EAAE;YACd;QACF;QAEA,kBAAkB;QAClB,MAAM,WAAW,MAAM,4HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,SAAS,KAAK,IAAI;QAE9D,MAAM,eAA6B;YACjC,MAAM;gBAAE,GAAG,IAAI;gBAAE;YAAQ;YACzB;YACA;YACA;YACA;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU;QAEjC,OAAO;IACT;IAEA,MAAM,qBACJ,IAAc,EACd,mBAA6B;QAAC;QAAe;QAAY;KAAQ,EACtC;QAC3B,MAAM,UAAU,KAAK,OAAO,IAAI,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK,IAAI;QACnE,MAAM,cAAgC,EAAE;QAExC,KAAK,MAAM,QAAQ,iBAAkB;YACnC,IAAI;gBACF,MAAM,aAAa,MAAM,4HAAA,CAAA,WAAQ,CAAC,mBAAmB,CACnD,SACA,KAAK,IAAI,EACT,MACA,CAAC,QAAQ,EAAE,KAAK,qBAAqB,CAAC;gBAGxC,IAAI,YAAY;oBACd,YAAY,IAAI,CAAC;gBACnB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,KAAK,iBAAiB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YAC1E;QACF;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,WAAmB,EAAE,UAAmB,EAAkC;QACjG,IAAI,cAAc;QAClB,IAAI,cAAc;QAElB,IAAI,CAAC,aAAa;YAChB,uDAAuD;YACvD,MAAM,QAAQ,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,OAAO;YACpD,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,IAC7B,EAAE,IAAI,CAAC,QAAQ,CAAC,eAChB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAChB,EAAE,IAAI,CAAC,QAAQ,CAAC;YAGlB,cAAc,SAAS,CAAC,EAAE,EAAE,QAAQ;QACtC;QAEA,IAAI;YACF,cAAc,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;QAC9D;QAEA,OAAO,MAAM,4HAAA,CAAA,WAAQ,CAAC,kBAAkB,CAAC,aAAa,aAAa;IACrE;IAEQ,SAAS,IAAY,EAAE,QAAgB,EAAO;QACpD,MAAM,eAAe,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC;QACnE,MAAM,QAAQ,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;QAE7D,IAAI,cAAc;YAChB,wCAAwC;YACxC,IAAI;gBACF,OAAO,CAAA,GAAA,mJAAA,CAAA,QAAU,AAAD,EAAE,MAAM;oBACtB,YAAY;oBACZ,6BAA6B;oBAC7B,4BAA4B;oBAC5B,SAAS;wBACP;2BACI,QAAQ;4BAAC;yBAAM,GAAG,EAAE;wBACxB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,EAAE,EAAE,OAAO;YACzE;QACF,OAAO;YACL,iCAAiC;YACjC,IAAI;gBACF,OAAO,CAAA,GAAA,yIAAA,CAAA,QAAW,AAAD,EAAE,MAAM;oBACvB,aAAa;oBACb,YAAY;oBACZ,6BAA6B;oBAC7B,4BAA4B;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,+CAA+C;gBAC/C,IAAI;oBACF,OAAO,CAAA,GAAA,yIAAA,CAAA,QAAW,AAAD,EAAE,MAAM;wBACvB,aAAa;wBACb,YAAY;oBACd;gBACF,EAAE,OAAO,aAAa;oBACpB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,EAAE,EAAE,OAAO;gBACzE;YACF;QACF;IACF;IAEQ,oBAAoB,GAAQ,EAAY;QAC9C,MAAM,eAAyB,EAAE;QAEjC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,OAAO;QAE9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAE;YAC3B,IAAI,KAAK,IAAI,KAAK,uBAAuB,KAAK,MAAM,EAAE,OAAO;gBAC3D,aAAa,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK;YACrC;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAc,EAAE,oBAAoB;IACzD;IAEQ,eAAe,GAAQ,EAAY;QACzC,MAAM,UAAoB,EAAE;QAE5B,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,OAAO;QAE9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAE;YAC3B,IAAI,KAAK,IAAI,KAAK,0BAA0B;gBAC1C,IAAI,KAAK,WAAW,EAAE;oBACpB,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,yBAAyB,KAAK,WAAW,CAAC,EAAE,EAAE;wBAC1E,QAAQ,IAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI;oBACvC,OAAO,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,uBAAuB;wBAC1D,KAAK,MAAM,cAAc,KAAK,WAAW,CAAC,YAAY,CAAE;4BACtD,IAAI,WAAW,EAAE,EAAE,MAAM;gCACvB,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI;4BACjC;wBACF;oBACF;gBACF;gBAEA,IAAI,KAAK,UAAU,EAAE;oBACnB,KAAK,MAAM,aAAa,KAAK,UAAU,CAAE;wBACvC,IAAI,UAAU,QAAQ,EAAE,MAAM;4BAC5B,QAAQ,IAAI,CAAC,UAAU,QAAQ,CAAC,IAAI;wBACtC;oBACF;gBACF;YACF,OAAO,IAAI,KAAK,IAAI,KAAK,4BAA4B;gBACnD,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAS,EAAE,oBAAoB;IACpD;IAEQ,wBAAwB,YAA4B,EAAkB;QAC5E,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,mBAAmB;QACvB,IAAI,kBAAkB;QACtB,IAAI,uBAAuB;QAC3B,IAAI,aAAa;QACjB,IAAI,kBAAkB;QAEtB,KAAK,MAAM,YAAY,aAAc;YACnC,MAAM,UAAU,SAAS,QAAQ,CAAC,OAAO;YACzC,oBAAoB,QAAQ,WAAW;YACvC,mBAAmB,QAAQ,UAAU;YACrC,wBAAwB,QAAQ,eAAe;YAC/C,cAAc,SAAS,QAAQ,CAAC,MAAM,CAAC,MAAM;YAC7C,mBAAmB,SAAS,QAAQ,CAAC,WAAW,CAAC,MAAM;QACzD;QAEA,OAAO;YACL;YACA;YACA,mBAAmB,aAAa,IAAI,kBAAkB,aAAa;YACnE,wBAAwB,aAAa,IAAI,uBAAuB,aAAa;YAC7E;YACA;QACF;IACF;IAEQ,iBAAiB,QAAgB,EAAW;QAClD,MAAM,uBAAuB;YAAC;YAAO;YAAQ;YAAO;YAAQ;YAAQ;SAAQ;QAC5E,OAAO,qBAAqB,IAAI,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC;IAC5D;IAEQ,iBAAiB,QAAgB,EAAW;QAClD,OAAO,SAAS,QAAQ,CAAC,UAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,UAClB,SAAS,QAAQ,CAAC;IAC3B;IAEA,aAAmB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA,eAAuB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;AACF;AAEO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/app/api/analyze/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { codeAnalyzer } from '@/lib/analyzer';\nimport { fileSystem } from '@/lib/filesystem';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const file = searchParams.get('file');\n    const full = searchParams.get('full') === 'true';\n\n    if (file) {\n      // Analyze specific file\n      const fileInfo = await fileSystem.getFileInfo(file);\n      const analysis = await codeAnalyzer.analyzeFile(fileInfo);\n      \n      return NextResponse.json({\n        success: true,\n        data: analysis\n      });\n    } else if (full) {\n      // Full project analysis\n      const analysis = await codeAnalyzer.analyzeProject();\n      \n      return NextResponse.json({\n        success: true,\n        data: analysis\n      });\n    } else {\n      // Quick project overview\n      const files = await fileSystem.listDirectory('src', true);\n      const codeFiles = files.filter(f => \n        f.type === 'file' && \n        (f.name.endsWith('.ts') || f.name.endsWith('.tsx') || \n         f.name.endsWith('.js') || f.name.endsWith('.jsx'))\n      );\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          totalFiles: codeFiles.length,\n          files: codeFiles.map(f => ({\n            name: f.name,\n            path: f.path,\n            size: f.size,\n            lastModified: f.lastModified\n          }))\n        }\n      });\n    }\n  } catch (error) {\n    console.error('Analysis error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, data } = body;\n\n    switch (action) {\n      case 'generate-improvements':\n        const { file, types } = data;\n        const fileInfo = await fileSystem.getFileInfo(file);\n        const improvements = await codeAnalyzer.generateImprovements(fileInfo, types);\n        \n        return NextResponse.json({\n          success: true,\n          data: improvements\n        });\n\n      case 'generate-feature':\n        const { description, targetFile } = data;\n        const feature = await codeAnalyzer.generateNewFeature(description, targetFile);\n        \n        return NextResponse.json({\n          success: true,\n          data: feature\n        });\n\n      case 'clear-cache':\n        codeAnalyzer.clearCache();\n        \n        return NextResponse.json({\n          success: true,\n          message: 'Analysis cache cleared'\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Unknown action'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Analysis POST error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,OAAO,aAAa,GAAG,CAAC,YAAY;QAE1C,IAAI,MAAM;YACR,wBAAwB;YACxB,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,WAAW,CAAC;YAC9C,MAAM,WAAW,MAAM,wHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;YAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF,OAAO,IAAI,MAAM;YACf,wBAAwB;YACxB,MAAM,WAAW,MAAM,wHAAA,CAAA,eAAY,CAAC,cAAc;YAElD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF,OAAO;YACL,yBAAyB;YACzB,MAAM,QAAQ,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,OAAO;YACpD,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,IAC7B,EAAE,IAAI,KAAK,UACX,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,WAC1C,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;YAGpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,YAAY,UAAU,MAAM;oBAC5B,OAAO,UAAU,GAAG,CAAC,CAAA,IAAK,CAAC;4BACzB,MAAM,EAAE,IAAI;4BACZ,MAAM,EAAE,IAAI;4BACZ,MAAM,EAAE,IAAI;4BACZ,cAAc,EAAE,YAAY;wBAC9B,CAAC;gBACH;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;gBACxB,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,WAAW,CAAC;gBAC9C,MAAM,eAAe,MAAM,wHAAA,CAAA,eAAY,CAAC,oBAAoB,CAAC,UAAU;gBAEvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG;gBACpC,MAAM,UAAU,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,aAAa;gBAEnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,wHAAA,CAAA,eAAY,CAAC,UAAU;gBAEvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;gBACX;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}