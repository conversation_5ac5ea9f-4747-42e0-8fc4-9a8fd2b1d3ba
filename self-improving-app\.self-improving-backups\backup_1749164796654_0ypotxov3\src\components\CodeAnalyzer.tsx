'use client';

import { useState, useEffect } from 'react';
import { 
  FileText, 
  Search, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  TrendingUp,
  Code,
  Folder,
  FolderOpen
} from 'lucide-react';
import <PERSON>Viewer from './CodeViewer';

interface FileInfo {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified?: string;
  content?: string;
}

interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  file: string;
  line?: number;
  severity: number;
}

interface CodeSuggestion {
  id: string;
  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';
  title: string;
  description: string;
  file: string;
  originalCode: string;
  suggestedCode: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
}

interface FileAnalysis {
  file: FileInfo;
  analysis: {
    issues: CodeIssue[];
    suggestions: CodeSuggestion[];
    metrics: {
      linesOfCode: number;
      complexity: number;
      maintainability: number;
      performance: number;
    };
  };
}

export default function CodeAnalyzer() {
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);
  const [fileAnalysis, setFileAnalysis] = useState<FileAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['src']));

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/files?action=list&recursive=true');
      const data = await response.json();

      if (data.success) {
        setFiles(data.data);
      } else {
        setError(data.error || 'Failed to load files');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  const analyzeFile = async (file: FileInfo) => {
    try {
      setAnalyzing(true);
      setError(null);

      const response = await fetch(`/api/analyze?file=${encodeURIComponent(file.path)}`);
      const data = await response.json();

      if (data.success) {
        setFileAnalysis(data.data);
      } else {
        setError(data.error || 'Failed to analyze file');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to analyze file');
    } finally {
      setAnalyzing(false);
    }
  };

  const handleFileSelect = async (file: FileInfo) => {
    if (file.type === 'directory') {
      toggleFolder(file.path);
      return;
    }

    setSelectedFile(file);
    setFileAnalysis(null);

    // Load file content if not already loaded
    if (!file.content) {
      try {
        const response = await fetch(`/api/files?action=read&path=${encodeURIComponent(file.path)}`);
        const data = await response.json();
        
        if (data.success) {
          file.content = data.data.content;
        }
      } catch (err) {
        console.error('Failed to load file content:', err);
      }
    }

    // Auto-analyze if it's a code file
    if (isCodeFile(file.name)) {
      await analyzeFile(file);
    }
  };

  const toggleFolder = (folderPath: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderPath)) {
      newExpanded.delete(folderPath);
    } else {
      newExpanded.add(folderPath);
    }
    setExpandedFolders(newExpanded);
  };

  const isCodeFile = (fileName: string): boolean => {
    const codeExtensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.json'];
    return codeExtensions.some(ext => fileName.endsWith(ext));
  };

  const getFileIcon = (file: FileInfo) => {
    if (file.type === 'directory') {
      return expandedFolders.has(file.path) ? FolderOpen : Folder;
    }
    return FileText;
  };

  const getIssueIcon = (type: string) => {
    switch (type) {
      case 'error':
        return AlertTriangle;
      case 'warning':
        return AlertTriangle;
      case 'info':
        return Info;
      default:
        return Info;
    }
  };

  const getIssueColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'text-red-600';
      case 'warning':
        return 'text-yellow-600';
      case 'info':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const renderFileTree = (fileList: FileInfo[], level: number = 0) => {
    const grouped = fileList.reduce((acc, file) => {
      const pathParts = file.path.split('/');
      const currentLevel = pathParts[level];
      
      if (!currentLevel) return acc;
      
      if (!acc[currentLevel]) {
        acc[currentLevel] = [];
      }
      acc[currentLevel].push(file);
      return acc;
    }, {} as Record<string, FileInfo[]>);

    return Object.entries(grouped).map(([name, files]) => {
      const isDirectory = files.some(f => f.path.split('/').length > level + 1);
      const currentPath = files[0].path.split('/').slice(0, level + 1).join('/');
      
      if (isDirectory) {
        const isExpanded = expandedFolders.has(currentPath);
        return (
          <div key={currentPath}>
            <div
              className="flex items-center space-x-2 py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded"
              style={{ paddingLeft: `${level * 16 + 8}px` }}
              onClick={() => toggleFolder(currentPath)}
            >
              {isExpanded ? (
                <FolderOpen className="h-4 w-4 text-blue-600" />
              ) : (
                <Folder className="h-4 w-4 text-blue-600" />
              )}
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {name}
              </span>
            </div>
            {isExpanded && renderFileTree(files, level + 1)}
          </div>
        );
      } else {
        const file = files[0];
        return (
          <div
            key={file.path}
            className={`flex items-center space-x-2 py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded ${
              selectedFile?.path === file.path ? 'bg-blue-50 dark:bg-blue-900' : ''
            }`}
            style={{ paddingLeft: `${level * 16 + 8}px` }}
            onClick={() => handleFileSelect(file)}
          >
            <FileText className="h-4 w-4 text-gray-600" />
            <span className="text-sm text-gray-900 dark:text-white">
              {name}
            </span>
            {file.size && (
              <span className="text-xs text-gray-500 ml-auto">
                {Math.round(file.size / 1024)}KB
              </span>
            )}
          </div>
        );
      }
    });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      {/* File Tree */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Project Files
          </h3>
        </div>
        <div className="p-2 overflow-y-auto h-full">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            renderFileTree(files)
          )}
        </div>
      </div>

      {/* File Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            {selectedFile ? selectedFile.name : 'Select a file'}
          </h3>
        </div>
        <div className="overflow-y-auto h-full">
          {selectedFile && selectedFile.content ? (
            <CodeViewer
              code={selectedFile.content}
              language={getLanguageFromFileName(selectedFile.name)}
              fileName={selectedFile.name}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              Select a file to view its content
            </div>
          )}
        </div>
      </div>

      {/* Analysis Results */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Analysis Results
          </h3>
        </div>
        <div className="p-4 overflow-y-auto h-full">
          {analyzing ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Analyzing...</span>
            </div>
          ) : fileAnalysis ? (
            <div className="space-y-6">
              {/* Metrics */}
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Code Metrics
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {fileAnalysis.analysis.metrics.linesOfCode}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Lines of Code
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {fileAnalysis.analysis.metrics.complexity}
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Complexity
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {fileAnalysis.analysis.metrics.maintainability}/10
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Maintainability
                    </div>
                  </div>
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {fileAnalysis.analysis.metrics.performance}/10
                    </div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      Performance
                    </div>
                  </div>
                </div>
              </div>

              {/* Issues */}
              {fileAnalysis.analysis.issues.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Issues ({fileAnalysis.analysis.issues.length})
                  </h4>
                  <div className="space-y-2">
                    {fileAnalysis.analysis.issues.map((issue, index) => {
                      const Icon = getIssueIcon(issue.type);
                      return (
                        <div
                          key={index}
                          className="flex items-start space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded"
                        >
                          <Icon className={`h-4 w-4 mt-0.5 ${getIssueColor(issue.type)}`} />
                          <div className="flex-1">
                            <p className="text-sm text-gray-900 dark:text-white">
                              {issue.message}
                            </p>
                            {issue.line && (
                              <p className="text-xs text-gray-600 dark:text-gray-400">
                                Line {issue.line}
                              </p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Suggestions */}
              {fileAnalysis.analysis.suggestions.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    Suggestions ({fileAnalysis.analysis.suggestions.length})
                  </h4>
                  <div className="space-y-3">
                    {fileAnalysis.analysis.suggestions.map((suggestion) => (
                      <div
                        key={suggestion.id}
                        className="p-3 bg-blue-50 dark:bg-blue-900 rounded border border-blue-200 dark:border-blue-700"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-blue-900 dark:text-blue-100">
                            {suggestion.title}
                          </h5>
                          <span className="text-xs bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                            {Math.round(suggestion.confidence * 100)}%
                          </span>
                        </div>
                        <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
                          {suggestion.description}
                        </p>
                        <div className="flex items-center space-x-2 text-xs text-blue-700 dark:text-blue-300">
                          <span className="capitalize">{suggestion.type}</span>
                          <span>•</span>
                          <span className="capitalize">{suggestion.impact} impact</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              {selectedFile && isCodeFile(selectedFile.name)
                ? 'Click analyze to see results'
                : 'Select a code file to analyze'
              }
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function getLanguageFromFileName(fileName: string): string {
  const ext = fileName.split('.').pop()?.toLowerCase();
  switch (ext) {
    case 'ts':
    case 'tsx':
      return 'typescript';
    case 'js':
    case 'jsx':
      return 'javascript';
    case 'css':
      return 'css';
    case 'json':
      return 'json';
    default:
      return 'text';
  }
}
