import { NextRequest, NextResponse } from 'next/server';
import { selfImprover } from '@/lib/improver';
import { safetyManager } from '@/lib/safety';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'current-session':
        const currentSession = selfImprover.getCurrentSession();
        return NextResponse.json({
          success: true,
          data: currentSession
        });

      case 'sessions':
        const sessions = selfImprover.getAllSessions();
        return NextResponse.json({
          success: true,
          data: sessions
        });

      case 'stats':
        const stats = selfImprover.getStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'settings':
        const settings = selfImprover.getSettings();
        return NextResponse.json({
          success: true,
          data: settings
        });

      case 'applied-changes':
        const changes = safetyManager.getAppliedChanges();
        return NextResponse.json({
          success: true,
          data: changes
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Improve GET error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'start-session':
        const { settings } = data || {};
        const session = await selfImprover.startImprovementSession(settings);
        
        return NextResponse.json({
          success: true,
          data: session
        });

      case 'cancel-session':
        const cancelled = selfImprover.cancelCurrentSession();
        
        return NextResponse.json({
          success: true,
          data: { cancelled }
        });

      case 'apply-suggestion':
        const { suggestion, force } = data;
        const appliedChange = await selfImprover.applyCustomImprovement(suggestion, force);
        
        return NextResponse.json({
          success: true,
          data: appliedChange
        });

      case 'rollback-change':
        const { changeId } = data;
        const rollbackSuccess = await selfImprover.rollbackChange(changeId);
        
        return NextResponse.json({
          success: true,
          data: { success: rollbackSuccess }
        });

      case 'generate-custom':
        const { description, targetFile } = data;
        const customSuggestion = await selfImprover.generateCustomImprovement(description, targetFile);
        
        return NextResponse.json({
          success: true,
          data: customSuggestion
        });

      case 'update-settings':
        const { newSettings } = data;
        selfImprover.updateSettings(newSettings);
        
        return NextResponse.json({
          success: true,
          message: 'Settings updated'
        });

      case 'export-session':
        const { sessionId } = data;
        const exportPath = await selfImprover.exportSession(sessionId);
        
        return NextResponse.json({
          success: true,
          data: { exportPath }
        });

      case 'validate-change':
        const { suggestionToValidate } = data;
        const validation = await safetyManager.validateChange(suggestionToValidate);
        
        return NextResponse.json({
          success: true,
          data: validation
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Improve POST error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
