{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/ai-client.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nexport interface CodeAnalysisResult {\n  issues: CodeIssue[];\n  suggestions: CodeSuggestion[];\n  metrics: CodeMetrics;\n}\n\nexport interface CodeIssue {\n  type: 'error' | 'warning' | 'info';\n  message: string;\n  file: string;\n  line?: number;\n  column?: number;\n  severity: number; // 1-10\n}\n\nexport interface CodeSuggestion {\n  id: string;\n  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';\n  title: string;\n  description: string;\n  file: string;\n  originalCode: string;\n  suggestedCode: string;\n  confidence: number; // 0-1\n  impact: 'low' | 'medium' | 'high';\n  estimatedBenefit: string;\n}\n\nexport interface CodeMetrics {\n  linesOfCode: number;\n  complexity: number;\n  maintainability: number;\n  testCoverage?: number;\n  performance: number;\n}\n\nexport class AIClient {\n  private client: OpenAI;\n  private model: string;\n\n  constructor() {\n    if (!process.env.OPENROUTER_API_KEY) {\n      throw new Error('OPENROUTER_API_KEY environment variable is required');\n    }\n\n    this.client = new OpenAI({\n      baseURL: 'https://openrouter.ai/api/v1',\n      apiKey: process.env.OPENROUTER_API_KEY,\n      defaultHeaders: {\n        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n        'X-Title': process.env.NEXT_PUBLIC_APP_NAME || 'Self-Improving App',\n      },\n    });\n\n    // Default to GPT-4.1 Nano as specified\n    this.model = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-nano';\n  }\n\n  async analyzeCode(code: string, fileName: string): Promise<CodeAnalysisResult> {\n    const prompt = this.createAnalysisPrompt(code, fileName);\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert code analyzer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.2,\n        max_tokens: 3000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        console.warn('No response content from AI model');\n        return this.createEmptyAnalysis();\n      }\n\n      const result = this.parseAnalysisResponse(content, fileName);\n\n      // If parsing failed, create a basic analysis with some default metrics\n      if (result.issues.length === 0 && result.suggestions.length === 0 && result.metrics.linesOfCode === 0) {\n        return this.createBasicAnalysis(code, fileName);\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error analyzing code:', error);\n      return this.createBasicAnalysis(code, fileName);\n    }\n  }\n\n  async generateImprovement(\n    code: string,\n    fileName: string,\n    improvementType: string,\n    context?: string\n  ): Promise<CodeSuggestion | null> {\n    const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert software engineer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.3,\n        max_tokens: 2000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        console.warn('No response content from AI model for improvement generation');\n        return null;\n      }\n\n      const result = this.parseImprovementResponse(content, fileName, improvementType);\n\n      // If parsing failed but we have content, try to create a basic suggestion\n      if (!result && content.length > 10) {\n        console.warn('Failed to parse improvement response, creating fallback suggestion');\n        return this.createFallbackSuggestion(fileName, improvementType, content);\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error generating improvement:', error);\n      return null;\n    }\n  }\n\n  async generateNewFeature(\n    description: string,\n    existingCode: string,\n    fileName: string\n  ): Promise<CodeSuggestion | null> {\n    const prompt = `\nGenerate a new feature based on this description: \"${description}\"\n\nExisting code context:\n\\`\\`\\`${this.getFileExtension(fileName)}\n${existingCode}\n\\`\\`\\`\n\nPlease provide:\n1. A clear implementation plan\n2. The new code to add\n3. Any modifications needed to existing code\n4. Potential impacts and considerations\n\nReturn your response as JSON with this structure:\n{\n  \"title\": \"Feature title\",\n  \"description\": \"Detailed description\",\n  \"originalCode\": \"existing code that needs modification\",\n  \"suggestedCode\": \"new/modified code\",\n  \"confidence\": 0.8,\n  \"impact\": \"medium\",\n  \"estimatedBenefit\": \"description of benefits\"\n}\n`;\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert software engineer specializing in feature development.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.5,\n        max_tokens: 2000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        return null;\n      }\n\n      return this.parseImprovementResponse(content, fileName, 'feature');\n    } catch (error) {\n      console.error('Error generating new feature:', error);\n      return null;\n    }\n  }\n\n  private createAnalysisPrompt(code: string, fileName: string): string {\n    return `\nAnalyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:\n\nFile: ${fileName}\n\\`\\`\\`${this.getFileExtension(fileName)}\n${code}\n\\`\\`\\`\n\nIMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text before or after the JSON.\n\nRequired JSON structure:\n{\n  \"issues\": [\n    {\n      \"type\": \"error\",\n      \"message\": \"Issue description\",\n      \"line\": 10,\n      \"severity\": 5\n    }\n  ],\n  \"suggestions\": [\n    {\n      \"type\": \"performance\",\n      \"title\": \"Suggestion title\",\n      \"description\": \"Detailed description\",\n      \"originalCode\": \"code to replace\",\n      \"suggestedCode\": \"replacement code\",\n      \"confidence\": 0.8,\n      \"impact\": \"medium\",\n      \"estimatedBenefit\": \"Description of benefits\"\n    }\n  ],\n  \"metrics\": {\n    \"linesOfCode\": 50,\n    \"complexity\": 3,\n    \"maintainability\": 8,\n    \"performance\": 7\n  }\n}\n\nEnsure all string values are properly quoted and the JSON is valid.`;\n  }\n\n  private createImprovementPrompt(\n    code: string,\n    fileName: string,\n    improvementType: string,\n    context?: string\n  ): string {\n    return `\nGenerate a ${improvementType} improvement for this code:\n\nFile: ${fileName}\n${context ? `Context: ${context}` : ''}\n\n\\`\\`\\`${this.getFileExtension(fileName)}\n${code}\n\\`\\`\\`\n\nIMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text.\n\nFocus on ${improvementType} improvements and return this exact JSON structure:\n{\n  \"title\": \"Improvement title\",\n  \"description\": \"Detailed description of the improvement\",\n  \"originalCode\": \"Exact code to be replaced\",\n  \"suggestedCode\": \"Improved replacement code\",\n  \"confidence\": 0.8,\n  \"impact\": \"medium\",\n  \"estimatedBenefit\": \"Description of expected benefits\"\n}\n\nEnsure all strings are properly quoted and the JSON is valid.`;\n  }\n\n  private parseAnalysisResponse(content: string, fileName: string): CodeAnalysisResult {\n    try {\n      // GPT-4.1 Nano returns clean JSON, so try direct parsing first\n      const trimmedContent = content.trim();\n      let parsed: any;\n\n      try {\n        // Direct parsing - this should work for GPT-4.1 Nano\n        parsed = JSON.parse(trimmedContent);\n        console.log('Direct JSON parsing successful');\n      } catch (directParseError) {\n        console.log('Direct JSON parsing failed, trying extraction methods...');\n        console.log('Parse error:', directParseError);\n\n        // If direct parsing fails, try to extract JSON from response\n        let jsonStr = this.extractJSON(content);\n        if (!jsonStr) {\n          console.warn('No valid JSON found in AI response, using fallback analysis');\n          return this.createBasicAnalysis(content, fileName);\n        }\n\n        try {\n          // Try parsing the extracted JSON without cleaning first\n          parsed = JSON.parse(jsonStr);\n          console.log('Extracted JSON parsing successful');\n        } catch (extractedParseError) {\n          console.log('Extracted JSON parsing failed, trying cleanup...');\n          console.log('Extracted parse error:', extractedParseError);\n          console.log('JSON string before cleanup:', jsonStr.substring(0, 100) + '...');\n\n          // Only clean if extraction also fails\n          jsonStr = this.cleanupJSON(jsonStr);\n          console.log('JSON string after cleanup:', jsonStr.substring(0, 100) + '...');\n          parsed = JSON.parse(jsonStr);\n          console.log('Cleaned JSON parsing successful');\n        }\n      }\n\n      // Validate and normalize the response structure\n      const normalized = this.normalizeAnalysisResponse(parsed, fileName);\n\n      return normalized;\n    } catch (error) {\n      console.error('Error parsing analysis response:', error);\n      console.error('Raw content preview:', content.substring(0, 500) + '...');\n\n      // Try to extract partial information if full JSON parsing fails\n      const fallback = this.extractPartialAnalysis(content, fileName);\n      if (fallback) {\n        console.log('Using fallback partial analysis');\n        return fallback;\n      }\n\n      console.log('Using basic analysis fallback');\n      return this.createBasicAnalysis(content, fileName);\n    }\n  }\n\n  private extractPartialAnalysis(content: string, fileName: string): CodeAnalysisResult | null {\n    try {\n      // Try to extract individual sections even if the full JSON is malformed\n      const result: CodeAnalysisResult = {\n        issues: [],\n        suggestions: [],\n        metrics: {\n          linesOfCode: 0,\n          complexity: 0,\n          maintainability: 5,\n          performance: 5\n        }\n      };\n\n      // Try to extract issues\n      const issuesMatch = content.match(/\"issues\"\\s*:\\s*\\[([\\s\\S]*?)\\]/);\n      if (issuesMatch) {\n        try {\n          const issuesJson = `[${issuesMatch[1]}]`;\n          const issues = JSON.parse(issuesJson);\n          if (Array.isArray(issues)) {\n            result.issues = issues.map((issue: any) => ({\n              type: issue.type || 'info',\n              message: issue.message || 'Issue detected',\n              file: fileName,\n              line: issue.line,\n              severity: issue.severity || 1\n            }));\n          }\n        } catch (e) {\n          console.warn('Could not parse issues section');\n        }\n      }\n\n      // Try to extract metrics\n      const metricsMatch = content.match(/\"metrics\"\\s*:\\s*\\{([^}]*)\\}/);\n      if (metricsMatch) {\n        try {\n          const metricsJson = `{${metricsMatch[1]}}`;\n          const metrics = JSON.parse(metricsJson);\n          result.metrics = {\n            linesOfCode: metrics.linesOfCode || 0,\n            complexity: metrics.complexity || 0,\n            maintainability: metrics.maintainability || 5,\n            performance: metrics.performance || 5\n          };\n        } catch (e) {\n          console.warn('Could not parse metrics section');\n        }\n      }\n\n      return result;\n    } catch (error) {\n      console.warn('Could not extract partial analysis');\n      return null;\n    }\n  }\n\n  private extractJSON(content: string): string | null {\n    // Try multiple patterns to find JSON\n    const patterns = [\n      /\\{[\\s\\S]*\\}/,  // Basic JSON object\n      /```json\\s*(\\{[\\s\\S]*?\\})\\s*```/,  // JSON in code blocks\n      /```\\s*(\\{[\\s\\S]*?\\})\\s*```/,  // JSON in generic code blocks\n    ];\n\n    for (const pattern of patterns) {\n      const match = content.match(pattern);\n      if (match) {\n        return match[1] || match[0];\n      }\n    }\n\n    return null;\n  }\n\n  private cleanupJSON(jsonStr: string): string {\n    // Remove common formatting issues\n    let cleaned = jsonStr\n      .replace(/,\\s*}/g, '}')  // Remove trailing commas in objects\n      .replace(/,\\s*]/g, ']')  // Remove trailing commas in arrays\n      .replace(/'/g, '\"')      // Replace single quotes with double quotes\n      .replace(/(\\w+):/g, '\"$1\":')  // Quote unquoted keys\n      .replace(/\"\\s*\"/g, '\"\"')  // Fix empty strings\n      .replace(/\\\\'/g, \"'\")    // Fix escaped single quotes\n      .replace(/\\\\\"/g, '\\\\\"')  // Ensure double quotes are properly escaped\n      .replace(/\\n/g, '\\\\n')   // Escape newlines in strings\n      .replace(/\\r/g, '\\\\r')   // Escape carriage returns\n      .replace(/\\t/g, '\\\\t')   // Escape tabs\n      .trim();\n\n    // Fix common issues with property names that got over-quoted\n    cleaned = cleaned.replace(/\"(\\w+)\":/g, '\"$1\":');\n\n    // Fix issues where quotes inside strings break JSON\n    // This is a simple approach - find strings and escape internal quotes\n    cleaned = this.fixQuotesInStrings(cleaned);\n\n    return cleaned;\n  }\n\n  private fixQuotesInStrings(jsonStr: string): string {\n    // This is a simplified approach to fix quotes in string values\n    // It's not perfect but handles most common cases\n    try {\n      // Try to parse as-is first\n      JSON.parse(jsonStr);\n      return jsonStr;\n    } catch (error) {\n      // If parsing fails, try to fix common quote issues\n      let fixed = jsonStr;\n\n      // Replace unescaped quotes in string values (basic heuristic)\n      fixed = fixed.replace(/\"([^\"]*)\"([^\"]*)\"([^\"]*)\":/g, '\"$1\\\\\"$2\\\\\"$3\":');\n      fixed = fixed.replace(/:\\s*\"([^\"]*)\"([^\"]*)\"([^\"]*)\"(?=\\s*[,}])/g, ': \"$1\\\\\"$2\\\\\"$3\"');\n\n      return fixed;\n    }\n  }\n\n  private normalizeAnalysisResponse(parsed: any, fileName: string): CodeAnalysisResult {\n    const result: CodeAnalysisResult = {\n      issues: [],\n      suggestions: [],\n      metrics: {\n        linesOfCode: 0,\n        complexity: 0,\n        maintainability: 0,\n        performance: 0\n      }\n    };\n\n    // Normalize issues\n    if (Array.isArray(parsed.issues)) {\n      result.issues = parsed.issues.map((issue: any) => ({\n        type: issue.type || 'info',\n        message: issue.message || 'No message provided',\n        file: fileName,\n        line: issue.line || undefined,\n        column: issue.column || undefined,\n        severity: issue.severity || 1\n      }));\n    }\n\n    // Normalize suggestions\n    if (Array.isArray(parsed.suggestions)) {\n      result.suggestions = parsed.suggestions.map((suggestion: any) => ({\n        id: this.generateId(),\n        type: suggestion.type || 'refactor',\n        title: suggestion.title || 'Improvement suggestion',\n        description: suggestion.description || 'No description provided',\n        file: fileName,\n        originalCode: suggestion.originalCode || '',\n        suggestedCode: suggestion.suggestedCode || '',\n        confidence: Math.min(Math.max(suggestion.confidence || 0.5, 0), 1),\n        impact: suggestion.impact || 'medium',\n        estimatedBenefit: suggestion.estimatedBenefit || 'Improved code quality'\n      }));\n    }\n\n    // Normalize metrics\n    if (parsed.metrics && typeof parsed.metrics === 'object') {\n      result.metrics = {\n        linesOfCode: Math.max(parsed.metrics.linesOfCode || 0, 0),\n        complexity: Math.max(parsed.metrics.complexity || 0, 0),\n        maintainability: Math.min(Math.max(parsed.metrics.maintainability || 5, 0), 10),\n        performance: Math.min(Math.max(parsed.metrics.performance || 5, 0), 10)\n      };\n    }\n\n    return result;\n  }\n\n  private parseImprovementResponse(\n    content: string,\n    fileName: string,\n    type: string = 'refactor'\n  ): CodeSuggestion | null {\n    try {\n      const trimmedContent = content.trim();\n      let parsed: any;\n\n      try {\n        // Direct parsing for GPT-4.1 Nano clean JSON\n        parsed = JSON.parse(trimmedContent);\n      } catch (directParseError) {\n        console.log('Direct improvement JSON parsing failed, trying extraction...');\n\n        let jsonStr = this.extractJSON(content);\n        if (!jsonStr) {\n          console.warn('No valid JSON found in improvement response');\n          return null;\n        }\n\n        try {\n          parsed = JSON.parse(jsonStr);\n        } catch (extractedParseError) {\n          jsonStr = this.cleanupJSON(jsonStr);\n          parsed = JSON.parse(jsonStr);\n        }\n      }\n\n      // Normalize the improvement response\n      return {\n        id: this.generateId(),\n        type: type as any,\n        file: fileName,\n        title: parsed.title || 'Code Improvement',\n        description: parsed.description || 'No description provided',\n        originalCode: parsed.originalCode || '',\n        suggestedCode: parsed.suggestedCode || '',\n        confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1),\n        impact: parsed.impact || 'medium',\n        estimatedBenefit: parsed.estimatedBenefit || 'Improved code quality'\n      };\n    } catch (error) {\n      console.error('Error parsing improvement response:', error);\n      console.error('Raw content preview:', content.substring(0, 200) + '...');\n      return null;\n    }\n  }\n\n  private createEmptyAnalysis(): CodeAnalysisResult {\n    return {\n      issues: [],\n      suggestions: [],\n      metrics: {\n        linesOfCode: 0,\n        complexity: 0,\n        maintainability: 0,\n        performance: 0\n      }\n    };\n  }\n\n  private createBasicAnalysis(code: string, fileName: string): CodeAnalysisResult {\n    const lines = code.split('\\n').length;\n    const complexity = Math.min(Math.floor(lines / 10), 10);\n\n    return {\n      issues: [\n        {\n          type: 'info',\n          message: 'AI analysis temporarily unavailable - showing basic metrics only',\n          file: fileName,\n          severity: 1\n        }\n      ],\n      suggestions: [],\n      metrics: {\n        linesOfCode: lines,\n        complexity: complexity,\n        maintainability: Math.max(10 - complexity, 1),\n        performance: 5\n      }\n    };\n  }\n\n  private createFallbackSuggestion(fileName: string, improvementType: string, content: string): CodeSuggestion {\n    return {\n      id: this.generateId(),\n      type: improvementType as any,\n      title: `${improvementType.charAt(0).toUpperCase() + improvementType.slice(1)} Improvement`,\n      description: 'AI generated an improvement suggestion but the response format was invalid. Please review the raw suggestion.',\n      file: fileName,\n      originalCode: '// Original code section',\n      suggestedCode: content.substring(0, 500) + (content.length > 500 ? '...' : ''),\n      confidence: 0.3,\n      impact: 'low',\n      estimatedBenefit: 'Manual review required due to parsing issues'\n    };\n  }\n\n  private getFileExtension(fileName: string): string {\n    const ext = fileName.split('.').pop()?.toLowerCase();\n    switch (ext) {\n      case 'ts':\n      case 'tsx':\n        return 'typescript';\n      case 'js':\n      case 'jsx':\n        return 'javascript';\n      case 'css':\n        return 'css';\n      case 'json':\n        return 'json';\n      default:\n        return 'text';\n    }\n  }\n\n  private generateId(): string {\n    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }\n}\n\nexport const aiClient = new AIClient();\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAsCO,MAAM;IACH,OAAe;IACf,MAAc;IAEtB,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,MAAM,GAAG,IAAI,wKAAA,CAAA,UAAM,CAAC;YACvB,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACtC,gBAAgB;gBACd,gBAAgB,6DAAmC;gBACnD,WAAW,0DAAoC;YACjD;QACF;QAEA,uCAAuC;QACvC,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAC/C;IAEA,MAAM,YAAY,IAAY,EAAE,QAAgB,EAA+B;QAC7E,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,MAAM;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,mBAAmB;YACjC;YAEA,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAEnD,uEAAuE;YACvE,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,KAAK,OAAO,WAAW,CAAC,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,WAAW,KAAK,GAAG;gBACrG,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACxC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACxC;IACF;IAEA,MAAM,oBACJ,IAAY,EACZ,QAAgB,EAChB,eAAuB,EACvB,OAAgB,EACgB;QAChC,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC,MAAM,UAAU,iBAAiB;QAE7E,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,MAAM,SAAS,IAAI,CAAC,wBAAwB,CAAC,SAAS,UAAU;YAEhE,0EAA0E;YAC1E,IAAI,CAAC,UAAU,QAAQ,MAAM,GAAG,IAAI;gBAClC,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,wBAAwB,CAAC,UAAU,iBAAiB;YAClE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,mBACJ,WAAmB,EACnB,YAAoB,EACpB,QAAgB,EACgB;QAChC,MAAM,SAAS,CAAC;mDAC+B,EAAE,YAAY;;;MAG3D,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,aAAa;;;;;;;;;;;;;;;;;;;AAmBf,CAAC;QAEG,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,OAAO;YACT;YAEA,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,UAAU;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEQ,qBAAqB,IAAY,EAAE,QAAgB,EAAU;QACnE,OAAO,CAAC;aACC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;;MAEzC,EAAE,SAAS;MACX,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mEAmC4D,CAAC;IAClE;IAEQ,wBACN,IAAY,EACZ,QAAgB,EAChB,eAAuB,EACvB,OAAgB,EACR;QACR,OAAO,CAAC;WACD,EAAE,gBAAgB;;MAEvB,EAAE,SAAS;AACjB,EAAE,UAAU,CAAC,SAAS,EAAE,SAAS,GAAG,GAAG;;MAEjC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,KAAK;;;;;SAKE,EAAE,gBAAgB;;;;;;;;;;;6DAWkC,CAAC;IAC5D;IAEQ,sBAAsB,OAAe,EAAE,QAAgB,EAAsB;QACnF,IAAI;YACF,+DAA+D;YAC/D,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI;YAEJ,IAAI;gBACF,qDAAqD;gBACrD,SAAS,KAAK,KAAK,CAAC;gBACpB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,gBAAgB;gBAE5B,6DAA6D;gBAC7D,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC/B,IAAI,CAAC,SAAS;oBACZ,QAAQ,IAAI,CAAC;oBACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;gBAC3C;gBAEA,IAAI;oBACF,wDAAwD;oBACxD,SAAS,KAAK,KAAK,CAAC;oBACpB,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,qBAAqB;oBAC5B,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,0BAA0B;oBACtC,QAAQ,GAAG,CAAC,+BAA+B,QAAQ,SAAS,CAAC,GAAG,OAAO;oBAEvE,sCAAsC;oBACtC,UAAU,IAAI,CAAC,WAAW,CAAC;oBAC3B,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,SAAS,CAAC,GAAG,OAAO;oBACtE,SAAS,KAAK,KAAK,CAAC;oBACpB,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA,gDAAgD;YAChD,MAAM,aAAa,IAAI,CAAC,yBAAyB,CAAC,QAAQ;YAE1D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAElE,gEAAgE;YAChE,MAAM,WAAW,IAAI,CAAC,sBAAsB,CAAC,SAAS;YACtD,IAAI,UAAU;gBACZ,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAC3C;IACF;IAEQ,uBAAuB,OAAe,EAAE,QAAgB,EAA6B;QAC3F,IAAI;YACF,wEAAwE;YACxE,MAAM,SAA6B;gBACjC,QAAQ,EAAE;gBACV,aAAa,EAAE;gBACf,SAAS;oBACP,aAAa;oBACb,YAAY;oBACZ,iBAAiB;oBACjB,aAAa;gBACf;YACF;YAEA,wBAAwB;YACxB,MAAM,cAAc,QAAQ,KAAK,CAAC;YAClC,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxC,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,OAAO,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,QAAe,CAAC;gCAC1C,MAAM,MAAM,IAAI,IAAI;gCACpB,SAAS,MAAM,OAAO,IAAI;gCAC1B,MAAM;gCACN,MAAM,MAAM,IAAI;gCAChB,UAAU,MAAM,QAAQ,IAAI;4BAC9B,CAAC;oBACH;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,yBAAyB;YACzB,MAAM,eAAe,QAAQ,KAAK,CAAC;YACnC,IAAI,cAAc;gBAChB,IAAI;oBACF,MAAM,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1C,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,OAAO,GAAG;wBACf,aAAa,QAAQ,WAAW,IAAI;wBACpC,YAAY,QAAQ,UAAU,IAAI;wBAClC,iBAAiB,QAAQ,eAAe,IAAI;wBAC5C,aAAa,QAAQ,WAAW,IAAI;oBACtC;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;IACF;IAEQ,YAAY,OAAe,EAAiB;QAClD,qCAAqC;QACrC,MAAM,WAAW;YACf;YACA;YACA;SACD;QAED,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YAC7B;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,OAAe,EAAU;QAC3C,kCAAkC;QAClC,IAAI,UAAU,QACX,OAAO,CAAC,UAAU,KAAM,oCAAoC;SAC5D,OAAO,CAAC,UAAU,KAAM,mCAAmC;SAC3D,OAAO,CAAC,MAAM,KAAU,2CAA2C;SACnE,OAAO,CAAC,WAAW,SAAU,sBAAsB;SACnD,OAAO,CAAC,UAAU,MAAO,oBAAoB;SAC7C,OAAO,CAAC,QAAQ,KAAQ,4BAA4B;SACpD,OAAO,CAAC,QAAQ,OAAQ,4CAA4C;SACpE,OAAO,CAAC,OAAO,OAAS,6BAA6B;SACrD,OAAO,CAAC,OAAO,OAAS,0BAA0B;SAClD,OAAO,CAAC,OAAO,OAAS,cAAc;SACtC,IAAI;QAEP,6DAA6D;QAC7D,UAAU,QAAQ,OAAO,CAAC,aAAa;QAEvC,oDAAoD;QACpD,sEAAsE;QACtE,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAElC,OAAO;IACT;IAEQ,mBAAmB,OAAe,EAAU;QAClD,+DAA+D;QAC/D,iDAAiD;QACjD,IAAI;YACF,2BAA2B;YAC3B,KAAK,KAAK,CAAC;YACX,OAAO;QACT,EAAE,OAAO,OAAO;YACd,mDAAmD;YACnD,IAAI,QAAQ;YAEZ,8DAA8D;YAC9D,QAAQ,MAAM,OAAO,CAAC,+BAA+B;YACrD,QAAQ,MAAM,OAAO,CAAC,6CAA6C;YAEnE,OAAO;QACT;IACF;IAEQ,0BAA0B,MAAW,EAAE,QAAgB,EAAsB;QACnF,MAAM,SAA6B;YACjC,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB;gBACjB,aAAa;YACf;QACF;QAEA,mBAAmB;QACnB,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;YAChC,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAe,CAAC;oBACjD,MAAM,MAAM,IAAI,IAAI;oBACpB,SAAS,MAAM,OAAO,IAAI;oBAC1B,MAAM;oBACN,MAAM,MAAM,IAAI,IAAI;oBACpB,QAAQ,MAAM,MAAM,IAAI;oBACxB,UAAU,MAAM,QAAQ,IAAI;gBAC9B,CAAC;QACH;QAEA,wBAAwB;QACxB,IAAI,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG;YACrC,OAAO,WAAW,GAAG,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,aAAoB,CAAC;oBAChE,IAAI,IAAI,CAAC,UAAU;oBACnB,MAAM,WAAW,IAAI,IAAI;oBACzB,OAAO,WAAW,KAAK,IAAI;oBAC3B,aAAa,WAAW,WAAW,IAAI;oBACvC,MAAM;oBACN,cAAc,WAAW,YAAY,IAAI;oBACzC,eAAe,WAAW,aAAa,IAAI;oBAC3C,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,WAAW,UAAU,IAAI,KAAK,IAAI;oBAChE,QAAQ,WAAW,MAAM,IAAI;oBAC7B,kBAAkB,WAAW,gBAAgB,IAAI;gBACnD,CAAC;QACH;QAEA,oBAAoB;QACpB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;YACxD,OAAO,OAAO,GAAG;gBACf,aAAa,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvD,YAAY,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,UAAU,IAAI,GAAG;gBACrD,iBAAiB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,eAAe,IAAI,GAAG,IAAI;gBAC5E,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,GAAG,IAAI;YACtE;QACF;QAEA,OAAO;IACT;IAEQ,yBACN,OAAe,EACf,QAAgB,EAChB,OAAe,UAAU,EACF;QACvB,IAAI;YACF,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI;YAEJ,IAAI;gBACF,6CAA6C;gBAC7C,SAAS,KAAK,KAAK,CAAC;YACtB,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,GAAG,CAAC;gBAEZ,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC/B,IAAI,CAAC,SAAS;oBACZ,QAAQ,IAAI,CAAC;oBACb,OAAO;gBACT;gBAEA,IAAI;oBACF,SAAS,KAAK,KAAK,CAAC;gBACtB,EAAE,OAAO,qBAAqB;oBAC5B,UAAU,IAAI,CAAC,WAAW,CAAC;oBAC3B,SAAS,KAAK,KAAK,CAAC;gBACtB;YACF;YAEA,qCAAqC;YACrC,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,MAAM;gBACN,OAAO,OAAO,KAAK,IAAI;gBACvB,aAAa,OAAO,WAAW,IAAI;gBACnC,cAAc,OAAO,YAAY,IAAI;gBACrC,eAAe,OAAO,aAAa,IAAI;gBACvC,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,UAAU,IAAI,KAAK,IAAI;gBAC5D,QAAQ,OAAO,MAAM,IAAI;gBACzB,kBAAkB,OAAO,gBAAgB,IAAI;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAClE,OAAO;QACT;IACF;IAEQ,sBAA0C;QAChD,OAAO;YACL,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB;gBACjB,aAAa;YACf;QACF;IACF;IAEQ,oBAAoB,IAAY,EAAE,QAAgB,EAAsB;QAC9E,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM;QACrC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK;QAEpD,OAAO;YACL,QAAQ;gBACN;oBACE,MAAM;oBACN,SAAS;oBACT,MAAM;oBACN,UAAU;gBACZ;aACD;YACD,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB,KAAK,GAAG,CAAC,KAAK,YAAY;gBAC3C,aAAa;YACf;QACF;IACF;IAEQ,yBAAyB,QAAgB,EAAE,eAAuB,EAAE,OAAe,EAAkB;QAC3G,OAAO;YACL,IAAI,IAAI,CAAC,UAAU;YACnB,MAAM;YACN,OAAO,GAAG,gBAAgB,MAAM,CAAC,GAAG,WAAW,KAAK,gBAAgB,KAAK,CAAC,GAAG,YAAY,CAAC;YAC1F,aAAa;YACb,MAAM;YACN,cAAc;YACd,eAAe,QAAQ,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,EAAE;YAC7E,YAAY;YACZ,QAAQ;YACR,kBAAkB;QACpB;IACF;IAEQ,iBAAiB,QAAgB,EAAU;QACjD,MAAM,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QACvC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEQ,aAAqB;QAC3B,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;IACvE;AACF;AAEO,MAAM,WAAW,IAAI", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/app/api/test-ai/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { aiClient } from '@/lib/ai-client';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const testCode = `\nfunction hello(name) {\n  console.log(\"Hello \" + name);\n  return name;\n}\n`;\n\n    console.log('Testing AI client with simple code...');\n    const result = await aiClient.analyzeCode(testCode, 'test.js');\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        testCode,\n        analysis: result,\n        message: 'AI client is working correctly'\n      }\n    });\n  } catch (error) {\n    console.error('AI test error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      message: 'AI client test failed'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { code, fileName } = body;\n\n    if (!code || !fileName) {\n      return NextResponse.json({\n        success: false,\n        error: 'Code and fileName are required'\n      }, { status: 400 });\n    }\n\n    console.log(`Testing AI client with custom code: ${fileName}`);\n    const result = await aiClient.analyzeCode(code, fileName);\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        analysis: result,\n        message: 'Custom code analysis completed'\n      }\n    });\n  } catch (error) {\n    console.error('AI test error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      message: 'AI client test failed'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAC;;;;;AAKtB,CAAC;QAEG,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,4HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,UAAU;QAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,UAAU;gBACV,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;QAE3B,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,UAAU;QAC7D,MAAM,SAAS,MAAM,4HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,MAAM;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,UAAU;gBACV,SAAS;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}