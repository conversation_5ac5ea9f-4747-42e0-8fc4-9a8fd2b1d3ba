module.exports = {

"[project]/.next-internal/server/app/api/improve/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/constants [external] (constants, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("constants", () => require("constants"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/filesystem.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileSystemManager": (()=>FileSystemManager),
    "fileSystem": (()=>fileSystem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fs-extra/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileSystemManager {
    projectRoot;
    backupDir;
    constructor(projectRoot){
        this.projectRoot = projectRoot || process.cwd();
        this.backupDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.projectRoot, '.self-improving-backups');
        this.ensureBackupDir();
    }
    async ensureBackupDir() {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(this.backupDir);
    }
    async readFile(filePath) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readFile(fullPath, 'utf-8');
    }
    async writeFile(filePath, content) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(fullPath));
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].writeFile(fullPath, content, 'utf-8');
    }
    async fileExists(filePath) {
        try {
            const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].access(fullPath);
            return true;
        } catch  {
            return false;
        }
    }
    async getFileInfo(filePath) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].stat(fullPath);
        const info = {
            name: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath),
            path: filePath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            lastModified: stats.mtime
        };
        if (info.type === 'file' && this.isTextFile(filePath)) {
            try {
                info.content = await this.readFile(filePath);
            } catch (error) {
                console.warn(`Could not read file content for ${filePath}:`, error);
            }
        }
        return info;
    }
    async listDirectory(dirPath = '', recursive = false) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, dirPath);
        const items = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readdir(fullPath);
        const fileInfos = [];
        for (const item of items){
            // Skip node_modules, .git, and backup directories
            if (this.shouldSkipPath(item)) continue;
            const itemPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, item);
            try {
                const info = await this.getFileInfo(itemPath);
                fileInfos.push(info);
                if (recursive && info.type === 'directory') {
                    const subItems = await this.listDirectory(itemPath, true);
                    fileInfos.push(...subItems);
                }
            } catch (error) {
                console.warn(`Could not get info for ${itemPath}:`, error);
            }
        }
        return fileInfos;
    }
    async createBackup(files, description) {
        const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, backupId);
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(backupPath);
        const backupInfo = {
            id: backupId,
            timestamp: new Date(),
            files: files,
            description: description
        };
        // Copy files to backup directory
        for (const file of files){
            try {
                const sourcePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, file);
                const targetPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, file);
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(targetPath));
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].copy(sourcePath, targetPath);
            } catch (error) {
                console.warn(`Could not backup file ${file}:`, error);
            }
        }
        // Save backup metadata
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].writeJSON(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, 'backup-info.json'), backupInfo, {
            spaces: 2
        });
        return backupId;
    }
    async restoreBackup(backupId) {
        const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, backupId);
        const backupInfoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, 'backup-info.json');
        if (!await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(backupInfoPath)) {
            throw new Error(`Backup ${backupId} not found`);
        }
        const backupInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readJSON(backupInfoPath);
        for (const file of backupInfo.files){
            try {
                const sourcePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, file);
                const targetPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, file);
                if (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(sourcePath)) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(targetPath));
                    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].copy(sourcePath, targetPath);
                }
            } catch (error) {
                console.warn(`Could not restore file ${file}:`, error);
            }
        }
    }
    async listBackups() {
        const backups = [];
        if (!await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(this.backupDir)) {
            return backups;
        }
        const backupDirs = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readdir(this.backupDir);
        for (const dir of backupDirs){
            try {
                const backupInfoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, dir, 'backup-info.json');
                if (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(backupInfoPath)) {
                    const backupInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readJSON(backupInfoPath);
                    // Convert timestamp string back to Date object
                    if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {
                        backupInfo.timestamp = new Date(backupInfo.timestamp);
                    }
                    backups.push(backupInfo);
                }
            } catch (error) {
                console.warn(`Could not read backup info for ${dir}:`, error);
            }
        }
        return backups.sort((a, b)=>{
            // Handle case where timestamp might be missing or invalid
            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;
            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;
            return timeB - timeA;
        });
    }
    isTextFile(filePath) {
        const textExtensions = [
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.json',
            '.md',
            '.txt',
            '.css',
            '.scss',
            '.html',
            '.xml',
            '.yml',
            '.yaml',
            '.env',
            '.gitignore',
            '.eslintrc',
            '.prettierrc',
            '.config',
            '.lock'
        ];
        const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath).toLowerCase();
        return textExtensions.includes(ext) || !ext;
    }
    shouldSkipPath(pathName) {
        const skipPatterns = [
            'node_modules',
            '.git',
            '.next',
            'dist',
            'build',
            '.self-improving-backups',
            '.env.local',
            '.DS_Store',
            'Thumbs.db'
        ];
        return skipPatterns.some((pattern)=>pathName.includes(pattern));
    }
    getProjectRoot() {
        return this.projectRoot;
    }
}
const fileSystem = new FileSystemManager();
}}),
"[project]/src/lib/ai-client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIClient": (()=>AIClient),
    "aiClient": (()=>aiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
class AIClient {
    client;
    model;
    constructor(){
        if (!process.env.OPENROUTER_API_KEY) {
            throw new Error('OPENROUTER_API_KEY environment variable is required');
        }
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: process.env.OPENROUTER_API_KEY,
            defaultHeaders: {
                'HTTP-Referer': ("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000',
                'X-Title': ("TURBOPACK compile-time value", "Self-Improving App") || 'Self-Improving App'
            }
        });
        // Default to GPT-4.1 Nano as specified
        this.model = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-nano';
    }
    async analyzeCode(code, fileName) {
        const prompt = this.createAnalysisPrompt(code, fileName);
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert code analyzer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                max_tokens: 3000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                console.warn('No response content from AI model');
                return this.createEmptyAnalysis();
            }
            const result = this.parseAnalysisResponse(content, fileName);
            // If parsing failed, create a basic analysis with some default metrics
            if (result.issues.length === 0 && result.suggestions.length === 0 && result.metrics.linesOfCode === 0) {
                return this.createBasicAnalysis(code, fileName);
            }
            return result;
        } catch (error) {
            console.error('Error analyzing code:', error);
            return this.createBasicAnalysis(code, fileName);
        }
    }
    async generateImprovement(code, fileName, improvementType, context) {
        const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert software engineer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                console.warn('No response content from AI model for improvement generation');
                return null;
            }
            const result = this.parseImprovementResponse(content, fileName, improvementType);
            // If parsing failed but we have content, try to create a basic suggestion
            if (!result && content.length > 10) {
                console.warn('Failed to parse improvement response, creating fallback suggestion');
                return this.createFallbackSuggestion(fileName, improvementType, content);
            }
            return result;
        } catch (error) {
            console.error('Error generating improvement:', error);
            return null;
        }
    }
    async generateNewFeature(description, existingCode, fileName) {
        const prompt = `
Generate a new feature based on this description: "${description}"

Existing code context:
\`\`\`${this.getFileExtension(fileName)}
${existingCode}
\`\`\`

Please provide:
1. A clear implementation plan
2. The new code to add
3. Any modifications needed to existing code
4. Potential impacts and considerations

Return your response as JSON with this structure:
{
  "title": "Feature title",
  "description": "Detailed description",
  "originalCode": "existing code that needs modification",
  "suggestedCode": "new/modified code",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "description of benefits"
}
`;
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert software engineer specializing in feature development.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.5,
                max_tokens: 2000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                return null;
            }
            return this.parseImprovementResponse(content, fileName, 'feature');
        } catch (error) {
            console.error('Error generating new feature:', error);
            return null;
        }
    }
    createAnalysisPrompt(code, fileName) {
        return `
Analyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:

File: ${fileName}
\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

IMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text before or after the JSON.

Required JSON structure:
{
  "issues": [
    {
      "type": "error",
      "message": "Issue description",
      "line": 10,
      "severity": 5
    }
  ],
  "suggestions": [
    {
      "type": "performance",
      "title": "Suggestion title",
      "description": "Detailed description",
      "originalCode": "code to replace",
      "suggestedCode": "replacement code",
      "confidence": 0.8,
      "impact": "medium",
      "estimatedBenefit": "Description of benefits"
    }
  ],
  "metrics": {
    "linesOfCode": 50,
    "complexity": 3,
    "maintainability": 8,
    "performance": 7
  }
}

Ensure all string values are properly quoted and the JSON is valid.`;
    }
    createImprovementPrompt(code, fileName, improvementType, context) {
        return `
Generate a ${improvementType} improvement for this code:

File: ${fileName}
${context ? `Context: ${context}` : ''}

\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

IMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text.

Focus on ${improvementType} improvements and return this exact JSON structure:
{
  "title": "Improvement title",
  "description": "Detailed description of the improvement",
  "originalCode": "Exact code to be replaced",
  "suggestedCode": "Improved replacement code",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "Description of expected benefits"
}

Ensure all strings are properly quoted and the JSON is valid.`;
    }
    parseAnalysisResponse(content, fileName) {
        try {
            // GPT-4.1 Nano returns clean JSON, so try direct parsing first
            const trimmedContent = content.trim();
            let parsed;
            try {
                // Direct parsing - this should work for GPT-4.1 Nano
                parsed = JSON.parse(trimmedContent);
                console.log('Direct JSON parsing successful');
            } catch (directParseError) {
                console.log('Direct JSON parsing failed, trying extraction methods...');
                console.log('Parse error:', directParseError);
                // If direct parsing fails, try to extract JSON from response
                let jsonStr = this.extractJSON(content);
                if (!jsonStr) {
                    console.warn('No valid JSON found in AI response, using fallback analysis');
                    return this.createBasicAnalysis(content, fileName);
                }
                try {
                    // Try parsing the extracted JSON without cleaning first
                    parsed = JSON.parse(jsonStr);
                    console.log('Extracted JSON parsing successful');
                } catch (extractedParseError) {
                    console.log('Extracted JSON parsing failed, trying cleanup...');
                    console.log('Extracted parse error:', extractedParseError);
                    console.log('JSON string before cleanup:', jsonStr.substring(0, 100) + '...');
                    // Only clean if extraction also fails
                    jsonStr = this.cleanupJSON(jsonStr);
                    console.log('JSON string after cleanup:', jsonStr.substring(0, 100) + '...');
                    parsed = JSON.parse(jsonStr);
                    console.log('Cleaned JSON parsing successful');
                }
            }
            // Validate and normalize the response structure
            const normalized = this.normalizeAnalysisResponse(parsed, fileName);
            return normalized;
        } catch (error) {
            console.error('Error parsing analysis response:', error);
            console.error('Raw content preview:', content.substring(0, 500) + '...');
            // Try to extract partial information if full JSON parsing fails
            const fallback = this.extractPartialAnalysis(content, fileName);
            if (fallback) {
                console.log('Using fallback partial analysis');
                return fallback;
            }
            console.log('Using basic analysis fallback');
            return this.createBasicAnalysis(content, fileName);
        }
    }
    extractPartialAnalysis(content, fileName) {
        try {
            // Try to extract individual sections even if the full JSON is malformed
            const result = {
                issues: [],
                suggestions: [],
                metrics: {
                    linesOfCode: 0,
                    complexity: 0,
                    maintainability: 5,
                    performance: 5
                }
            };
            // Try to extract issues
            const issuesMatch = content.match(/"issues"\s*:\s*\[([\s\S]*?)\]/);
            if (issuesMatch) {
                try {
                    const issuesJson = `[${issuesMatch[1]}]`;
                    const issues = JSON.parse(issuesJson);
                    if (Array.isArray(issues)) {
                        result.issues = issues.map((issue)=>({
                                type: issue.type || 'info',
                                message: issue.message || 'Issue detected',
                                file: fileName,
                                line: issue.line,
                                severity: issue.severity || 1
                            }));
                    }
                } catch (e) {
                    console.warn('Could not parse issues section');
                }
            }
            // Try to extract metrics
            const metricsMatch = content.match(/"metrics"\s*:\s*\{([^}]*)\}/);
            if (metricsMatch) {
                try {
                    const metricsJson = `{${metricsMatch[1]}}`;
                    const metrics = JSON.parse(metricsJson);
                    result.metrics = {
                        linesOfCode: metrics.linesOfCode || 0,
                        complexity: metrics.complexity || 0,
                        maintainability: metrics.maintainability || 5,
                        performance: metrics.performance || 5
                    };
                } catch (e) {
                    console.warn('Could not parse metrics section');
                }
            }
            return result;
        } catch (error) {
            console.warn('Could not extract partial analysis');
            return null;
        }
    }
    extractJSON(content) {
        // Try multiple patterns to find JSON
        const patterns = [
            /\{[\s\S]*\}/,
            /```json\s*(\{[\s\S]*?\})\s*```/,
            /```\s*(\{[\s\S]*?\})\s*```/
        ];
        for (const pattern of patterns){
            const match = content.match(pattern);
            if (match) {
                return match[1] || match[0];
            }
        }
        return null;
    }
    cleanupJSON(jsonStr) {
        // Remove common formatting issues
        let cleaned = jsonStr.replace(/,\s*}/g, '}') // Remove trailing commas in objects
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/'/g, '"') // Replace single quotes with double quotes
        .replace(/(\w+):/g, '"$1":') // Quote unquoted keys
        .replace(/"\s*"/g, '""') // Fix empty strings
        .replace(/\\'/g, "'") // Fix escaped single quotes
        .replace(/\\"/g, '\\"') // Ensure double quotes are properly escaped
        .replace(/\n/g, '\\n') // Escape newlines in strings
        .replace(/\r/g, '\\r') // Escape carriage returns
        .replace(/\t/g, '\\t') // Escape tabs
        .trim();
        // Fix common issues with property names that got over-quoted
        cleaned = cleaned.replace(/"(\w+)":/g, '"$1":');
        // Fix issues where quotes inside strings break JSON
        // This is a simple approach - find strings and escape internal quotes
        cleaned = this.fixQuotesInStrings(cleaned);
        return cleaned;
    }
    fixQuotesInStrings(jsonStr) {
        // This is a simplified approach to fix quotes in string values
        // It's not perfect but handles most common cases
        try {
            // Try to parse as-is first
            JSON.parse(jsonStr);
            return jsonStr;
        } catch (error) {
            // If parsing fails, try to fix common quote issues
            let fixed = jsonStr;
            // Replace unescaped quotes in string values (basic heuristic)
            fixed = fixed.replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1\\"$2\\"$3":');
            fixed = fixed.replace(/:\s*"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}])/g, ': "$1\\"$2\\"$3"');
            return fixed;
        }
    }
    normalizeAnalysisResponse(parsed, fileName) {
        const result = {
            issues: [],
            suggestions: [],
            metrics: {
                linesOfCode: 0,
                complexity: 0,
                maintainability: 0,
                performance: 0
            }
        };
        // Normalize issues
        if (Array.isArray(parsed.issues)) {
            result.issues = parsed.issues.map((issue)=>({
                    type: issue.type || 'info',
                    message: issue.message || 'No message provided',
                    file: fileName,
                    line: issue.line || undefined,
                    column: issue.column || undefined,
                    severity: issue.severity || 1
                }));
        }
        // Normalize suggestions
        if (Array.isArray(parsed.suggestions)) {
            result.suggestions = parsed.suggestions.map((suggestion)=>({
                    id: this.generateId(),
                    type: suggestion.type || 'refactor',
                    title: suggestion.title || 'Improvement suggestion',
                    description: suggestion.description || 'No description provided',
                    file: fileName,
                    originalCode: suggestion.originalCode || '',
                    suggestedCode: suggestion.suggestedCode || '',
                    confidence: Math.min(Math.max(suggestion.confidence || 0.5, 0), 1),
                    impact: suggestion.impact || 'medium',
                    estimatedBenefit: suggestion.estimatedBenefit || 'Improved code quality'
                }));
        }
        // Normalize metrics
        if (parsed.metrics && typeof parsed.metrics === 'object') {
            result.metrics = {
                linesOfCode: Math.max(parsed.metrics.linesOfCode || 0, 0),
                complexity: Math.max(parsed.metrics.complexity || 0, 0),
                maintainability: Math.min(Math.max(parsed.metrics.maintainability || 5, 0), 10),
                performance: Math.min(Math.max(parsed.metrics.performance || 5, 0), 10)
            };
        }
        return result;
    }
    parseImprovementResponse(content, fileName, type = 'refactor') {
        try {
            const trimmedContent = content.trim();
            let parsed;
            try {
                // Direct parsing for GPT-4.1 Nano clean JSON
                parsed = JSON.parse(trimmedContent);
            } catch (directParseError) {
                console.log('Direct improvement JSON parsing failed, trying extraction...');
                let jsonStr = this.extractJSON(content);
                if (!jsonStr) {
                    console.warn('No valid JSON found in improvement response');
                    return null;
                }
                try {
                    parsed = JSON.parse(jsonStr);
                } catch (extractedParseError) {
                    jsonStr = this.cleanupJSON(jsonStr);
                    parsed = JSON.parse(jsonStr);
                }
            }
            // Normalize the improvement response
            return {
                id: this.generateId(),
                type: type,
                file: fileName,
                title: parsed.title || 'Code Improvement',
                description: parsed.description || 'No description provided',
                originalCode: parsed.originalCode || '',
                suggestedCode: parsed.suggestedCode || '',
                confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1),
                impact: parsed.impact || 'medium',
                estimatedBenefit: parsed.estimatedBenefit || 'Improved code quality'
            };
        } catch (error) {
            console.error('Error parsing improvement response:', error);
            console.error('Raw content preview:', content.substring(0, 200) + '...');
            return null;
        }
    }
    createEmptyAnalysis() {
        return {
            issues: [],
            suggestions: [],
            metrics: {
                linesOfCode: 0,
                complexity: 0,
                maintainability: 0,
                performance: 0
            }
        };
    }
    createBasicAnalysis(code, fileName) {
        const lines = code.split('\n').length;
        const complexity = Math.min(Math.floor(lines / 10), 10);
        return {
            issues: [
                {
                    type: 'info',
                    message: 'AI analysis temporarily unavailable - showing basic metrics only',
                    file: fileName,
                    severity: 1
                }
            ],
            suggestions: [],
            metrics: {
                linesOfCode: lines,
                complexity: complexity,
                maintainability: Math.max(10 - complexity, 1),
                performance: 5
            }
        };
    }
    createFallbackSuggestion(fileName, improvementType, content) {
        return {
            id: this.generateId(),
            type: improvementType,
            title: `${improvementType.charAt(0).toUpperCase() + improvementType.slice(1)} Improvement`,
            description: 'AI generated an improvement suggestion but the response format was invalid. Please review the raw suggestion.',
            file: fileName,
            originalCode: '// Original code section',
            suggestedCode: content.substring(0, 500) + (content.length > 500 ? '...' : ''),
            confidence: 0.3,
            impact: 'low',
            estimatedBenefit: 'Manual review required due to parsing issues'
        };
    }
    getFileExtension(fileName) {
        const ext = fileName.split('.').pop()?.toLowerCase();
        switch(ext){
            case 'ts':
            case 'tsx':
                return 'typescript';
            case 'js':
            case 'jsx':
                return 'javascript';
            case 'css':
                return 'css';
            case 'json':
                return 'json';
            default:
                return 'text';
        }
    }
    generateId() {
        return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
}
const aiClient = new AIClient();
}}),
"[project]/src/lib/analyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CodeAnalyzer": (()=>CodeAnalyzer),
    "codeAnalyzer": (()=>codeAnalyzer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$acorn$2f$dist$2f$acorn$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/acorn/dist/acorn.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$parser$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/parser/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/filesystem.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-client.ts [app-route] (ecmascript)");
;
;
;
;
class CodeAnalyzer {
    analysisCache = new Map();
    async analyzeProject() {
        console.log('Starting project analysis...');
        const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory('src', true);
        const codeFiles = files.filter((file)=>file.type === 'file' && this.isAnalyzableFile(file.name));
        const fileAnalyses = [];
        const allSuggestions = [];
        for (const file of codeFiles){
            try {
                const analysis = await this.analyzeFile(file);
                fileAnalyses.push(analysis);
                allSuggestions.push(...analysis.analysis.suggestions);
            } catch (error) {
                console.error(`Error analyzing file ${file.path}:`, error);
            }
        }
        const overallMetrics = this.calculateProjectMetrics(fileAnalyses);
        return {
            files: fileAnalyses,
            overallMetrics,
            suggestions: allSuggestions,
            lastAnalyzed: new Date()
        };
    }
    async analyzeFile(file) {
        // Check cache first
        const cacheKey = `${file.path}_${file.lastModified?.getTime()}`;
        if (this.analysisCache.has(cacheKey)) {
            return this.analysisCache.get(cacheKey);
        }
        console.log(`Analyzing file: ${file.path}`);
        const content = file.content || await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(file.path);
        // Parse AST for JavaScript/TypeScript files
        let ast = null;
        let dependencies = [];
        let exports = [];
        if (this.isJavaScriptFile(file.name)) {
            try {
                ast = this.parseAST(content, file.name);
                dependencies = this.extractDependencies(ast);
                exports = this.extractExports(ast);
            } catch (error) {
                console.warn(`Could not parse AST for ${file.path}:`, error);
                // Continue without AST - the AI analysis will still work
                ast = null;
                dependencies = [];
                exports = [];
            }
        }
        // Get AI analysis
        const analysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiClient"].analyzeCode(content, file.path);
        const fileAnalysis = {
            file: {
                ...file,
                content
            },
            analysis,
            ast,
            dependencies,
            exports
        };
        // Cache the result
        this.analysisCache.set(cacheKey, fileAnalysis);
        return fileAnalysis;
    }
    async generateImprovements(file, improvementTypes = [
        'performance',
        'refactor',
        'style'
    ]) {
        const content = file.content || await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(file.path);
        const suggestions = [];
        for (const type of improvementTypes){
            try {
                const suggestion = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiClient"].generateImprovement(content, file.path, type, `Improve ${type} aspects of this code`);
                if (suggestion) {
                    suggestions.push(suggestion);
                }
            } catch (error) {
                console.error(`Error generating ${type} improvement for ${file.path}:`, error);
            }
        }
        return suggestions;
    }
    async generateNewFeature(description, targetFile) {
        let contextFile = targetFile;
        let contextCode = '';
        if (!contextFile) {
            // Find the most relevant file based on the description
            const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory('src', true);
            const mainFiles = files.filter((f)=>f.name.includes('page.tsx') || f.name.includes('index.tsx') || f.name.includes('App.tsx'));
            contextFile = mainFiles[0]?.path || 'src/app/page.tsx';
        }
        try {
            contextCode = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(contextFile);
        } catch (error) {
            console.warn(`Could not read context file ${contextFile}:`, error);
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiClient"].generateNewFeature(description, contextCode, contextFile);
    }
    parseAST(code, fileName) {
        const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
        const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');
        if (isTypeScript) {
            // Use Babel parser for TypeScript files
            try {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$parser$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(code, {
                    sourceType: 'module',
                    allowImportExportEverywhere: true,
                    allowReturnOutsideFunction: true,
                    plugins: [
                        'typescript',
                        ...isJSX ? [
                            'jsx'
                        ] : [],
                        'decorators-legacy',
                        'classProperties',
                        'objectRestSpread',
                        'asyncGenerators',
                        'functionBind',
                        'exportDefaultFrom',
                        'exportNamespaceFrom',
                        'dynamicImport',
                        'nullishCoalescingOperator',
                        'optionalChaining'
                    ]
                });
            } catch (error) {
                throw new Error(`Could not parse TypeScript file ${fileName}: ${error}`);
            }
        } else {
            // Use Acorn for JavaScript files
            try {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$acorn$2f$dist$2f$acorn$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(code, {
                    ecmaVersion: 2022,
                    sourceType: 'module',
                    allowImportExportEverywhere: true,
                    allowReturnOutsideFunction: true
                });
            } catch (error) {
                // If parsing fails, try with different options
                try {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$acorn$2f$dist$2f$acorn$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(code, {
                        ecmaVersion: 2022,
                        sourceType: 'script'
                    });
                } catch (secondError) {
                    throw new Error(`Could not parse JavaScript file ${fileName}: ${error}`);
                }
            }
        }
    }
    extractDependencies(ast) {
        const dependencies = [];
        if (!ast || !ast.body) return dependencies;
        for (const node of ast.body){
            if (node.type === 'ImportDeclaration' && node.source?.value) {
                dependencies.push(node.source.value);
            }
        }
        return [
            ...new Set(dependencies)
        ]; // Remove duplicates
    }
    extractExports(ast) {
        const exports = [];
        if (!ast || !ast.body) return exports;
        for (const node of ast.body){
            if (node.type === 'ExportNamedDeclaration') {
                if (node.declaration) {
                    if (node.declaration.type === 'FunctionDeclaration' && node.declaration.id) {
                        exports.push(node.declaration.id.name);
                    } else if (node.declaration.type === 'VariableDeclaration') {
                        for (const declarator of node.declaration.declarations){
                            if (declarator.id?.name) {
                                exports.push(declarator.id.name);
                            }
                        }
                    }
                }
                if (node.specifiers) {
                    for (const specifier of node.specifiers){
                        if (specifier.exported?.name) {
                            exports.push(specifier.exported.name);
                        }
                    }
                }
            } else if (node.type === 'ExportDefaultDeclaration') {
                exports.push('default');
            }
        }
        return [
            ...new Set(exports)
        ]; // Remove duplicates
    }
    calculateProjectMetrics(fileAnalyses) {
        const totalFiles = fileAnalyses.length;
        let totalLinesOfCode = 0;
        let totalComplexity = 0;
        let totalMaintainability = 0;
        let issueCount = 0;
        let suggestionCount = 0;
        for (const analysis of fileAnalyses){
            const metrics = analysis.analysis.metrics;
            totalLinesOfCode += metrics.linesOfCode;
            totalComplexity += metrics.complexity;
            totalMaintainability += metrics.maintainability;
            issueCount += analysis.analysis.issues.length;
            suggestionCount += analysis.analysis.suggestions.length;
        }
        return {
            totalFiles,
            totalLinesOfCode,
            averageComplexity: totalFiles > 0 ? totalComplexity / totalFiles : 0,
            averageMaintainability: totalFiles > 0 ? totalMaintainability / totalFiles : 0,
            issueCount,
            suggestionCount
        };
    }
    isAnalyzableFile(fileName) {
        const analyzableExtensions = [
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.css',
            '.json'
        ];
        return analyzableExtensions.some((ext)=>fileName.endsWith(ext));
    }
    isJavaScriptFile(fileName) {
        return fileName.endsWith('.js') || fileName.endsWith('.jsx') || fileName.endsWith('.ts') || fileName.endsWith('.tsx');
    }
    clearCache() {
        this.analysisCache.clear();
    }
    getCacheSize() {
        return this.analysisCache.size;
    }
}
const codeAnalyzer = new CodeAnalyzer();
}}),
"[project]/src/lib/safety.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SafetyManager": (()=>SafetyManager),
    "safetyManager": (()=>safetyManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/filesystem.ts [app-route] (ecmascript)");
;
class SafetyManager {
    appliedChanges = [];
    maxBackups = 50;
    async validateChange(suggestion) {
        console.log(`Validating change: ${suggestion.title}`);
        const checks = [];
        const warnings = [];
        const errors = [];
        // Syntax validation
        const syntaxCheck = await this.validateSyntax(suggestion);
        checks.push(syntaxCheck);
        if (!syntaxCheck.passed) {
            errors.push(syntaxCheck.message);
        }
        // Dependency validation
        const dependencyCheck = await this.validateDependencies(suggestion);
        checks.push(dependencyCheck);
        if (!dependencyCheck.passed) {
            if (dependencyCheck.severity === 'critical' || dependencyCheck.severity === 'high') {
                errors.push(dependencyCheck.message);
            } else {
                warnings.push(dependencyCheck.message);
            }
        }
        // Breaking change detection
        const breakingChangeCheck = await this.detectBreakingChanges(suggestion);
        checks.push(breakingChangeCheck);
        if (!breakingChangeCheck.passed) {
            warnings.push(breakingChangeCheck.message);
        }
        // Security validation
        const securityCheck = await this.validateSecurity(suggestion);
        checks.push(securityCheck);
        if (!securityCheck.passed) {
            if (securityCheck.severity === 'critical' || securityCheck.severity === 'high') {
                errors.push(securityCheck.message);
            } else {
                warnings.push(securityCheck.message);
            }
        }
        const isValid = checks.every((check)=>check.passed || check.severity === 'low');
        const canProceed = errors.length === 0;
        return {
            isValid,
            checks,
            canProceed,
            warnings,
            errors
        };
    }
    async applyChange(suggestion, force = false) {
        console.log(`Applying change: ${suggestion.title}`);
        // Validate the change first
        const validation = await this.validateChange(suggestion);
        if (!validation.canProceed && !force) {
            const appliedChange = {
                id: this.generateId(),
                suggestion,
                appliedAt: new Date(),
                backupId: '',
                validationResult: validation,
                success: false,
                error: 'Validation failed: ' + validation.errors.join(', ')
            };
            this.appliedChanges.push(appliedChange);
            return appliedChange;
        }
        // Create backup before applying changes
        const backupId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].createBackup([
            suggestion.file
        ], `Before applying: ${suggestion.title}`);
        validation.backupId = backupId;
        try {
            // Read current file content
            if (!await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].fileExists(suggestion.file)) {
                throw new Error('File does not exist');
            }
            // Apply the change
            let newContent;
            if (suggestion.originalCode && suggestion.originalCode.trim()) {
                // Replace specific code section
                newContent = currentContent.replace(suggestion.originalCode, suggestion.suggestedCode);
                // Verify the replacement actually happened
                if (newContent === currentContent) {
                    throw new Error('Original code not found in file - no changes made');
                }
            } else {
                // If no original code specified, append or replace entire file
                newContent = suggestion.suggestedCode;
            }
            // Write the new content
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].writeFile(suggestion.file, newContent);
            const appliedChange = {
                id: this.generateId(),
                suggestion,
                appliedAt: new Date(),
                backupId,
                validationResult: validation,
                success: true
            };
            this.appliedChanges.push(appliedChange);
            await this.cleanupOldBackups();
            console.log(`Successfully applied change: ${suggestion.title}`);
            return appliedChange;
        } catch (error) {
            // If application failed, restore from backup
            try {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].restoreBackup(backupId);
            } catch (restoreError) {
                console.error('Failed to restore backup after failed change:', restoreError);
            }
            const appliedChange = {
                id: this.generateId(),
                suggestion,
                appliedAt: new Date(),
                backupId,
                validationResult: validation,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
            this.appliedChanges.push(appliedChange);
            return appliedChange;
        }
    }
    async rollbackChange(changeId) {
        const change = this.appliedChanges.find((c)=>c.id === changeId);
        if (!change) {
            throw new Error(`Change ${changeId} not found`);
        }
        if (!change.success) {
            throw new Error('Cannot rollback a change that was not successfully applied');
        }
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].restoreBackup(change.backupId);
            console.log(`Successfully rolled back change: ${change.suggestion.title}`);
            return true;
        } catch (error) {
            console.error(`Failed to rollback change ${changeId}:`, error);
            return false;
        }
    }
    async validateSyntax(suggestion) {
        try {
            // Basic syntax validation for JavaScript/TypeScript
            if (this.isJavaScriptFile(suggestion.file)) {
                // Try to parse the suggested code
                const code = suggestion.suggestedCode;
                // Basic checks
                if (!code || code.trim().length === 0) {
                    return {
                        id: this.generateId(),
                        type: 'syntax',
                        passed: false,
                        message: 'Suggested code is empty',
                        severity: 'high'
                    };
                }
                // Check for balanced brackets
                if (!this.hasBalancedBrackets(code)) {
                    return {
                        id: this.generateId(),
                        type: 'syntax',
                        passed: false,
                        message: 'Unbalanced brackets in suggested code',
                        severity: 'high'
                    };
                }
            }
            return {
                id: this.generateId(),
                type: 'syntax',
                passed: true,
                message: 'Syntax validation passed',
                severity: 'low'
            };
        } catch (error) {
            return {
                id: this.generateId(),
                type: 'syntax',
                passed: false,
                message: `Syntax validation failed: ${error}`,
                severity: 'high'
            };
        }
    }
    async validateDependencies(suggestion) {
        try {
            const code = suggestion.suggestedCode;
            // Check for new imports that might not exist
            const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
            const matches = Array.from(code.matchAll(importRegex));
            for (const match of matches){
                const importPath = match[1];
                // Check if it's a relative import
                if (importPath.startsWith('./') || importPath.startsWith('../')) {
                    continue;
                }
                // Check if it's a known package (basic check)
                if (!this.isKnownPackage(importPath)) {
                    return {
                        id: this.generateId(),
                        type: 'dependency',
                        passed: false,
                        message: `Unknown dependency: ${importPath}`,
                        severity: 'medium'
                    };
                }
            }
            return {
                id: this.generateId(),
                type: 'dependency',
                passed: true,
                message: 'Dependency validation passed',
                severity: 'low'
            };
        } catch (error) {
            return {
                id: this.generateId(),
                type: 'dependency',
                passed: false,
                message: `Dependency validation failed: ${error}`,
                severity: 'medium'
            };
        }
    }
    async detectBreakingChanges(suggestion) {
        try {
            const originalCode = suggestion.originalCode;
            const suggestedCode = suggestion.suggestedCode;
            // Check for removed exports
            const originalExports = this.extractExports(originalCode);
            const suggestedExports = this.extractExports(suggestedCode);
            const removedExports = originalExports.filter((exp)=>!suggestedExports.includes(exp));
            if (removedExports.length > 0) {
                return {
                    id: this.generateId(),
                    type: 'breaking-change',
                    passed: false,
                    message: `Potentially breaking change: removed exports ${removedExports.join(', ')}`,
                    severity: 'medium'
                };
            }
            return {
                id: this.generateId(),
                type: 'breaking-change',
                passed: true,
                message: 'No breaking changes detected',
                severity: 'low'
            };
        } catch (error) {
            return {
                id: this.generateId(),
                type: 'breaking-change',
                passed: true,
                message: 'Breaking change detection skipped due to error',
                severity: 'low'
            };
        }
    }
    async validateSecurity(suggestion) {
        try {
            const code = suggestion.suggestedCode;
            // Basic security checks
            const securityIssues = [];
            // Check for eval usage
            if (code.includes('eval(')) {
                securityIssues.push('Usage of eval() detected');
            }
            // Check for innerHTML usage
            if (code.includes('innerHTML')) {
                securityIssues.push('Usage of innerHTML detected (potential XSS risk)');
            }
            // Check for document.write
            if (code.includes('document.write')) {
                securityIssues.push('Usage of document.write detected');
            }
            if (securityIssues.length > 0) {
                return {
                    id: this.generateId(),
                    type: 'security',
                    passed: false,
                    message: `Security concerns: ${securityIssues.join(', ')}`,
                    severity: 'medium'
                };
            }
            return {
                id: this.generateId(),
                type: 'security',
                passed: true,
                message: 'Security validation passed',
                severity: 'low'
            };
        } catch (error) {
            return {
                id: this.generateId(),
                type: 'security',
                passed: true,
                message: 'Security validation skipped due to error',
                severity: 'low'
            };
        }
    }
    hasBalancedBrackets(code) {
        const brackets = {
            '(': ')',
            '[': ']',
            '{': '}'
        };
        const stack = [];
        for (const char of code){
            if (char in brackets) {
                stack.push(char);
            } else if (Object.values(brackets).includes(char)) {
                const last = stack.pop();
                if (!last || brackets[last] !== char) {
                    return false;
                }
            }
        }
        return stack.length === 0;
    }
    extractExports(code) {
        const exports = [];
        const exportRegex = /export\s+(?:default\s+)?(?:function|class|const|let|var)\s+(\w+)/g;
        let match;
        while((match = exportRegex.exec(code)) !== null){
            exports.push(match[1]);
        }
        return exports;
    }
    isKnownPackage(packageName) {
        const knownPackages = [
            'react',
            'react-dom',
            'next',
            'typescript',
            'tailwindcss',
            'lucide-react',
            'openai',
            'fs-extra',
            'acorn'
        ];
        return knownPackages.some((pkg)=>packageName.startsWith(pkg));
    }
    isJavaScriptFile(fileName) {
        return fileName.endsWith('.js') || fileName.endsWith('.jsx') || fileName.endsWith('.ts') || fileName.endsWith('.tsx');
    }
    async cleanupOldBackups() {
        try {
            const backups = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listBackups();
            if (backups.length > this.maxBackups) {
                const oldBackups = backups.slice(this.maxBackups);
                // TODO: Implement backup cleanup
                console.log(`Should cleanup ${oldBackups.length} old backups`);
            }
        } catch (error) {
            console.warn('Failed to cleanup old backups:', error);
        }
    }
    generateId() {
        return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getAppliedChanges() {
        return [
            ...this.appliedChanges
        ];
    }
    getChangeById(id) {
        return this.appliedChanges.find((change)=>change.id === id);
    }
}
const safetyManager = new SafetyManager();
}}),
"[project]/src/lib/improver.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SelfImprover": (()=>SelfImprover),
    "selfImprover": (()=>selfImprover)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analyzer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/safety.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/filesystem.ts [app-route] (ecmascript)");
;
;
;
class SelfImprover {
    currentSession = null;
    sessions = [];
    defaultSettings = {
        autoApprove: false,
        maxChangesPerSession: 10,
        improvementTypes: [
            'performance',
            'refactor',
            'style',
            'bug-fix'
        ],
        minConfidence: 0.7,
        excludeFiles: [
            'node_modules',
            '.git',
            'dist',
            'build'
        ],
        requireManualApproval: true
    };
    async startImprovementSession(settings) {
        if (this.currentSession && this.currentSession.status === 'running') {
            throw new Error('An improvement session is already running');
        }
        const sessionSettings = {
            ...this.defaultSettings,
            ...settings
        };
        console.log('Starting new improvement session...');
        // Analyze the current project
        const analysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].analyzeProject();
        const session = {
            id: this.generateSessionId(),
            startedAt: new Date(),
            analysis,
            appliedChanges: [],
            status: 'running',
            settings: sessionSettings
        };
        this.currentSession = session;
        this.sessions.push(session);
        // Start the improvement process
        this.runImprovementProcess(session);
        return session;
    }
    async runImprovementProcess(session) {
        try {
            console.log(`Running improvement process for session ${session.id}`);
            // Debug: Log the analysis results
            console.log(`Analysis completed with ${session.analysis.suggestions.length} total suggestions`);
            console.log('Session settings:', {
                minConfidence: session.settings.minConfidence,
                improvementTypes: session.settings.improvementTypes,
                excludeFiles: session.settings.excludeFiles
            });
            // Get high-confidence suggestions from the analysis
            const allSuggestions = session.analysis.suggestions;
            console.log('All suggestions:', allSuggestions.map((s)=>({
                    title: s.title,
                    confidence: s.confidence,
                    type: s.type,
                    file: s.file
                })));
            const confidenceFiltered = allSuggestions.filter((s)=>s.confidence >= session.settings.minConfidence);
            console.log(`After confidence filter (>= ${session.settings.minConfidence}):`, confidenceFiltered.length);
            const typeFiltered = confidenceFiltered.filter((s)=>session.settings.improvementTypes.includes(s.type));
            console.log(`After type filter:`, typeFiltered.length);
            const suggestions = typeFiltered.filter((s)=>!this.isFileExcluded(s.file, session.settings.excludeFiles)).sort((a, b)=>b.confidence - a.confidence) // Sort by confidence descending
            .slice(0, session.settings.maxChangesPerSession);
            console.log(`Found ${suggestions.length} improvement suggestions after all filters`);
            for (const suggestion of suggestions){
                if (session.status !== 'running') {
                    break; // Session was cancelled or failed
                }
                try {
                    await this.processSuggestion(suggestion, session);
                } catch (error) {
                    console.error(`Error processing suggestion ${suggestion.id}:`, error);
                }
            }
            // Mark session as completed
            session.completedAt = new Date();
            session.status = 'completed';
            this.currentSession = null;
            console.log(`Improvement session ${session.id} completed with ${session.appliedChanges.length} changes applied`);
        } catch (error) {
            console.error(`Improvement session ${session.id} failed:`, error);
            session.status = 'failed';
            session.completedAt = new Date();
            this.currentSession = null;
        }
    }
    async processSuggestion(suggestion, session) {
        console.log(`Processing suggestion: ${suggestion.title}`);
        // Apply the change through the safety manager
        const appliedChange = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safetyManager"].applyChange(suggestion, session.settings.autoApprove);
        session.appliedChanges.push(appliedChange);
        if (appliedChange.success) {
            console.log(`Successfully applied: ${suggestion.title}`);
        } else {
            console.warn(`Failed to apply: ${suggestion.title} - ${appliedChange.error}`);
        }
    }
    async generateCustomImprovement(description, targetFile) {
        console.log(`Generating custom improvement: ${description}`);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].generateNewFeature(description, targetFile);
    }
    async applyCustomImprovement(suggestion, force = false) {
        console.log(`Applying custom improvement: ${suggestion.title}`);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safetyManager"].applyChange(suggestion, force);
    }
    async rollbackChange(changeId) {
        console.log(`Rolling back change: ${changeId}`);
        const success = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safetyManager"].rollbackChange(changeId);
        // Update session records
        for (const session of this.sessions){
            const change = session.appliedChanges.find((c)=>c.id === changeId);
            if (change) {
                break;
            }
        }
        return success;
    }
    cancelCurrentSession() {
        if (this.currentSession && this.currentSession.status === 'running') {
            this.currentSession.status = 'cancelled';
            this.currentSession.completedAt = new Date();
            this.currentSession = null;
            console.log('Current improvement session cancelled');
            return true;
        }
        return false;
    }
    getCurrentSession() {
        return this.currentSession;
    }
    getAllSessions() {
        return [
            ...this.sessions
        ];
    }
    getSessionById(sessionId) {
        return this.sessions.find((s)=>s.id === sessionId);
    }
    getStats() {
        const totalSessions = this.sessions.length;
        const completedSessions = this.sessions.filter((s)=>s.status === 'completed');
        const totalChangesApplied = this.sessions.reduce((total, session)=>total + session.appliedChanges.filter((c)=>c.success).length, 0);
        const totalChangesAttempted = this.sessions.reduce((total, session)=>total + session.appliedChanges.length, 0);
        const successRate = totalChangesAttempted > 0 ? totalChangesApplied / totalChangesAttempted : 0;
        const averageImprovementsPerSession = completedSessions.length > 0 ? totalChangesApplied / completedSessions.length : 0;
        const lastImprovement = this.sessions.flatMap((s)=>s.appliedChanges).filter((c)=>c.success).sort((a, b)=>b.appliedAt.getTime() - a.appliedAt.getTime())[0]?.appliedAt || null;
        // Calculate top improvement types
        const typeCount = {};
        this.sessions.forEach((session)=>{
            session.appliedChanges.filter((c)=>c.success).forEach((change)=>{
                typeCount[change.suggestion.type] = (typeCount[change.suggestion.type] || 0) + 1;
            });
        });
        const topImprovementTypes = Object.entries(typeCount).map(([type, count])=>({
                type,
                count
            })).sort((a, b)=>b.count - a.count).slice(0, 5);
        return {
            totalSessions,
            totalChangesApplied,
            successRate,
            averageImprovementsPerSession,
            lastImprovement,
            topImprovementTypes
        };
    }
    async exportSession(sessionId) {
        const session = this.getSessionById(sessionId);
        if (!session) {
            throw new Error(`Session ${sessionId} not found`);
        }
        const exportData = {
            session,
            exportedAt: new Date(),
            version: '1.0.0'
        };
        const exportPath = `exports/session_${sessionId}_${Date.now()}.json`;
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].writeFile(exportPath, JSON.stringify(exportData, null, 2));
        return exportPath;
    }
    isFileExcluded(filePath, excludePatterns) {
        return excludePatterns.some((pattern)=>filePath.includes(pattern));
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    updateSettings(newSettings) {
        this.defaultSettings = {
            ...this.defaultSettings,
            ...newSettings
        };
    }
    getSettings() {
        return {
            ...this.defaultSettings
        };
    }
}
const selfImprover = new SelfImprover();
}}),
"[project]/src/app/api/improve/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/improver.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/safety.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');
        switch(action){
            case 'current-session':
                const currentSession = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].getCurrentSession();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: currentSession
                });
            case 'sessions':
                const sessions = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].getAllSessions();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: sessions
                });
            case 'stats':
                const stats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].getStats();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: stats
                });
            case 'settings':
                const settings = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].getSettings();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: settings
                });
            case 'applied-changes':
                const changes = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safetyManager"].getAppliedChanges();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: changes
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Unknown action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Improve GET error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { action, data } = body;
        switch(action){
            case 'start-session':
                const { settings } = data || {};
                const session = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].startImprovementSession(settings);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: session
                });
            case 'cancel-session':
                const cancelled = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].cancelCurrentSession();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        cancelled
                    }
                });
            case 'apply-suggestion':
                const { suggestion, force } = data;
                const appliedChange = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].applyCustomImprovement(suggestion, force);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: appliedChange
                });
            case 'rollback-change':
                const { changeId } = data;
                const rollbackSuccess = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].rollbackChange(changeId);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        success: rollbackSuccess
                    }
                });
            case 'generate-custom':
                const { description, targetFile } = data;
                const customSuggestion = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].generateCustomImprovement(description, targetFile);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: customSuggestion
                });
            case 'update-settings':
                const { newSettings } = data;
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].updateSettings(newSettings);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    message: 'Settings updated'
                });
            case 'export-session':
                const { sessionId } = data;
                const exportPath = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$improver$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["selfImprover"].exportSession(sessionId);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: {
                        exportPath
                    }
                });
            case 'validate-change':
                const { suggestionToValidate } = data;
                const validation = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$safety$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["safetyManager"].validateChange(suggestionToValidate);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: validation
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Unknown action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Improve POST error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3fa81919._.js.map