import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs-extra';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'model':
        const currentModel = process.env.OPENROUTER_MODEL || 'anthropic/claude-3.5-sonnet';
        return NextResponse.json({
          success: true,
          data: { model: currentModel }
        });

      case 'status':
        const hasApiKey = !!process.env.OPENROUTER_API_KEY;
        const model = process.env.OPENROUTER_MODEL || 'anthropic/claude-3.5-sonnet';
        
        return NextResponse.json({
          success: true,
          data: {
            hasApiKey,
            model,
            configured: hasApiKey
          }
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Config GET error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'update-model':
        const { model } = data;
        if (!model) {
          return NextResponse.json({
            success: false,
            error: 'Model is required'
          }, { status: 400 });
        }

        // Update the .env.local file
        const envPath = path.join(process.cwd(), '.env.local');
        let envContent = '';
        
        try {
          envContent = await fs.readFile(envPath, 'utf-8');
        } catch (error) {
          // File doesn't exist, create new content
          envContent = '';
        }

        // Update or add the OPENROUTER_MODEL line
        const lines = envContent.split('\n');
        let modelLineFound = false;
        
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].startsWith('OPENROUTER_MODEL=')) {
            lines[i] = `OPENROUTER_MODEL=${model}`;
            modelLineFound = true;
            break;
          }
        }
        
        if (!modelLineFound) {
          lines.push(`OPENROUTER_MODEL=${model}`);
        }
        
        await fs.writeFile(envPath, lines.join('\n'));
        
        return NextResponse.json({
          success: true,
          message: 'Model configuration updated. Restart the application to apply changes.'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Config POST error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
