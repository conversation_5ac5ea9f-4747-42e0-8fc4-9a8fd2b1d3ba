{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  Activity, \n  Code, \n  TrendingUp, \n  AlertTriangle, \n  CheckCircle, \n  Clock,\n  FileText,\n  Zap\n} from 'lucide-react';\n\ninterface DashboardStats {\n  totalSessions: number;\n  totalChangesApplied: number;\n  successRate: number;\n  averageImprovementsPerSession: number;\n  lastImprovement: Date | null;\n  topImprovementTypes: { type: string; count: number }[];\n}\n\ninterface ProjectOverview {\n  totalFiles: number;\n  files: Array<{\n    name: string;\n    path: string;\n    size: number;\n    lastModified: string;\n  }>;\n}\n\nexport default function Dashboard() {\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [projectOverview, setProjectOverview] = useState<ProjectOverview | null>(null);\n  const [currentSession, setCurrentSession] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Load improvement stats\n      const statsResponse = await fetch('/api/improve?action=stats');\n      const statsData = await statsResponse.json();\n      \n      if (statsData.success) {\n        setStats(statsData.data);\n      }\n\n      // Load current session\n      const sessionResponse = await fetch('/api/improve?action=current-session');\n      const sessionData = await sessionResponse.json();\n      \n      if (sessionData.success) {\n        setCurrentSession(sessionData.data);\n      }\n\n      // Load project overview\n      const projectResponse = await fetch('/api/analyze');\n      const projectData = await projectResponse.json();\n      \n      if (projectData.success) {\n        setProjectOverview(projectData.data);\n      }\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startImprovementSession = async () => {\n    try {\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'start-session',\n          data: {\n            settings: {\n              autoApprove: false,\n              maxChangesPerSession: 5,\n              improvementTypes: ['performance', 'refactor', 'style'],\n              minConfidence: 0.7\n            }\n          }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setCurrentSession(data.data);\n        // Refresh stats after starting session\n        setTimeout(loadDashboardData, 1000);\n      } else {\n        setError(data.error || 'Failed to start improvement session');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to start session');\n    }\n  };\n\n  const cancelSession = async () => {\n    try {\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'cancel-session'\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setCurrentSession(null);\n        loadDashboardData();\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to cancel session');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600\">Loading dashboard...</span>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div className=\"flex items-center\">\n          <AlertTriangle className=\"h-5 w-5 text-red-600 mr-2\" />\n          <span className=\"text-red-800\">{error}</span>\n        </div>\n        <button\n          onClick={loadDashboardData}\n          className=\"mt-2 text-red-600 hover:text-red-800 underline\"\n        >\n          Try again\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          Dashboard\n        </h2>\n        <div className=\"flex space-x-3\">\n          {currentSession?.status === 'running' ? (\n            <button\n              onClick={cancelSession}\n              className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n            >\n              Cancel Session\n            </button>\n          ) : (\n            <button\n              onClick={startImprovementSession}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n            >\n              <Zap className=\"h-4 w-4\" />\n              <span>Start Auto-Improvement</span>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Current Session Status */}\n      {currentSession && (\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <Activity className=\"h-5 w-5 text-blue-600\" />\n              <div>\n                <h3 className=\"font-medium text-blue-900\">\n                  Improvement Session Active\n                </h3>\n                <p className=\"text-sm text-blue-700\">\n                  Started: {new Date(currentSession.startedAt).toLocaleString()}\n                </p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <div className=\"text-lg font-bold text-blue-900\">\n                {currentSession.appliedChanges?.length || 0}\n              </div>\n              <div className=\"text-sm text-blue-700\">Changes Applied</div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <Activity className=\"h-6 w-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                Total Sessions\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {stats?.totalSessions || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <CheckCircle className=\"h-6 w-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                Changes Applied\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {stats?.totalChangesApplied || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-yellow-100 rounded-lg\">\n              <TrendingUp className=\"h-6 w-6 text-yellow-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                Success Rate\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {stats ? Math.round(stats.successRate * 100) : 0}%\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <FileText className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                Code Files\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {projectOverview?.totalFiles || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity & Project Overview */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Top Improvement Types */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Top Improvement Types\n          </h3>\n          {stats?.topImprovementTypes && stats.topImprovementTypes.length > 0 ? (\n            <div className=\"space-y-3\">\n              {stats.topImprovementTypes.map((type, index) => (\n                <div key={type.type} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white capitalize\">\n                      {type.type.replace('-', ' ')}\n                    </span>\n                  </div>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {type.count}\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n              No improvements applied yet\n            </p>\n          )}\n        </div>\n\n        {/* Recent Files */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Recent Files\n          </h3>\n          {projectOverview?.files && projectOverview.files.length > 0 ? (\n            <div className=\"space-y-3\">\n              {projectOverview.files.slice(0, 5).map((file, index) => (\n                <div key={file.path} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Code className=\"h-4 w-4 text-gray-400\" />\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {file.name}\n                    </span>\n                  </div>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {Math.round(file.size / 1024)}KB\n                  </span>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n              No files found\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Last Improvement */}\n      {stats?.lastImprovement && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <Clock className=\"h-5 w-5 text-gray-400\" />\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                Last Improvement\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {new Date(stats.lastImprovement).toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,SAAS;YAET,yBAAyB;YACzB,MAAM,gBAAgB,MAAM,MAAM;YAClC,MAAM,YAAY,MAAM,cAAc,IAAI;YAE1C,IAAI,UAAU,OAAO,EAAE;gBACrB,SAAS,UAAU,IAAI;YACzB;YAEA,uBAAuB;YACvB,MAAM,kBAAkB,MAAM,MAAM;YACpC,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAE9C,IAAI,YAAY,OAAO,EAAE;gBACvB,kBAAkB,YAAY,IAAI;YACpC;YAEA,wBAAwB;YACxB,MAAM,kBAAkB,MAAM,MAAM;YACpC,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAE9C,IAAI,YAAY,OAAO,EAAE;gBACvB,mBAAmB,YAAY,IAAI;YACrC;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBACJ,UAAU;4BACR,aAAa;4BACb,sBAAsB;4BACtB,kBAAkB;gCAAC;gCAAe;gCAAY;6BAAQ;4BACtD,eAAe;wBACjB;oBACF;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,KAAK,IAAI;gBAC3B,uCAAuC;gBACvC,WAAW,mBAAmB;YAChC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;gBACV;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB;gBAClB;YACF;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAqB;;;;;;;;;;;;IAG3C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;8BAElC,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,WAAW,0BAC1B,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;iDAID,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAOb,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAG1C,8OAAC;4CAAE,WAAU;;gDAAwB;gDACzB,IAAI,KAAK,eAAe,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,eAAe,cAAc,EAAE,UAAU;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDACV,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDACV,OAAO,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;;gDACV,QAAQ,KAAK,KAAK,CAAC,MAAM,WAAW,GAAG,OAAO;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAuD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDACV,iBAAiB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;4BAGtE,OAAO,uBAAuB,MAAM,mBAAmB,CAAC,MAAM,GAAG,kBAChE,8OAAC;gCAAI,WAAU;0CACZ,MAAM,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEACb,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAG5B,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;;uCARL,KAAK,IAAI;;;;;;;;;qDAcvB,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAO5D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;4BAGtE,iBAAiB,SAAS,gBAAgB,KAAK,CAAC,MAAM,GAAG,kBACxD,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC5C,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,KAAK,IAAI;;;;;;;;;;;;0DAGd,8OAAC;gDAAK,WAAU;;oDACb,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG;oDAAM;;;;;;;;uCARxB,KAAK,IAAI;;;;;;;;;qDAcvB,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;;;;;;;YAQ7D,OAAO,iCACN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CACV,IAAI,KAAK,MAAM,eAAe,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/components/CodeViewer.tsx"], "sourcesContent": ["'use client';\n\nimport { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';\nimport { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';\nimport { useState, useEffect } from 'react';\nimport { Copy, Check } from 'lucide-react';\n\ninterface CodeViewerProps {\n  code: string;\n  language: string;\n  fileName?: string;\n  showLineNumbers?: boolean;\n  highlightLines?: number[];\n}\n\nexport default function CodeViewer({\n  code,\n  language,\n  fileName,\n  showLineNumbers = true,\n  highlightLines = []\n}: CodeViewerProps) {\n  const [copied, setCopied] = useState(false);\n  const [isDark, setIsDark] = useState(false);\n\n  useEffect(() => {\n    // Check if dark mode is enabled\n    const checkDarkMode = () => {\n      setIsDark(document.documentElement.classList.contains('dark'));\n    };\n    \n    checkDarkMode();\n    \n    // Listen for theme changes\n    const observer = new MutationObserver(checkDarkMode);\n    observer.observe(document.documentElement, {\n      attributes: true,\n      attributeFilter: ['class']\n    });\n    \n    return () => observer.disconnect();\n  }, []);\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(code);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy code:', err);\n    }\n  };\n\n  const customStyle = {\n    margin: 0,\n    padding: '1rem',\n    fontSize: '14px',\n    lineHeight: '1.5',\n    fontFamily: 'ui-monospace, SFMono-Regular, \"SF Mono\", Consolas, \"Liberation Mono\", Menlo, monospace',\n    ...(isDark ? vscDarkPlus : vs),\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* Header */}\n      {fileName && (\n        <div className=\"flex items-center justify-between px-4 py-2 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600\">\n          <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n            {fileName}\n          </span>\n          <button\n            onClick={copyToClipboard}\n            className=\"flex items-center space-x-1 px-2 py-1 text-xs bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 rounded transition-colors\"\n          >\n            {copied ? (\n              <>\n                <Check className=\"h-3 w-3\" />\n                <span>Copied!</span>\n              </>\n            ) : (\n              <>\n                <Copy className=\"h-3 w-3\" />\n                <span>Copy</span>\n              </>\n            )}\n          </button>\n        </div>\n      )}\n\n      {/* Code */}\n      <div className=\"overflow-auto max-h-96\">\n        <SyntaxHighlighter\n          language={language}\n          style={isDark ? vscDarkPlus : vs}\n          showLineNumbers={showLineNumbers}\n          customStyle={customStyle}\n          wrapLines={true}\n          lineProps={(lineNumber) => {\n            const style: React.CSSProperties = {};\n            if (highlightLines.includes(lineNumber)) {\n              style.backgroundColor = isDark ? 'rgba(255, 255, 0, 0.1)' : 'rgba(255, 255, 0, 0.2)';\n              style.display = 'block';\n              style.margin = '0 -1rem';\n              style.padding = '0 1rem';\n            }\n            return { style };\n          }}\n        >\n          {code}\n        </SyntaxHighlighter>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AALA;;;;;;AAee,SAAS,WAAW,EACjC,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,kBAAkB,IAAI,EACtB,iBAAiB,EAAE,EACH;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,MAAM,gBAAgB;YACpB,UAAU,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;QACxD;QAEA;QAEA,2BAA2B;QAC3B,MAAM,WAAW,IAAI,iBAAiB;QACtC,SAAS,OAAO,CAAC,SAAS,eAAe,EAAE;YACzC,YAAY;YACZ,iBAAiB;gBAAC;aAAQ;QAC5B;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,SAAS;QACT,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,GAAI,SAAS,iPAAA,CAAA,cAAW,GAAG,uNAAA,CAAA,KAAE;IAC/B;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAET,uBACC;;8CACE,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;yDAGR;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;0BAQhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0MAAA,CAAA,QAAiB;oBAChB,UAAU;oBACV,OAAO,SAAS,iPAAA,CAAA,cAAW,GAAG,uNAAA,CAAA,KAAE;oBAChC,iBAAiB;oBACjB,aAAa;oBACb,WAAW;oBACX,WAAW,CAAC;wBACV,MAAM,QAA6B,CAAC;wBACpC,IAAI,eAAe,QAAQ,CAAC,aAAa;4BACvC,MAAM,eAAe,GAAG,SAAS,2BAA2B;4BAC5D,MAAM,OAAO,GAAG;4BAChB,MAAM,MAAM,GAAG;4BACf,MAAM,OAAO,GAAG;wBAClB;wBACA,OAAO;4BAAE;wBAAM;oBACjB;8BAEC;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 944, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/components/CodeAnalyzer.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  FileText, \n  Search, \n  AlertTriangle, \n  CheckCircle, \n  Info,\n  TrendingUp,\n  Code,\n  Folder,\n  FolderOpen\n} from 'lucide-react';\nimport <PERSON>Viewer from './CodeViewer';\n\ninterface FileInfo {\n  name: string;\n  path: string;\n  type: 'file' | 'directory';\n  size?: number;\n  lastModified?: string;\n  content?: string;\n}\n\ninterface CodeIssue {\n  type: 'error' | 'warning' | 'info';\n  message: string;\n  file: string;\n  line?: number;\n  severity: number;\n}\n\ninterface CodeSuggestion {\n  id: string;\n  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';\n  title: string;\n  description: string;\n  file: string;\n  originalCode: string;\n  suggestedCode: string;\n  confidence: number;\n  impact: 'low' | 'medium' | 'high';\n}\n\ninterface FileAnalysis {\n  file: FileInfo;\n  analysis: {\n    issues: CodeIssue[];\n    suggestions: CodeSuggestion[];\n    metrics: {\n      linesOfCode: number;\n      complexity: number;\n      maintainability: number;\n      performance: number;\n    };\n  };\n}\n\nexport default function CodeAnalyzer() {\n  const [files, setFiles] = useState<FileInfo[]>([]);\n  const [selectedFile, setSelectedFile] = useState<FileInfo | null>(null);\n  const [fileAnalysis, setFileAnalysis] = useState<FileAnalysis | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [analyzing, setAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['src']));\n\n  useEffect(() => {\n    loadFiles();\n  }, []);\n\n  const loadFiles = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/files?action=list&recursive=true');\n      const data = await response.json();\n\n      if (data.success) {\n        setFiles(data.data);\n      } else {\n        setError(data.error || 'Failed to load files');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load files');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const analyzeFile = async (file: FileInfo) => {\n    try {\n      setAnalyzing(true);\n      setError(null);\n\n      const response = await fetch(`/api/analyze?file=${encodeURIComponent(file.path)}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setFileAnalysis(data.data);\n      } else {\n        setError(data.error || 'Failed to analyze file');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to analyze file');\n    } finally {\n      setAnalyzing(false);\n    }\n  };\n\n  const handleFileSelect = async (file: FileInfo) => {\n    if (file.type === 'directory') {\n      toggleFolder(file.path);\n      return;\n    }\n\n    setSelectedFile(file);\n    setFileAnalysis(null);\n\n    // Load file content if not already loaded\n    if (!file.content) {\n      try {\n        const response = await fetch(`/api/files?action=read&path=${encodeURIComponent(file.path)}`);\n        const data = await response.json();\n        \n        if (data.success) {\n          file.content = data.data.content;\n        }\n      } catch (err) {\n        console.error('Failed to load file content:', err);\n      }\n    }\n\n    // Auto-analyze if it's a code file\n    if (isCodeFile(file.name)) {\n      await analyzeFile(file);\n    }\n  };\n\n  const toggleFolder = (folderPath: string) => {\n    const newExpanded = new Set(expandedFolders);\n    if (newExpanded.has(folderPath)) {\n      newExpanded.delete(folderPath);\n    } else {\n      newExpanded.add(folderPath);\n    }\n    setExpandedFolders(newExpanded);\n  };\n\n  const isCodeFile = (fileName: string): boolean => {\n    const codeExtensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.json'];\n    return codeExtensions.some(ext => fileName.endsWith(ext));\n  };\n\n  const getFileIcon = (file: FileInfo) => {\n    if (file.type === 'directory') {\n      return expandedFolders.has(file.path) ? FolderOpen : Folder;\n    }\n    return FileText;\n  };\n\n  const getIssueIcon = (type: string) => {\n    switch (type) {\n      case 'error':\n        return AlertTriangle;\n      case 'warning':\n        return AlertTriangle;\n      case 'info':\n        return Info;\n      default:\n        return Info;\n    }\n  };\n\n  const getIssueColor = (type: string) => {\n    switch (type) {\n      case 'error':\n        return 'text-red-600';\n      case 'warning':\n        return 'text-yellow-600';\n      case 'info':\n        return 'text-blue-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const renderFileTree = (fileList: FileInfo[], level: number = 0) => {\n    const grouped = fileList.reduce((acc, file) => {\n      const pathParts = file.path.split('/');\n      const currentLevel = pathParts[level];\n      \n      if (!currentLevel) return acc;\n      \n      if (!acc[currentLevel]) {\n        acc[currentLevel] = [];\n      }\n      acc[currentLevel].push(file);\n      return acc;\n    }, {} as Record<string, FileInfo[]>);\n\n    return Object.entries(grouped).map(([name, files]) => {\n      const isDirectory = files.some(f => f.path.split('/').length > level + 1);\n      const currentPath = files[0].path.split('/').slice(0, level + 1).join('/');\n      \n      if (isDirectory) {\n        const isExpanded = expandedFolders.has(currentPath);\n        return (\n          <div key={currentPath}>\n            <div\n              className=\"flex items-center space-x-2 py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded\"\n              style={{ paddingLeft: `${level * 16 + 8}px` }}\n              onClick={() => toggleFolder(currentPath)}\n            >\n              {isExpanded ? (\n                <FolderOpen className=\"h-4 w-4 text-blue-600\" />\n              ) : (\n                <Folder className=\"h-4 w-4 text-blue-600\" />\n              )}\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {name}\n              </span>\n            </div>\n            {isExpanded && renderFileTree(files, level + 1)}\n          </div>\n        );\n      } else {\n        const file = files[0];\n        return (\n          <div\n            key={file.path}\n            className={`flex items-center space-x-2 py-1 px-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer rounded ${\n              selectedFile?.path === file.path ? 'bg-blue-50 dark:bg-blue-900' : ''\n            }`}\n            style={{ paddingLeft: `${level * 16 + 8}px` }}\n            onClick={() => handleFileSelect(file)}\n          >\n            <FileText className=\"h-4 w-4 text-gray-600\" />\n            <span className=\"text-sm text-gray-900 dark:text-white\">\n              {name}\n            </span>\n            {file.size && (\n              <span className=\"text-xs text-gray-500 ml-auto\">\n                {Math.round(file.size / 1024)}KB\n              </span>\n            )}\n          </div>\n        );\n      }\n    });\n  };\n\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]\">\n      {/* File Tree */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Project Files\n          </h3>\n        </div>\n        <div className=\"p-2 overflow-y-auto h-full\">\n          {loading ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n            </div>\n          ) : (\n            renderFileTree(files)\n          )}\n        </div>\n      </div>\n\n      {/* File Content */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n            {selectedFile ? selectedFile.name : 'Select a file'}\n          </h3>\n        </div>\n        <div className=\"overflow-y-auto h-full\">\n          {selectedFile && selectedFile.content ? (\n            <CodeViewer\n              code={selectedFile.content}\n              language={getLanguageFromFileName(selectedFile.name)}\n              fileName={selectedFile.name}\n            />\n          ) : (\n            <div className=\"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\">\n              Select a file to view its content\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Analysis Results */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Analysis Results\n          </h3>\n        </div>\n        <div className=\"p-4 overflow-y-auto h-full\">\n          {analyzing ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n              <span className=\"ml-2 text-gray-600\">Analyzing...</span>\n            </div>\n          ) : fileAnalysis ? (\n            <div className=\"space-y-6\">\n              {/* Metrics */}\n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                  Code Metrics\n                </h4>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                    <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {fileAnalysis.analysis.metrics.linesOfCode}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      Lines of Code\n                    </div>\n                  </div>\n                  <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                    <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {fileAnalysis.analysis.metrics.complexity}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      Complexity\n                    </div>\n                  </div>\n                  <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                    <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {fileAnalysis.analysis.metrics.maintainability}/10\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      Maintainability\n                    </div>\n                  </div>\n                  <div className=\"text-center p-3 bg-gray-50 dark:bg-gray-700 rounded\">\n                    <div className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                      {fileAnalysis.analysis.metrics.performance}/10\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      Performance\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Issues */}\n              {fileAnalysis.analysis.issues.length > 0 && (\n                <div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                    Issues ({fileAnalysis.analysis.issues.length})\n                  </h4>\n                  <div className=\"space-y-2\">\n                    {fileAnalysis.analysis.issues.map((issue, index) => {\n                      const Icon = getIssueIcon(issue.type);\n                      return (\n                        <div\n                          key={index}\n                          className=\"flex items-start space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded\"\n                        >\n                          <Icon className={`h-4 w-4 mt-0.5 ${getIssueColor(issue.type)}`} />\n                          <div className=\"flex-1\">\n                            <p className=\"text-sm text-gray-900 dark:text-white\">\n                              {issue.message}\n                            </p>\n                            {issue.line && (\n                              <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                                Line {issue.line}\n                              </p>\n                            )}\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              )}\n\n              {/* Suggestions */}\n              {fileAnalysis.analysis.suggestions.length > 0 && (\n                <div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                    Suggestions ({fileAnalysis.analysis.suggestions.length})\n                  </h4>\n                  <div className=\"space-y-3\">\n                    {fileAnalysis.analysis.suggestions.map((suggestion) => (\n                      <div\n                        key={suggestion.id}\n                        className=\"p-3 bg-blue-50 dark:bg-blue-900 rounded border border-blue-200 dark:border-blue-700\"\n                      >\n                        <div className=\"flex items-center justify-between mb-2\">\n                          <h5 className=\"font-medium text-blue-900 dark:text-blue-100\">\n                            {suggestion.title}\n                          </h5>\n                          <span className=\"text-xs bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded\">\n                            {Math.round(suggestion.confidence * 100)}%\n                          </span>\n                        </div>\n                        <p className=\"text-sm text-blue-800 dark:text-blue-200 mb-2\">\n                          {suggestion.description}\n                        </p>\n                        <div className=\"flex items-center space-x-2 text-xs text-blue-700 dark:text-blue-300\">\n                          <span className=\"capitalize\">{suggestion.type}</span>\n                          <span>•</span>\n                          <span className=\"capitalize\">{suggestion.impact} impact</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-center h-full text-gray-500 dark:text-gray-400\">\n              {selectedFile && isCodeFile(selectedFile.name)\n                ? 'Click analyze to see results'\n                : 'Select a code file to analyze'\n              }\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction getLanguageFromFileName(fileName: string): string {\n  const ext = fileName.split('.').pop()?.toLowerCase();\n  switch (ext) {\n    case 'ts':\n    case 'tsx':\n      return 'typescript';\n    case 'js':\n    case 'jsx':\n      return 'javascript';\n    case 'css':\n      return 'css';\n    case 'json':\n      return 'json';\n    default:\n      return 'text';\n  }\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AA2De,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI,IAAI;QAAC;KAAM;IAEnF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,aAAa;YACb,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,KAAK,IAAI,GAAG;YACjF,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,gBAAgB,KAAK,IAAI;YAC3B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,aAAa,KAAK,IAAI;YACtB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,0CAA0C;QAC1C,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,4BAA4B,EAAE,mBAAmB,KAAK,IAAI,GAAG;gBAC3F,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,KAAK,OAAO,GAAG,KAAK,IAAI,CAAC,OAAO;gBAClC;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA,mCAAmC;QACnC,IAAI,WAAW,KAAK,IAAI,GAAG;YACzB,MAAM,YAAY;QACpB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,aAAa;YAC/B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,mBAAmB;IACrB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,iBAAiB;YAAC;YAAO;YAAQ;YAAO;YAAQ;YAAQ;SAAQ;QACtE,OAAO,eAAe,IAAI,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC;IACtD;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,OAAO,gBAAgB,GAAG,CAAC,KAAK,IAAI,IAAI,kNAAA,CAAA,aAAU,GAAG,sMAAA,CAAA,SAAM;QAC7D;QACA,OAAO,8MAAA,CAAA,WAAQ;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO,wNAAA,CAAA,gBAAa;YACtB,KAAK;gBACH,OAAO,wNAAA,CAAA,gBAAa;YACtB,KAAK;gBACH,OAAO,kMAAA,CAAA,OAAI;YACb;gBACE,OAAO,kMAAA,CAAA,OAAI;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC,UAAsB,QAAgB,CAAC;QAC7D,MAAM,UAAU,SAAS,MAAM,CAAC,CAAC,KAAK;YACpC,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC;YAClC,MAAM,eAAe,SAAS,CAAC,MAAM;YAErC,IAAI,CAAC,cAAc,OAAO;YAE1B,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE;gBACtB,GAAG,CAAC,aAAa,GAAG,EAAE;YACxB;YACA,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;YACvB,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM;YAC/C,MAAM,cAAc,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM,GAAG,QAAQ;YACvE,MAAM,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;YAEtE,IAAI,aAAa;gBACf,MAAM,aAAa,gBAAgB,GAAG,CAAC;gBACvC,qBACE,8OAAC;;sCACC,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,aAAa,GAAG,QAAQ,KAAK,EAAE,EAAE,CAAC;4BAAC;4BAC5C,SAAS,IAAM,aAAa;;gCAE3B,2BACC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;yDAEtB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAEpB,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;wBAGJ,cAAc,eAAe,OAAO,QAAQ;;mBAfrC;;;;;YAkBd,OAAO;gBACL,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,qBACE,8OAAC;oBAEC,WAAW,CAAC,sGAAsG,EAChH,cAAc,SAAS,KAAK,IAAI,GAAG,gCAAgC,IACnE;oBACF,OAAO;wBAAE,aAAa,GAAG,QAAQ,KAAK,EAAE,EAAE,CAAC;oBAAC;oBAC5C,SAAS,IAAM,iBAAiB;;sCAEhC,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCACb;;;;;;wBAEF,KAAK,IAAI,kBACR,8OAAC;4BAAK,WAAU;;gCACb,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG;gCAAM;;;;;;;;mBAb7B,KAAK,IAAI;;;;;YAkBpB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;;;;;;kCAIpE,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;mCAGjB,eAAe;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,eAAe,aAAa,IAAI,GAAG;;;;;;;;;;;kCAGxC,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,aAAa,OAAO,iBACnC,8OAAC,gIAAA,CAAA,UAAU;4BACT,MAAM,aAAa,OAAO;4BAC1B,UAAU,wBAAwB,aAAa,IAAI;4BACnD,UAAU,aAAa,IAAI;;;;;iDAG7B,8OAAC;4BAAI,WAAU;sCAA2E;;;;;;;;;;;;;;;;;0BAQhG,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;;;;;;kCAIpE,8OAAC;wBAAI,WAAU;kCACZ,0BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;mCAErC,6BACF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;sDAG/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,aAAa,QAAQ,CAAC,OAAO,CAAC,WAAW;;;;;;sEAE5C,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,aAAa,QAAQ,CAAC,OAAO,CAAC,UAAU;;;;;;sEAE3C,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,aAAa,QAAQ,CAAC,OAAO,CAAC,eAAe;gEAAC;;;;;;;sEAEjD,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,aAAa,QAAQ,CAAC,OAAO,CAAC,WAAW;gEAAC;;;;;;;sEAE7C,8OAAC;4DAAI,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;gCAQ/D,aAAa,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,mBACrC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAiD;gDACpD,aAAa,QAAQ,CAAC,MAAM,CAAC,MAAM;gDAAC;;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;sDACZ,aAAa,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;gDACxC,MAAM,OAAO,aAAa,MAAM,IAAI;gDACpC,qBACE,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAK,WAAW,CAAC,eAAe,EAAE,cAAc,MAAM,IAAI,GAAG;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,MAAM,OAAO;;;;;;gEAEf,MAAM,IAAI,kBACT,8OAAC;oEAAE,WAAU;;wEAA2C;wEAChD,MAAM,IAAI;;;;;;;;;;;;;;mDAVjB;;;;;4CAgBX;;;;;;;;;;;;gCAML,aAAa,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,mBAC1C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAiD;gDAC/C,aAAa,QAAQ,CAAC,WAAW,CAAC,MAAM;gDAAC;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;sDACZ,aAAa,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,2BACtC,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,WAAW,KAAK;;;;;;8EAEnB,8OAAC;oEAAK,WAAU;;wEACb,KAAK,KAAK,CAAC,WAAW,UAAU,GAAG;wEAAK;;;;;;;;;;;;;sEAG7C,8OAAC;4DAAE,WAAU;sEACV,WAAW,WAAW;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAc,WAAW,IAAI;;;;;;8EAC7C,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;;wEAAc,WAAW,MAAM;wEAAC;;;;;;;;;;;;;;mDAjB7C,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;iDA0B9B,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,WAAW,aAAa,IAAI,IACzC,iCACA;;;;;;;;;;;;;;;;;;;;;;;AAQlB;AAEA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IACvC,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/components/ImprovementCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  Brain, \n  Play, \n  Pause, \n  CheckCircle, \n  XCircle, \n  AlertTriangle,\n  TrendingUp,\n  Code,\n  Lightbulb,\n  Settings\n} from 'lucide-react';\nimport CodeViewer from './CodeViewer';\n\ninterface CodeSuggestion {\n  id: string;\n  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';\n  title: string;\n  description: string;\n  file: string;\n  originalCode: string;\n  suggestedCode: string;\n  confidence: number;\n  impact: 'low' | 'medium' | 'high';\n  estimatedBenefit: string;\n}\n\ninterface ImprovementSession {\n  id: string;\n  startedAt: string;\n  completedAt?: string;\n  status: 'running' | 'completed' | 'failed' | 'cancelled';\n  appliedChanges: any[];\n}\n\nexport default function ImprovementCenter() {\n  const [suggestions, setSuggestions] = useState<CodeSuggestion[]>([]);\n  const [currentSession, setCurrentSession] = useState<ImprovementSession | null>(null);\n  const [selectedSuggestion, setSelectedSuggestion] = useState<CodeSuggestion | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [applying, setApplying] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  const [customDescription, setCustomDescription] = useState('');\n  const [generatingCustom, setGeneratingCustom] = useState(false);\n\n  useEffect(() => {\n    loadCurrentSession();\n    loadSuggestions();\n  }, []);\n\n  const loadCurrentSession = async () => {\n    try {\n      const response = await fetch('/api/improve?action=current-session');\n      const data = await response.json();\n      \n      if (data.success && data.data) {\n        setCurrentSession(data.data);\n      }\n    } catch (err) {\n      console.error('Failed to load current session:', err);\n    }\n  };\n\n  const loadSuggestions = async () => {\n    try {\n      setLoading(true);\n      \n      // Get suggestions from the latest analysis\n      const response = await fetch('/api/analyze?full=true');\n      const data = await response.json();\n      \n      if (data.success && data.data.suggestions) {\n        setSuggestions(data.data.suggestions);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load suggestions');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const startImprovementSession = async () => {\n    try {\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'start-session',\n          data: {\n            settings: {\n              autoApprove: false,\n              maxChangesPerSession: 10,\n              improvementTypes: ['performance', 'refactor', 'style', 'bug-fix'],\n              minConfidence: 0.6\n            }\n          }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setCurrentSession(data.data);\n        // Refresh suggestions\n        setTimeout(loadSuggestions, 2000);\n      } else {\n        setError(data.error || 'Failed to start improvement session');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to start session');\n    }\n  };\n\n  const cancelSession = async () => {\n    try {\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'cancel-session'\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setCurrentSession(null);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to cancel session');\n    }\n  };\n\n  const applySuggestion = async (suggestion: CodeSuggestion) => {\n    try {\n      setApplying(suggestion.id);\n      setError(null);\n\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'apply-suggestion',\n          data: { suggestion, force: false }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        if (data.data.success) {\n          // Remove applied suggestion from list\n          setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));\n          // Refresh session data\n          loadCurrentSession();\n        } else {\n          setError(data.data.error || 'Failed to apply suggestion');\n        }\n      } else {\n        setError(data.error || 'Failed to apply suggestion');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to apply suggestion');\n    } finally {\n      setApplying(null);\n    }\n  };\n\n  const generateCustomImprovement = async () => {\n    if (!customDescription.trim()) return;\n\n    try {\n      setGeneratingCustom(true);\n      setError(null);\n\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'generate-custom',\n          data: {\n            description: customDescription,\n            targetFile: 'src/app/page.tsx' // Default target\n          }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success && data.data) {\n        // Add the custom suggestion to the list\n        setSuggestions(prev => [data.data, ...prev]);\n        setCustomDescription('');\n      } else {\n        setError(data.error || 'Failed to generate custom improvement');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to generate improvement');\n    } finally {\n      setGeneratingCustom(false);\n    }\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'performance':\n        return TrendingUp;\n      case 'refactor':\n        return Code;\n      case 'feature':\n        return Lightbulb;\n      case 'bug-fix':\n        return AlertTriangle;\n      case 'style':\n        return Settings;\n      default:\n        return Code;\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'performance':\n        return 'text-green-600 bg-green-100';\n      case 'refactor':\n        return 'text-blue-600 bg-blue-100';\n      case 'feature':\n        return 'text-purple-600 bg-purple-100';\n      case 'bug-fix':\n        return 'text-red-600 bg-red-100';\n      case 'style':\n        return 'text-yellow-600 bg-yellow-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getImpactColor = (impact: string) => {\n    switch (impact) {\n      case 'high':\n        return 'text-red-600 bg-red-100';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'low':\n        return 'text-green-600 bg-green-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          Improvement Center\n        </h2>\n        <div className=\"flex space-x-3\">\n          {currentSession?.status === 'running' ? (\n            <button\n              onClick={cancelSession}\n              className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2\"\n            >\n              <Pause className=\"h-4 w-4\" />\n              <span>Cancel Session</span>\n            </button>\n          ) : (\n            <button\n              onClick={startImprovementSession}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n            >\n              <Play className=\"h-4 w-4\" />\n              <span>Start Auto-Improvement</span>\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Current Session Status */}\n      {currentSession && (\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <Brain className=\"h-5 w-5 text-blue-600\" />\n              <div>\n                <h3 className=\"font-medium text-blue-900\">\n                  Session {currentSession.status}\n                </h3>\n                <p className=\"text-sm text-blue-700\">\n                  Started: {new Date(currentSession.startedAt).toLocaleString()}\n                </p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <div className=\"text-lg font-bold text-blue-900\">\n                {currentSession.appliedChanges?.length || 0}\n              </div>\n              <div className=\"text-sm text-blue-700\">Changes Applied</div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Custom Improvement Generator */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n          Generate Custom Improvement\n        </h3>\n        <div className=\"flex space-x-3\">\n          <input\n            type=\"text\"\n            value={customDescription}\n            onChange={(e) => setCustomDescription(e.target.value)}\n            placeholder=\"Describe the improvement you want (e.g., 'Add a dark mode toggle button')\"\n            className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n            onKeyPress={(e) => e.key === 'Enter' && generateCustomImprovement()}\n          />\n          <button\n            onClick={generateCustomImprovement}\n            disabled={!customDescription.trim() || generatingCustom}\n            className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\"\n          >\n            {generatingCustom ? (\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n            ) : (\n              <Lightbulb className=\"h-4 w-4\" />\n            )}\n            <span>Generate</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <XCircle className=\"h-5 w-5 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Suggestions Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Suggestions List */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Available Improvements ({suggestions.length})\n          </h3>\n          \n          {loading ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n              <span className=\"ml-2 text-gray-600\">Loading suggestions...</span>\n            </div>\n          ) : suggestions.length === 0 ? (\n            <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n              No improvement suggestions available.\n              <br />\n              Try running a code analysis first.\n            </div>\n          ) : (\n            suggestions.map((suggestion) => {\n              const TypeIcon = getTypeIcon(suggestion.type);\n              return (\n                <div\n                  key={suggestion.id}\n                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                    selectedSuggestion?.id === suggestion.id\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'\n                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'\n                  }`}\n                  onClick={() => setSelectedSuggestion(suggestion)}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <div className={`p-1 rounded ${getTypeColor(suggestion.type)}`}>\n                        <TypeIcon className=\"h-4 w-4\" />\n                      </div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {suggestion.title}\n                      </h4>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`text-xs px-2 py-1 rounded ${getImpactColor(suggestion.impact)}`}>\n                        {suggestion.impact}\n                      </span>\n                      <span className=\"text-xs text-gray-500\">\n                        {Math.round(suggestion.confidence * 100)}%\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n                    {suggestion.description}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {suggestion.file}\n                    </span>\n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        applySuggestion(suggestion);\n                      }}\n                      disabled={applying === suggestion.id}\n                      className=\"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1\"\n                    >\n                      {applying === suggestion.id ? (\n                        <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"></div>\n                      ) : (\n                        <CheckCircle className=\"h-3 w-3\" />\n                      )}\n                      <span>Apply</span>\n                    </button>\n                  </div>\n                </div>\n              );\n            })\n          )}\n        </div>\n\n        {/* Selected Suggestion Details */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {selectedSuggestion ? 'Improvement Details' : 'Select an improvement'}\n            </h3>\n          </div>\n          \n          {selectedSuggestion ? (\n            <div className=\"p-4 space-y-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  Description\n                </h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {selectedSuggestion.description}\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  Expected Benefit\n                </h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {selectedSuggestion.estimatedBenefit}\n                </p>\n              </div>\n              \n              {selectedSuggestion.originalCode && (\n                <div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                    Current Code\n                  </h4>\n                  <CodeViewer\n                    code={selectedSuggestion.originalCode}\n                    language=\"typescript\"\n                    showLineNumbers={false}\n                  />\n                </div>\n              )}\n              \n              <div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  Suggested Code\n                </h4>\n                <CodeViewer\n                  code={selectedSuggestion.suggestedCode}\n                  language=\"typescript\"\n                  showLineNumbers={false}\n                />\n              </div>\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-center h-64 text-gray-500 dark:text-gray-400\">\n              Select an improvement to see details\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAfA;;;;;AAsCe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,kBAAkB,KAAK,IAAI;YAC7B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YAEX,2CAA2C;YAC3C,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;gBACzC,eAAe,KAAK,IAAI,CAAC,WAAW;YACtC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBACJ,UAAU;4BACR,aAAa;4BACb,sBAAsB;4BACtB,kBAAkB;gCAAC;gCAAe;gCAAY;gCAAS;6BAAU;4BACjE,eAAe;wBACjB;oBACF;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB,KAAK,IAAI;gBAC3B,sBAAsB;gBACtB,WAAW,iBAAiB;YAC9B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;gBACV;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,kBAAkB;YACpB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,YAAY,WAAW,EAAE;YACzB,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE;wBAAY,OAAO;oBAAM;gBACnC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE;oBACrB,sCAAsC;oBACtC,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;oBAC9D,uBAAuB;oBACvB;gBACF,OAAO;oBACL,SAAS,KAAK,IAAI,CAAC,KAAK,IAAI;gBAC9B;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI,CAAC,kBAAkB,IAAI,IAAI;QAE/B,IAAI;YACF,oBAAoB;YACpB,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBACJ,aAAa;wBACb,YAAY,mBAAmB,iBAAiB;oBAClD;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,wCAAwC;gBACxC,eAAe,CAAA,OAAQ;wBAAC,KAAK,IAAI;2BAAK;qBAAK;gBAC3C,qBAAqB;YACvB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO,kNAAA,CAAA,aAAU;YACnB,KAAK;gBACH,OAAO,kMAAA,CAAA,OAAI;YACb,KAAK;gBACH,OAAO,4MAAA,CAAA,YAAS;YAClB,KAAK;gBACH,OAAO,wNAAA,CAAA,gBAAa;YACtB,KAAK;gBACH,OAAO,0MAAA,CAAA,WAAQ;YACjB;gBACE,OAAO,kMAAA,CAAA,OAAI;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,WAAW,0BAC1B,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;;;;;iDAGR,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAOb,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAA4B;gDAC/B,eAAe,MAAM;;;;;;;sDAEhC,8OAAC;4CAAE,WAAU;;gDAAwB;gDACzB,IAAI,KAAK,eAAe,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,eAAe,cAAc,EAAE,UAAU;;;;;;8CAE5C,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,aAAY;gCACZ,WAAU;gCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0CAE1C,8OAAC;gCACC,SAAS;gCACT,UAAU,CAAC,kBAAkB,IAAI,MAAM;gCACvC,WAAU;;oCAET,iCACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDAEvB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAMX,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;oCACvC,YAAY,MAAM;oCAAC;;;;;;;4BAG7C,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;uCAErC,YAAY,MAAM,KAAK,kBACzB,8OAAC;gCAAI,WAAU;;oCAAoD;kDAEjE,8OAAC;;;;;oCAAK;;;;;;uCAIR,YAAY,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,YAAY,WAAW,IAAI;gCAC5C,qBACE,8OAAC;oCAEC,WAAW,CAAC,uDAAuD,EACjE,oBAAoB,OAAO,WAAW,EAAE,GACpC,gDACA,yFACJ;oCACF,SAAS,IAAM,sBAAsB;;sDAErC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,YAAY,EAAE,aAAa,WAAW,IAAI,GAAG;sEAC5D,cAAA,8OAAC;gEAAS,WAAU;;;;;;;;;;;sEAEtB,8OAAC;4DAAG,WAAU;sEACX,WAAW,KAAK;;;;;;;;;;;;8DAGrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAW,CAAC,0BAA0B,EAAE,eAAe,WAAW,MAAM,GAAG;sEAC9E,WAAW,MAAM;;;;;;sEAEpB,8OAAC;4DAAK,WAAU;;gEACb,KAAK,KAAK,CAAC,WAAW,UAAU,GAAG;gEAAK;;;;;;;;;;;;;;;;;;;sDAK/C,8OAAC;4CAAE,WAAU;sDACV,WAAW,WAAW;;;;;;sDAGzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,WAAW,IAAI;;;;;;8DAElB,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,gBAAgB;oDAClB;oDACA,UAAU,aAAa,WAAW,EAAE;oDACpC,WAAU;;wDAET,aAAa,WAAW,EAAE,iBACzB,8OAAC;4DAAI,WAAU;;;;;iFAEf,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEAEzB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;mCAhDL,WAAW,EAAE;;;;;4BAqDxB;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,qBAAqB,wBAAwB;;;;;;;;;;;4BAIjD,mCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC;gDAAE,WAAU;0DACV,mBAAmB,WAAW;;;;;;;;;;;;kDAInC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC;gDAAE,WAAU;0DACV,mBAAmB,gBAAgB;;;;;;;;;;;;oCAIvC,mBAAmB,YAAY,kBAC9B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC,gIAAA,CAAA,UAAU;gDACT,MAAM,mBAAmB,YAAY;gDACrC,UAAS;gDACT,iBAAiB;;;;;;;;;;;;kDAKvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC,gIAAA,CAAA,UAAU;gDACT,MAAM,mBAAmB,aAAa;gDACtC,UAAS;gDACT,iBAAiB;;;;;;;;;;;;;;;;;qDAKvB,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;;;;;;;;;;;;;AAQpG", "debugId": null}}, {"offset": {"line": 2541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/components/HistoryView.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  History, \n  CheckCircle, \n  XCircle, \n  RotateCcw, \n  Calendar,\n  Clock,\n  FileText,\n  TrendingUp,\n  AlertTriangle\n} from 'lucide-react';\nimport CodeViewer from './CodeViewer';\n\ninterface AppliedChange {\n  id: string;\n  suggestion: {\n    id: string;\n    type: string;\n    title: string;\n    description: string;\n    file: string;\n    originalCode: string;\n    suggestedCode: string;\n    confidence: number;\n    impact: string;\n  };\n  appliedAt: string;\n  backupId: string;\n  success: boolean;\n  error?: string;\n}\n\ninterface ImprovementSession {\n  id: string;\n  startedAt: string;\n  completedAt?: string;\n  status: 'running' | 'completed' | 'failed' | 'cancelled';\n  appliedChanges: AppliedChange[];\n  settings: any;\n}\n\nexport default function HistoryView() {\n  const [sessions, setSessions] = useState<ImprovementSession[]>([]);\n  const [appliedChanges, setAppliedChanges] = useState<AppliedChange[]>([]);\n  const [selectedChange, setSelectedChange] = useState<AppliedChange | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'sessions' | 'changes'>('sessions');\n  const [rollingBack, setRollingBack] = useState<string | null>(null);\n\n  useEffect(() => {\n    loadHistoryData();\n  }, []);\n\n  const loadHistoryData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Load sessions\n      const sessionsResponse = await fetch('/api/improve?action=sessions');\n      const sessionsData = await sessionsResponse.json();\n      \n      if (sessionsData.success) {\n        setSessions(sessionsData.data);\n      }\n\n      // Load applied changes\n      const changesResponse = await fetch('/api/improve?action=applied-changes');\n      const changesData = await changesResponse.json();\n      \n      if (changesData.success) {\n        setAppliedChanges(changesData.data);\n      }\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load history data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const rollbackChange = async (changeId: string) => {\n    try {\n      setRollingBack(changeId);\n      setError(null);\n\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'rollback-change',\n          data: { changeId }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success && data.data.success) {\n        // Refresh the data\n        loadHistoryData();\n        setSelectedChange(null);\n      } else {\n        setError('Failed to rollback change');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to rollback change');\n    } finally {\n      setRollingBack(null);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return CheckCircle;\n      case 'failed':\n        return XCircle;\n      case 'cancelled':\n        return XCircle;\n      case 'running':\n        return Clock;\n      default:\n        return Clock;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'text-green-600';\n      case 'failed':\n        return 'text-red-600';\n      case 'cancelled':\n        return 'text-yellow-600';\n      case 'running':\n        return 'text-blue-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'performance':\n        return 'bg-green-100 text-green-800';\n      case 'refactor':\n        return 'bg-blue-100 text-blue-800';\n      case 'feature':\n        return 'bg-purple-100 text-purple-800';\n      case 'bug-fix':\n        return 'bg-red-100 text-red-800';\n      case 'style':\n        return 'bg-yellow-100 text-yellow-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600\">Loading history...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          Improvement History\n        </h2>\n        <button\n          onClick={loadHistoryData}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          Refresh\n        </button>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertTriangle className=\"h-5 w-5 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\n        <nav className=\"-mb-px flex space-x-8\">\n          <button\n            onClick={() => setActiveTab('sessions')}\n            className={`py-2 px-1 border-b-2 font-medium text-sm ${\n              activeTab === 'sessions'\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n            }`}\n          >\n            Sessions ({sessions.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('changes')}\n            className={`py-2 px-1 border-b-2 font-medium text-sm ${\n              activeTab === 'changes'\n                ? 'border-blue-500 text-blue-600'\n                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n            }`}\n          >\n            Changes ({appliedChanges.length})\n          </button>\n        </nav>\n      </div>\n\n      {/* Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* List */}\n        <div className=\"space-y-4\">\n          {activeTab === 'sessions' ? (\n            // Sessions List\n            sessions.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                No improvement sessions found\n              </div>\n            ) : (\n              sessions.map((session) => {\n                const StatusIcon = getStatusIcon(session.status);\n                return (\n                  <div\n                    key={session.id}\n                    className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700\"\n                  >\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-2\">\n                        <StatusIcon className={`h-5 w-5 ${getStatusColor(session.status)}`} />\n                        <span className=\"font-medium text-gray-900 dark:text-white\">\n                          Session {session.id.split('_')[1]}\n                        </span>\n                      </div>\n                      <span className={`text-xs px-2 py-1 rounded capitalize ${getStatusColor(session.status)} bg-opacity-10`}>\n                        {session.status}\n                      </span>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500 dark:text-gray-400\">Started:</span>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {new Date(session.startedAt).toLocaleString()}\n                        </div>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500 dark:text-gray-400\">Changes:</span>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {session.appliedChanges?.length || 0}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    {session.completedAt && (\n                      <div className=\"mt-2 text-sm\">\n                        <span className=\"text-gray-500 dark:text-gray-400\">Completed:</span>\n                        <span className=\"ml-1 text-gray-900 dark:text-white\">\n                          {new Date(session.completedAt).toLocaleString()}\n                        </span>\n                      </div>\n                    )}\n                  </div>\n                );\n              })\n            )\n          ) : (\n            // Changes List\n            appliedChanges.length === 0 ? (\n              <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                No applied changes found\n              </div>\n            ) : (\n              appliedChanges.map((change) => (\n                <div\n                  key={change.id}\n                  className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 border cursor-pointer transition-colors ${\n                    selectedChange?.id === change.id\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'\n                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'\n                  }`}\n                  onClick={() => setSelectedChange(change)}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      {change.success ? (\n                        <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                      ) : (\n                        <XCircle className=\"h-4 w-4 text-red-600\" />\n                      )}\n                      <span className=\"font-medium text-gray-900 dark:text-white\">\n                        {change.suggestion.title}\n                      </span>\n                    </div>\n                    <span className={`text-xs px-2 py-1 rounded ${getTypeColor(change.suggestion.type)}`}>\n                      {change.suggestion.type}\n                    </span>\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    {change.suggestion.description}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\">\n                    <span>{change.suggestion.file}</span>\n                    <span>{new Date(change.appliedAt).toLocaleString()}</span>\n                  </div>\n                  \n                  {!change.success && change.error && (\n                    <div className=\"mt-2 text-xs text-red-600 bg-red-50 dark:bg-red-900 p-2 rounded\">\n                      {change.error}\n                    </div>\n                  )}\n                </div>\n              ))\n            )\n          )}\n        </div>\n\n        {/* Details */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              {selectedChange ? 'Change Details' : 'Select a change'}\n            </h3>\n          </div>\n          \n          {selectedChange ? (\n            <div className=\"p-4 space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                    {selectedChange.suggestion.title}\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Applied: {new Date(selectedChange.appliedAt).toLocaleString()}\n                  </p>\n                </div>\n                {selectedChange.success && (\n                  <button\n                    onClick={() => rollbackChange(selectedChange.id)}\n                    disabled={rollingBack === selectedChange.id}\n                    className=\"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1\"\n                  >\n                    {rollingBack === selectedChange.id ? (\n                      <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-white\"></div>\n                    ) : (\n                      <RotateCcw className=\"h-3 w-3\" />\n                    )}\n                    <span>Rollback</span>\n                  </button>\n                )}\n              </div>\n              \n              <div>\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  Description\n                </h5>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {selectedChange.suggestion.description}\n                </p>\n              </div>\n              \n              <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-500 dark:text-gray-400\">Type:</span>\n                  <div className=\"font-medium text-gray-900 dark:text-white capitalize\">\n                    {selectedChange.suggestion.type.replace('-', ' ')}\n                  </div>\n                </div>\n                <div>\n                  <span className=\"text-gray-500 dark:text-gray-400\">Impact:</span>\n                  <div className=\"font-medium text-gray-900 dark:text-white capitalize\">\n                    {selectedChange.suggestion.impact}\n                  </div>\n                </div>\n                <div>\n                  <span className=\"text-gray-500 dark:text-gray-400\">Confidence:</span>\n                  <div className=\"font-medium text-gray-900 dark:text-white\">\n                    {Math.round(selectedChange.suggestion.confidence * 100)}%\n                  </div>\n                </div>\n              </div>\n              \n              {selectedChange.suggestion.originalCode && (\n                <div>\n                  <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                    Original Code\n                  </h5>\n                  <CodeViewer\n                    code={selectedChange.suggestion.originalCode}\n                    language=\"typescript\"\n                    showLineNumbers={false}\n                  />\n                </div>\n              )}\n              \n              <div>\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  Applied Code\n                </h5>\n                <CodeViewer\n                  code={selectedChange.suggestion.suggestedCode}\n                  language=\"typescript\"\n                  showLineNumbers={false}\n                />\n              </div>\n            </div>\n          ) : (\n            <div className=\"flex items-center justify-center h-64 text-gray-500 dark:text-gray-400\">\n              Select a change to see details\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAWA;AAdA;;;;;AA4Ce,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,SAAS;YAET,gBAAgB;YAChB,MAAM,mBAAmB,MAAM,MAAM;YACrC,MAAM,eAAe,MAAM,iBAAiB,IAAI;YAEhD,IAAI,aAAa,OAAO,EAAE;gBACxB,YAAY,aAAa,IAAI;YAC/B;YAEA,uBAAuB;YACvB,MAAM,kBAAkB,MAAM,MAAM;YACpC,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAE9C,IAAI,YAAY,OAAO,EAAE;gBACvB,kBAAkB,YAAY,IAAI;YACpC;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,eAAe;YACf,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE;oBAAS;gBACnB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE;gBACrC,mBAAmB;gBACnB;gBACA,kBAAkB;YACpB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO,2NAAA,CAAA,cAAW;YACpB,KAAK;gBACH,OAAO,4MAAA,CAAA,UAAO;YAChB,KAAK;gBACH,OAAO,4MAAA,CAAA,UAAO;YAChB,KAAK;gBACH,OAAO,oMAAA,CAAA,QAAK;YACd;gBACE,OAAO,oMAAA,CAAA,QAAK;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAqB;;;;;;;;;;;;IAG3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMF,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,kCACA,8EACJ;;gCACH;gCACY,SAAS,MAAM;gCAAC;;;;;;;sCAE7B,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;;gCACH;gCACW,eAAe,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,cAAc,aACb,gBAAgB;wBAChB,SAAS,MAAM,KAAK,kBAClB,8OAAC;4BAAI,WAAU;sCAAoD;;;;;mCAInE,SAAS,GAAG,CAAC,CAAC;4BACZ,MAAM,aAAa,cAAc,QAAQ,MAAM;4BAC/C,qBACE,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAW,WAAW,CAAC,QAAQ,EAAE,eAAe,QAAQ,MAAM,GAAG;;;;;;kEAClE,8OAAC;wDAAK,WAAU;;4DAA4C;4DACjD,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;0DAGrC,8OAAC;gDAAK,WAAW,CAAC,qCAAqC,EAAE,eAAe,QAAQ,MAAM,EAAE,cAAc,CAAC;0DACpG,QAAQ,MAAM;;;;;;;;;;;;kDAInB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,IAAI,KAAK,QAAQ,SAAS,EAAE,cAAc;;;;;;;;;;;;0DAG/C,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,cAAc,EAAE,UAAU;;;;;;;;;;;;;;;;;;oCAKxC,QAAQ,WAAW,kBAClB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DACnD,8OAAC;gDAAK,WAAU;0DACb,IAAI,KAAK,QAAQ,WAAW,EAAE,cAAc;;;;;;;;;;;;;+BAlC9C,QAAQ,EAAE;;;;;wBAwCrB,KAGF,eAAe;wBACf,eAAe,MAAM,KAAK,kBACxB,8OAAC;4BAAI,WAAU;sCAAoD;;;;;mCAInE,eAAe,GAAG,CAAC,CAAC,uBAClB,8OAAC;gCAEC,WAAW,CAAC,wFAAwF,EAClG,gBAAgB,OAAO,OAAO,EAAE,GAC5B,gDACA,8DACJ;gCACF,SAAS,IAAM,kBAAkB;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDACZ,OAAO,OAAO,iBACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEAErB,8OAAC;wDAAK,WAAU;kEACb,OAAO,UAAU,CAAC,KAAK;;;;;;;;;;;;0DAG5B,8OAAC;gDAAK,WAAW,CAAC,0BAA0B,EAAE,aAAa,OAAO,UAAU,CAAC,IAAI,GAAG;0DACjF,OAAO,UAAU,CAAC,IAAI;;;;;;;;;;;;kDAI3B,8OAAC;wCAAE,WAAU;kDACV,OAAO,UAAU,CAAC,WAAW;;;;;;kDAGhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAM,OAAO,UAAU,CAAC,IAAI;;;;;;0DAC7B,8OAAC;0DAAM,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;;;;;;;oCAGjD,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,kBAC9B,8OAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK;;;;;;;+BAnCZ,OAAO,EAAE;;;;;;;;;;kCA6CxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,iBAAiB,mBAAmB;;;;;;;;;;;4BAIxC,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,eAAe,UAAU,CAAC,KAAK;;;;;;kEAElC,8OAAC;wDAAE,WAAU;;4DAA2C;4DAC5C,IAAI,KAAK,eAAe,SAAS,EAAE,cAAc;;;;;;;;;;;;;4CAG9D,eAAe,OAAO,kBACrB,8OAAC;gDACC,SAAS,IAAM,eAAe,eAAe,EAAE;gDAC/C,UAAU,gBAAgB,eAAe,EAAE;gDAC3C,WAAU;;oDAET,gBAAgB,eAAe,EAAE,iBAChC,8OAAC;wDAAI,WAAU;;;;;6EAEf,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEAEvB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC;gDAAE,WAAU;0DACV,eAAe,UAAU,CAAC,WAAW;;;;;;;;;;;;kDAI1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,eAAe,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAGjD,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,eAAe,UAAU,CAAC,MAAM;;;;;;;;;;;;0DAGrC,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAmC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,KAAK,CAAC,eAAe,UAAU,CAAC,UAAU,GAAG;4DAAK;;;;;;;;;;;;;;;;;;;oCAK7D,eAAe,UAAU,CAAC,YAAY,kBACrC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC,gIAAA,CAAA,UAAU;gDACT,MAAM,eAAe,UAAU,CAAC,YAAY;gDAC5C,UAAS;gDACT,iBAAiB;;;;;;;;;;;;kDAKvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAG/D,8OAAC,gIAAA,CAAA,UAAU;gDACT,MAAM,eAAe,UAAU,CAAC,aAAa;gDAC7C,UAAS;gDACT,iBAAiB;;;;;;;;;;;;;;;;;qDAKvB,8OAAC;gCAAI,WAAU;0CAAyE;;;;;;;;;;;;;;;;;;;;;;;;AAQpG", "debugId": null}}, {"offset": {"line": 3335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/components/SettingsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  Settings, \n  Save, \n  RotateCcw, \n  AlertTriangle,\n  CheckCircle,\n  Info,\n  Trash2,\n  Download\n} from 'lucide-react';\n\ninterface ImprovementSettings {\n  autoApprove: boolean;\n  maxChangesPerSession: number;\n  improvementTypes: string[];\n  minConfidence: number;\n  excludeFiles: string[];\n  requireManualApproval: boolean;\n}\n\nexport default function SettingsPanel() {\n  const [settings, setSettings] = useState<ImprovementSettings>({\n    autoApprove: false,\n    maxChangesPerSession: 10,\n    improvementTypes: ['performance', 'refactor', 'style', 'bug-fix'],\n    minConfidence: 0.7,\n    excludeFiles: ['node_modules', '.git', 'dist', 'build'],\n    requireManualApproval: true\n  });\n  \n  const [originalSettings, setOriginalSettings] = useState<ImprovementSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState(false);\n  const [selectedModel, setSelectedModel] = useState('openai/gpt-4.1-nano');\n\n  const improvementTypeOptions = [\n    { value: 'performance', label: 'Performance Optimizations' },\n    { value: 'refactor', label: 'Code Refactoring' },\n    { value: 'style', label: 'Code Style & Formatting' },\n    { value: 'bug-fix', label: 'Bug Fixes' },\n    { value: 'feature', label: 'New Features' }\n  ];\n\n  const modelOptions = [\n    { value: 'openai/gpt-4.1-nano', label: 'GPT-4.1 Nano (REQUIRED)', description: 'The only acceptable model for this application', provider: 'OpenAI', cost: 'Low' },\n    { value: 'anthropic/claude-3.5-sonnet', label: 'Claude 3.5 Sonnet', description: 'Excellent for code analysis and generation', provider: 'Anthropic', cost: 'Medium' },\n    { value: 'openai/gpt-4', label: 'GPT-4', description: 'Strong general-purpose model', provider: 'OpenAI', cost: 'High' },\n    { value: 'openai/gpt-4-turbo', label: 'GPT-4 Turbo', description: 'Faster GPT-4 variant', provider: 'OpenAI', cost: 'Medium' },\n    { value: 'anthropic/claude-3-haiku', label: 'Claude 3 Haiku', description: 'Fast and cost-effective', provider: 'Anthropic', cost: 'Low' },\n    { value: 'meta-llama/llama-3.1-405b', label: 'Llama 3.1 405B', description: 'Open-source alternative', provider: 'Meta', cost: 'Low' },\n    { value: 'google/gemini-pro', label: 'Gemini Pro', description: 'Google\\'s flagship model', provider: 'Google', cost: 'Medium' },\n    { value: 'mistralai/mixtral-8x7b-instruct', label: 'Mixtral 8x7B', description: 'Efficient mixture of experts', provider: 'Mistral AI', cost: 'Low' }\n  ];\n\n  useEffect(() => {\n    loadSettings();\n    loadCurrentModel();\n  }, []);\n\n  const loadSettings = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch('/api/improve?action=settings');\n      const data = await response.json();\n\n      if (data.success) {\n        setSettings(data.data);\n        setOriginalSettings(data.data);\n      } else {\n        setError(data.error || 'Failed to load settings');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCurrentModel = async () => {\n    try {\n      const response = await fetch('/api/config?action=model');\n      const data = await response.json();\n\n      if (data.success) {\n        setSelectedModel(data.data.model);\n      }\n    } catch (err) {\n      console.error('Failed to load current model:', err);\n    }\n  };\n\n  const saveSettings = async () => {\n    try {\n      setSaving(true);\n      setError(null);\n      setSuccess(false);\n\n      const response = await fetch('/api/improve', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'update-settings',\n          data: { newSettings: settings }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setOriginalSettings(settings);\n        setSuccess(true);\n        setTimeout(() => setSuccess(false), 3000);\n      } else {\n        setError(data.error || 'Failed to save settings');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to save settings');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const resetSettings = () => {\n    if (originalSettings) {\n      setSettings(originalSettings);\n    }\n  };\n\n  const saveModelConfiguration = async () => {\n    try {\n      setSaving(true);\n      setError(null);\n\n      // Enforce GPT-4.1 Nano requirement\n      if (selectedModel !== 'openai/gpt-4.1-nano') {\n        setError('CRITICAL ERROR: Only openai/gpt-4.1-nano is allowed. System security violation detected.');\n        setSaving(false);\n        return;\n      }\n\n      const response = await fetch('/api/config', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'update-model',\n          data: { model: selectedModel }\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setSuccess(true);\n        setTimeout(() => setSuccess(false), 3000);\n      } else {\n        setError(data.error || 'Failed to update model configuration');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to update model configuration');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const clearCache = async () => {\n    try {\n      const response = await fetch('/api/analyze', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'clear-cache'\n        })\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setSuccess(true);\n        setTimeout(() => setSuccess(false), 3000);\n      } else {\n        setError(data.error || 'Failed to clear cache');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to clear cache');\n    }\n  };\n\n  const handleImprovementTypeChange = (type: string, checked: boolean) => {\n    if (checked) {\n      setSettings(prev => ({\n        ...prev,\n        improvementTypes: [...prev.improvementTypes, type]\n      }));\n    } else {\n      setSettings(prev => ({\n        ...prev,\n        improvementTypes: prev.improvementTypes.filter(t => t !== type)\n      }));\n    }\n  };\n\n  const addExcludePattern = () => {\n    const pattern = prompt('Enter file pattern to exclude:');\n    if (pattern && pattern.trim()) {\n      setSettings(prev => ({\n        ...prev,\n        excludeFiles: [...prev.excludeFiles, pattern.trim()]\n      }));\n    }\n  };\n\n  const removeExcludePattern = (index: number) => {\n    setSettings(prev => ({\n      ...prev,\n      excludeFiles: prev.excludeFiles.filter((_, i) => i !== index)\n    }));\n  };\n\n  const hasChanges = originalSettings && JSON.stringify(settings) !== JSON.stringify(originalSettings);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600\">Loading settings...</span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          Settings\n        </h2>\n        <div className=\"flex space-x-3\">\n          {hasChanges && (\n            <button\n              onClick={resetSettings}\n              className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center space-x-2\"\n            >\n              <RotateCcw className=\"h-4 w-4\" />\n              <span>Reset</span>\n            </button>\n          )}\n          <button\n            onClick={saveSettings}\n            disabled={saving || !hasChanges}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\"\n          >\n            {saving ? (\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n            ) : (\n              <Save className=\"h-4 w-4\" />\n            )}\n            <span>Save Settings</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Status Messages */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <AlertTriangle className=\"h-5 w-5 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {success && (\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <CheckCircle className=\"h-5 w-5 text-green-600 mr-2\" />\n            <span className=\"text-green-800\">Settings saved successfully!</span>\n          </div>\n        </div>\n      )}\n\n      {/* Settings Sections */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Improvement Settings */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Improvement Settings\n          </h3>\n          \n          <div className=\"space-y-4\">\n            {/* Auto Approve */}\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Auto-approve changes\n                </label>\n                <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                  Automatically apply improvements without manual approval\n                </p>\n              </div>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoApprove}\n                onChange={(e) => setSettings(prev => ({ ...prev, autoApprove: e.target.checked }))}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n            </div>\n\n            {/* Manual Approval */}\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  Require manual approval\n                </label>\n                <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                  Always require manual approval before applying changes\n                </p>\n              </div>\n              <input\n                type=\"checkbox\"\n                checked={settings.requireManualApproval}\n                onChange={(e) => setSettings(prev => ({ ...prev, requireManualApproval: e.target.checked }))}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n            </div>\n\n            {/* Max Changes */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-900 dark:text-white mb-1\">\n                Max changes per session\n              </label>\n              <input\n                type=\"number\"\n                min=\"1\"\n                max=\"50\"\n                value={settings.maxChangesPerSession}\n                onChange={(e) => setSettings(prev => ({ ...prev, maxChangesPerSession: parseInt(e.target.value) || 10 }))}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n\n            {/* Min Confidence */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-900 dark:text-white mb-1\">\n                Minimum confidence ({Math.round(settings.minConfidence * 100)}%)\n              </label>\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"1\"\n                step=\"0.1\"\n                value={settings.minConfidence}\n                onChange={(e) => setSettings(prev => ({ ...prev, minConfidence: parseFloat(e.target.value) }))}\n                className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Improvement Types */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Improvement Types\n          </h3>\n          \n          <div className=\"space-y-3\">\n            {improvementTypeOptions.map((option) => (\n              <div key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id={option.value}\n                  checked={settings.improvementTypes.includes(option.value)}\n                  onChange={(e) => handleImprovementTypeChange(option.value, e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor={option.value} className=\"ml-2 text-sm text-gray-900 dark:text-white\">\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* File Exclusions */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Excluded Files\n            </h3>\n            <button\n              onClick={addExcludePattern}\n              className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors\"\n            >\n              Add Pattern\n            </button>\n          </div>\n          \n          <div className=\"space-y-2\">\n            {settings.excludeFiles.map((pattern, index) => (\n              <div key={index} className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded\">\n                <span className=\"text-sm text-gray-900 dark:text-white font-mono\">\n                  {pattern}\n                </span>\n                <button\n                  onClick={() => removeExcludePattern(index)}\n                  className=\"text-red-600 hover:text-red-800 transition-colors\"\n                >\n                  <Trash2 className=\"h-4 w-4\" />\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* AI Model Configuration */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            AI Model Configuration\n          </h3>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                Select AI Model\n              </label>\n              <select\n                value={selectedModel}\n                onChange={(e) => {\n                  if (e.target.value !== 'openai/gpt-4.1-nano') {\n                    setError('SECURITY VIOLATION: Only openai/gpt-4.1-nano is permitted!');\n                    return;\n                  }\n                  setSelectedModel(e.target.value);\n                }}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              >\n                {modelOptions.map((option) => (\n                  <option key={option.value} value={option.value}>\n                    {option.label} - {option.provider} ({option.cost} cost)\n                  </option>\n                ))}\n              </select>\n              <div className=\"mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded text-xs\">\n                <div className=\"font-medium text-gray-900 dark:text-white\">\n                  {modelOptions.find(m => m.value === selectedModel)?.label}\n                </div>\n                <div className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                  {modelOptions.find(m => m.value === selectedModel)?.description}\n                </div>\n                <div className=\"flex items-center space-x-4 mt-1 text-gray-500 dark:text-gray-400\">\n                  <span>Provider: {modelOptions.find(m => m.value === selectedModel)?.provider}</span>\n                  <span>Cost: {modelOptions.find(m => m.value === selectedModel)?.cost}</span>\n                </div>\n              </div>\n            </div>\n\n            <button\n              onClick={saveModelConfiguration}\n              disabled={saving}\n              className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2\"\n            >\n              {saving ? (\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n              ) : (\n                <Save className=\"h-4 w-4\" />\n              )}\n              <span>Update Model Configuration</span>\n            </button>\n\n            <div className=\"bg-red-50 dark:bg-red-900 p-3 rounded-lg border border-red-200 dark:border-red-700\">\n              <div className=\"flex items-start space-x-2\">\n                <AlertTriangle className=\"h-4 w-4 text-red-600 mt-0.5\" />\n                <div className=\"text-sm text-red-800 dark:text-red-200\">\n                  <p className=\"font-medium mb-1\">⚠️ CRITICAL REQUIREMENT</p>\n                  <p className=\"text-xs\">\n                    This application MUST use <strong>openai/gpt-4.1-nano</strong> as the AI model.\n                    Using any other model may result in system termination.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n        {/* System Actions */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            System Actions\n          </h3>\n\n          <div className=\"space-y-3\">\n            <button\n              onClick={clearCache}\n              className=\"w-full px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors flex items-center justify-center space-x-2\"\n            >\n              <Trash2 className=\"h-4 w-4\" />\n              <span>Clear Analysis Cache</span>\n            </button>\n\n            <div className=\"bg-blue-50 dark:bg-blue-900 p-3 rounded-lg\">\n              <div className=\"flex items-start space-x-2\">\n                <Info className=\"h-4 w-4 text-blue-600 mt-0.5\" />\n                <div className=\"text-sm text-blue-800 dark:text-blue-200\">\n                  <p className=\"font-medium mb-1\">OpenRouter API Configuration</p>\n                  <p>Configure your OpenRouter API key in the .env.local file:</p>\n                  <code className=\"block mt-1 p-1 bg-blue-100 dark:bg-blue-800 rounded text-xs\">\n                    OPENROUTER_API_KEY=your_api_key_here\n                  </code>\n                  <p className=\"mt-2 text-xs\">\n                    Get your API key from <a href=\"https://openrouter.ai/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline\">openrouter.ai</a>.\n                    OpenRouter provides access to multiple AI providers with transparent pricing.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAuBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QAC5D,aAAa;QACb,sBAAsB;QACtB,kBAAkB;YAAC;YAAe;YAAY;YAAS;SAAU;QACjE,eAAe;QACf,cAAc;YAAC;YAAgB;YAAQ;YAAQ;SAAQ;QACvD,uBAAuB;IACzB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IACrF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,yBAAyB;QAC7B;YAAE,OAAO;YAAe,OAAO;QAA4B;QAC3D;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAS,OAAO;QAA0B;QACnD;YAAE,OAAO;YAAW,OAAO;QAAY;QACvC;YAAE,OAAO;YAAW,OAAO;QAAe;KAC3C;IAED,MAAM,eAAe;QACnB;YAAE,OAAO;YAAuB,OAAO;YAA2B,aAAa;YAAkD,UAAU;YAAU,MAAM;QAAM;QACjK;YAAE,OAAO;YAA+B,OAAO;YAAqB,aAAa;YAA8C,UAAU;YAAa,MAAM;QAAS;QACrK;YAAE,OAAO;YAAgB,OAAO;YAAS,aAAa;YAAgC,UAAU;YAAU,MAAM;QAAO;QACvH;YAAE,OAAO;YAAsB,OAAO;YAAe,aAAa;YAAwB,UAAU;YAAU,MAAM;QAAS;QAC7H;YAAE,OAAO;YAA4B,OAAO;YAAkB,aAAa;YAA2B,UAAU;YAAa,MAAM;QAAM;QACzI;YAAE,OAAO;YAA6B,OAAO;YAAkB,aAAa;YAA2B,UAAU;YAAQ,MAAM;QAAM;QACrI;YAAE,OAAO;YAAqB,OAAO;YAAc,aAAa;YAA4B,UAAU;YAAU,MAAM;QAAS;QAC/H;YAAE,OAAO;YAAmC,OAAO;YAAgB,aAAa;YAAgC,UAAU;YAAc,MAAM;QAAM;KACrJ;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;gBACrB,oBAAoB,KAAK,IAAI;YAC/B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI,CAAC,KAAK;YAClC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,UAAU;YACV,SAAS;YACT,WAAW;YAEX,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE,aAAa;oBAAS;gBAChC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,oBAAoB;gBACpB,WAAW;gBACX,WAAW,IAAM,WAAW,QAAQ;YACtC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,kBAAkB;YACpB,YAAY;QACd;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI;YACF,UAAU;YACV,SAAS;YAET,mCAAmC;YACnC,IAAI,kBAAkB,uBAAuB;gBAC3C,SAAS;gBACT,UAAU;gBACV;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;wBAAE,OAAO;oBAAc;gBAC/B;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW;gBACX,WAAW,IAAM,WAAW,QAAQ;YACtC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;gBACV;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,WAAW;gBACX,WAAW,IAAM,WAAW,QAAQ;YACtC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,8BAA8B,CAAC,MAAc;QACjD,IAAI,SAAS;YACX,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,kBAAkB;2BAAI,KAAK,gBAAgB;wBAAE;qBAAK;gBACpD,CAAC;QACH,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,kBAAkB,KAAK,gBAAgB,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;gBAC5D,CAAC;QACH;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,UAAU,OAAO;QACvB,IAAI,WAAW,QAAQ,IAAI,IAAI;YAC7B,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,cAAc;2BAAI,KAAK,YAAY;wBAAE,QAAQ,IAAI;qBAAG;gBACtD,CAAC;QACH;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,cAAc,KAAK,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACzD,CAAC;IACH;IAEA,MAAM,aAAa,oBAAoB,KAAK,SAAS,CAAC,cAAc,KAAK,SAAS,CAAC;IAEnF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAK,WAAU;8BAAqB;;;;;;;;;;;;IAG3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,8OAAC;wBAAI,WAAU;;4BACZ,4BACC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAK;;;;;;;;;;;;0CAGV,8OAAC;gCACC,SAAS;gCACT,UAAU,UAAU,CAAC;gCACrB,WAAU;;oCAET,uBACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAElB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAMX,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAKrC,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAIvE,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoD;;;;;;kEAGrE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAI1D,8OAAC;gDACC,MAAK;gDACL,SAAS,SAAS,WAAW;gDAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;gDAChF,WAAU;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoD;;;;;;kEAGrE,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAI1D,8OAAC;gDACC,MAAK;gDACL,SAAS,SAAS,qBAAqB;gDACvC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,uBAAuB,EAAE,MAAM,CAAC,OAAO;wDAAC,CAAC;gDAC1F,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+D;;;;;;0DAGhF,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,SAAS,oBAAoB;gDACpC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,sBAAsB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDAAG,CAAC;gDACvG,WAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;;oDAA+D;oDACzD,KAAK,KAAK,CAAC,SAAS,aAAa,GAAG;oDAAK;;;;;;;0DAEhE,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDAC5F,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAIvE,8OAAC;gCAAI,WAAU;0CACZ,uBAAuB,GAAG,CAAC,CAAC,uBAC3B,8OAAC;wCAAuB,WAAU;;0DAChC,8OAAC;gDACC,MAAK;gDACL,IAAI,OAAO,KAAK;gDAChB,SAAS,SAAS,gBAAgB,CAAC,QAAQ,CAAC,OAAO,KAAK;gDACxD,UAAU,CAAC,IAAM,4BAA4B,OAAO,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO;gDAC3E,WAAU;;;;;;0DAEZ,8OAAC;gDAAM,SAAS,OAAO,KAAK;gDAAE,WAAU;0DACrC,OAAO,KAAK;;;;;;;uCATP,OAAO,KAAK;;;;;;;;;;;;;;;;kCAiB5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCAAI,WAAU;0CACZ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAK,WAAU;0DACb;;;;;;0DAEH,8OAAC;gDACC,SAAS,IAAM,qBAAqB;gDACpC,WAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;uCARZ;;;;;;;;;;;;;;;;kCAgBhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAIvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+D;;;;;;0DAGhF,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC;oDACT,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,uBAAuB;wDAC5C,SAAS;wDACT;oDACF;oDACA,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDACjC;gDACA,WAAU;0DAET,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;;4DAC3C,OAAO,KAAK;4DAAC;4DAAI,OAAO,QAAQ;4DAAC;4DAAG,OAAO,IAAI;4DAAC;;uDADtC,OAAO,KAAK;;;;;;;;;;0DAK7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,gBAAgB;;;;;;kEAEtD,8OAAC;wDAAI,WAAU;kEACZ,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,gBAAgB;;;;;;kEAEtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAW,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,gBAAgB;;;;;;;0EACpE,8OAAC;;oEAAK;oEAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;kDAKtE,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,uBACC,8OAAC;gDAAI,WAAU;;;;;qEAEf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAElB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAmB;;;;;;sEAChC,8OAAC;4DAAE,WAAU;;gEAAU;8EACK,8OAAC;8EAAO;;;;;;gEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAIvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAmB;;;;;;sEAChC,8OAAC;sEAAE;;;;;;sEACH,8OAAC;4DAAK,WAAU;sEAA8D;;;;;;sEAG9E,8OAAC;4DAAE,WAAU;;gEAAe;8EACJ,8OAAC;oEAAE,MAAK;oEAAyB,QAAO;oEAAS,KAAI;oEAAsB,WAAU;8EAAY;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5J", "debugId": null}}, {"offset": {"line": 4464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Activity, Brain, Code, Settings, History, FileText } from 'lucide-react';\nimport Dashboard from '@/components/Dashboard';\nimport CodeAnalyzer from '@/components/CodeAnalyzer';\nimport ImprovementCenter from '@/components/ImprovementCenter';\nimport HistoryView from '@/components/HistoryView';\nimport SettingsPanel from '@/components/SettingsPanel';\n\ntype TabType = 'dashboard' | 'analyze' | 'improve' | 'history' | 'settings';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<TabType>('dashboard');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const tabs: Array<{id: TabType; label: string; icon: React.ElementType}> = [\n  { id: 'dashboard', label: 'Dashboard', icon: Activity },\n  { id: 'analyze', label: 'Code Analysis', icon: Code },\n  { id: 'improve', label: 'Improvements', icon: Brain },\n  { id: 'history', label: 'History', icon: History },\n  { id: 'settings', label: 'Settings', icon: Settings },\n]; // Moved outside component\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'dashboard':\n        return <Dashboard />;\n      case 'analyze':\n        return <CodeAnalyzer />;\n      case 'improve':\n        return <ImprovementCenter />;\n      case 'history':\n        return <HistoryView />;\n      case 'settings':\n        return <SettingsPanel />;\n      default:\n        return <Dashboard />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <Brain className=\"h-8 w-8 text-blue-600\" />\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                Self-Improving App\n              </h1>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                v1.0.0\n              </div>\n              {isLoading && (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"></div>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-300\">Processing...</span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation */}\n      <nav className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{tab.label}</span>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {renderContent()}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAqE;QAC3E;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,IAAI;YAAW,OAAO;YAAiB,MAAM,kMAAA,CAAA,OAAI;QAAC;QACpD;YAAE,IAAI;YAAW,OAAO;YAAgB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACpD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,wMAAA,CAAA,UAAO;QAAC;QACjD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACrD,EAAE,0BAA0B;IAE3B,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,+HAAA,CAAA,UAAS;;;;;YACnB,KAAK;gBACH,qBAAO,8OAAC,kIAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,8OAAC,uIAAA,CAAA,UAAiB;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,UAAW;;;;;YACrB,KAAK;gBACH,qBAAO,8OAAC,mIAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,8OAAC,+HAAA,CAAA,UAAS;;;;;QACrB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;;;;;;;0CAKlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA2C;;;;;;oCAGzD,2BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,OAAO,IAAI,IAAI;4BACrB,qBACE,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,uFAAuF,EACjG,cAAc,IAAI,EAAE,GAChB,qDACA,0HACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,IAAI,KAAK;;;;;;;+BATX,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}]}