import OpenAI from 'openai';

export interface CodeAnalysisResult {
  issues: CodeIssue[];
  suggestions: CodeSuggestion[];
  metrics: CodeMetrics;
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  file: string;
  line?: number;
  column?: number;
  severity: number; // 1-10
}

export interface CodeSuggestion {
  id: string;
  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';
  title: string;
  description: string;
  file: string;
  originalCode: string;
  suggestedCode: string;
  confidence: number; // 0-1
  impact: 'low' | 'medium' | 'high';
  estimatedBenefit: string;
}

export interface CodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainability: number;
  testCoverage?: number;
  performance: number;
}

export class AIClient {
  private client: OpenAI;
  private model: string;

  constructor() {
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error('OPENROUTER_API_KEY environment variable is required');
    }

    this.client = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: process.env.OPENROUTER_API_KEY,
      defaultHeaders: {
        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        'X-Title': process.env.NEXT_PUBLIC_APP_NAME || 'Self-Improving App',
      },
    });

    // Default to GPT-4.1 Nano as specified
    this.model = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-nano';
  }

  async analyzeCode(code: string, fileName: string): Promise<CodeAnalysisResult> {
    const prompt = this.createAnalysisPrompt(code, fileName);

    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert code analyzer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 3000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        console.warn('No response content from AI model');
        return this.createEmptyAnalysis();
      }

      const result = this.parseAnalysisResponse(content, fileName);

      // If parsing failed, create a basic analysis with some default metrics
      if (result.issues.length === 0 && result.suggestions.length === 0 && result.metrics.linesOfCode === 0) {
        return this.createBasicAnalysis(code, fileName);
      }

      return result;
    } catch (error) {
      console.error('Error analyzing code:', error);
      return this.createBasicAnalysis(code, fileName);
    }
  }

  async generateImprovement(
    code: string,
    fileName: string,
    improvementType: string,
    context?: string
  ): Promise<CodeSuggestion | null> {
    const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);

    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert software engineer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        console.warn('No response content from AI model for improvement generation');
        return null;
      }

      const result = this.parseImprovementResponse(content, fileName, improvementType);

      // If parsing failed but we have content, try to create a basic suggestion
      if (!result && content.length > 10) {
        console.warn('Failed to parse improvement response, creating fallback suggestion');
        return this.createFallbackSuggestion(fileName, improvementType, content);
      }

      return result;
    } catch (error) {
      console.error('Error generating improvement:', error);
      return null;
    }
  }

  async generateNewFeature(
    description: string,
    existingCode: string,
    fileName: string
  ): Promise<CodeSuggestion | null> {
    const prompt = `
Generate a new feature based on this description: "${description}"

Existing code context:
\`\`\`${this.getFileExtension(fileName)}
${existingCode}
\`\`\`

Please provide:
1. A clear implementation plan
2. The new code to add
3. Any modifications needed to existing code
4. Potential impacts and considerations

Return your response as JSON with this structure:
{
  "title": "Feature title",
  "description": "Detailed description",
  "originalCode": "existing code that needs modification",
  "suggestedCode": "new/modified code",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "description of benefits"
}
`;

    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert software engineer specializing in feature development.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return null;
      }

      return this.parseImprovementResponse(content, fileName, 'feature');
    } catch (error) {
      console.error('Error generating new feature:', error);
      return null;
    }
  }

  private createAnalysisPrompt(code: string, fileName: string): string {
    return `
Analyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:

File: ${fileName}
\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

IMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text before or after the JSON.

Required JSON structure:
{
  "issues": [
    {
      "type": "error",
      "message": "Issue description",
      "line": 10,
      "severity": 5
    }
  ],
  "suggestions": [
    {
      "type": "performance",
      "title": "Suggestion title",
      "description": "Detailed description",
      "originalCode": "code to replace",
      "suggestedCode": "replacement code",
      "confidence": 0.8,
      "impact": "medium",
      "estimatedBenefit": "Description of benefits"
    }
  ],
  "metrics": {
    "linesOfCode": 50,
    "complexity": 3,
    "maintainability": 8,
    "performance": 7
  }
}

Ensure all string values are properly quoted and the JSON is valid.`;
  }

  private createImprovementPrompt(
    code: string,
    fileName: string,
    improvementType: string,
    context?: string
  ): string {
    return `
Generate a ${improvementType} improvement for this code:

File: ${fileName}
${context ? `Context: ${context}` : ''}

\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

IMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text.

Focus on ${improvementType} improvements and return this exact JSON structure:
{
  "title": "Improvement title",
  "description": "Detailed description of the improvement",
  "originalCode": "Exact code to be replaced",
  "suggestedCode": "Improved replacement code",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "Description of expected benefits"
}

Ensure all strings are properly quoted and the JSON is valid.`;
  }

  private parseAnalysisResponse(content: string, fileName: string): CodeAnalysisResult {
    try {
      // GPT-4.1 Nano returns clean JSON, so try direct parsing first
      const trimmedContent = content.trim();
      let parsed: any;

      try {
        // Direct parsing - this should work for GPT-4.1 Nano
        parsed = JSON.parse(trimmedContent);
        console.log('Direct JSON parsing successful');
      } catch (directParseError) {
        console.log('Direct JSON parsing failed, trying extraction methods...');
        console.log('Parse error:', directParseError);

        // If direct parsing fails, try to extract JSON from response
        let jsonStr = this.extractJSON(content);
        if (!jsonStr) {
          console.warn('No valid JSON found in AI response, using fallback analysis');
          return this.createBasicAnalysis(content, fileName);
        }

        try {
          // Try parsing the extracted JSON without cleaning first
          parsed = JSON.parse(jsonStr);
          console.log('Extracted JSON parsing successful');
        } catch (extractedParseError) {
          console.log('Extracted JSON parsing failed, trying cleanup...');
          console.log('Extracted parse error:', extractedParseError);
          console.log('JSON string before cleanup:', jsonStr.substring(0, 100) + '...');

          // Only clean if extraction also fails
          jsonStr = this.cleanupJSON(jsonStr);
          console.log('JSON string after cleanup:', jsonStr.substring(0, 100) + '...');
          parsed = JSON.parse(jsonStr);
          console.log('Cleaned JSON parsing successful');
        }
      }

      // Validate and normalize the response structure
      const normalized = this.normalizeAnalysisResponse(parsed, fileName);

      return normalized;
    } catch (error) {
      console.error('Error parsing analysis response:', error);
      console.error('Raw content preview:', content.substring(0, 500) + '...');

      // Try to extract partial information if full JSON parsing fails
      const fallback = this.extractPartialAnalysis(content, fileName);
      if (fallback) {
        console.log('Using fallback partial analysis');
        return fallback;
      }

      console.log('Using basic analysis fallback');
      return this.createBasicAnalysis(content, fileName);
    }
  }

  private extractPartialAnalysis(content: string, fileName: string): CodeAnalysisResult | null {
    try {
      // Try to extract individual sections even if the full JSON is malformed
      const result: CodeAnalysisResult = {
        issues: [],
        suggestions: [],
        metrics: {
          linesOfCode: 0,
          complexity: 0,
          maintainability: 5,
          performance: 5
        }
      };

      // Try to extract issues
      const issuesMatch = content.match(/"issues"\s*:\s*\[([\s\S]*?)\]/);
      if (issuesMatch) {
        try {
          const issuesJson = `[${issuesMatch[1]}]`;
          const issues = JSON.parse(issuesJson);
          if (Array.isArray(issues)) {
            result.issues = issues.map((issue: any) => ({
              type: issue.type || 'info',
              message: issue.message || 'Issue detected',
              file: fileName,
              line: issue.line,
              severity: issue.severity || 1
            }));
          }
        } catch (e) {
          console.warn('Could not parse issues section');
        }
      }

      // Try to extract metrics
      const metricsMatch = content.match(/"metrics"\s*:\s*\{([^}]*)\}/);
      if (metricsMatch) {
        try {
          const metricsJson = `{${metricsMatch[1]}}`;
          const metrics = JSON.parse(metricsJson);
          result.metrics = {
            linesOfCode: metrics.linesOfCode || 0,
            complexity: metrics.complexity || 0,
            maintainability: metrics.maintainability || 5,
            performance: metrics.performance || 5
          };
        } catch (e) {
          console.warn('Could not parse metrics section');
        }
      }

      return result;
    } catch (error) {
      console.warn('Could not extract partial analysis');
      return null;
    }
  }

  private extractJSON(content: string): string | null {
    // Try multiple patterns to find JSON
    const patterns = [
      /\{[\s\S]*\}/,  // Basic JSON object
      /```json\s*(\{[\s\S]*?\})\s*```/,  // JSON in code blocks
      /```\s*(\{[\s\S]*?\})\s*```/,  // JSON in generic code blocks
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1] || match[0];
      }
    }

    return null;
  }

  private cleanupJSON(jsonStr: string): string {
    // Remove common formatting issues
    let cleaned = jsonStr
      .replace(/,\s*}/g, '}')  // Remove trailing commas in objects
      .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
      .replace(/'/g, '"')      // Replace single quotes with double quotes
      .replace(/(\w+):/g, '"$1":')  // Quote unquoted keys
      .replace(/"\s*"/g, '""')  // Fix empty strings
      .replace(/\\'/g, "'")    // Fix escaped single quotes
      .replace(/\\"/g, '\\"')  // Ensure double quotes are properly escaped
      .replace(/\n/g, '\\n')   // Escape newlines in strings
      .replace(/\r/g, '\\r')   // Escape carriage returns
      .replace(/\t/g, '\\t')   // Escape tabs
      .trim();

    // Fix common issues with property names that got over-quoted
    cleaned = cleaned.replace(/"(\w+)":/g, '"$1":');

    // Fix issues where quotes inside strings break JSON
    // This is a simple approach - find strings and escape internal quotes
    cleaned = this.fixQuotesInStrings(cleaned);

    return cleaned;
  }

  private fixQuotesInStrings(jsonStr: string): string {
    // This is a simplified approach to fix quotes in string values
    // It's not perfect but handles most common cases
    try {
      // Try to parse as-is first
      JSON.parse(jsonStr);
      return jsonStr;
    } catch (error) {
      // If parsing fails, try to fix common quote issues
      let fixed = jsonStr;

      // Replace unescaped quotes in string values (basic heuristic)
      fixed = fixed.replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1\\"$2\\"$3":');
      fixed = fixed.replace(/:\s*"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}])/g, ': "$1\\"$2\\"$3"');

      return fixed;
    }
  }

  private normalizeAnalysisResponse(parsed: any, fileName: string): CodeAnalysisResult {
    const result: CodeAnalysisResult = {
      issues: [],
      suggestions: [],
      metrics: {
        linesOfCode: 0,
        complexity: 0,
        maintainability: 0,
        performance: 0
      }
    };

    // Normalize issues
    if (Array.isArray(parsed.issues)) {
      result.issues = parsed.issues.map((issue: any) => ({
        type: issue.type || 'info',
        message: issue.message || 'No message provided',
        file: fileName,
        line: issue.line || undefined,
        column: issue.column || undefined,
        severity: issue.severity || 1
      }));
    }

    // Normalize suggestions
    if (Array.isArray(parsed.suggestions)) {
      result.suggestions = parsed.suggestions.map((suggestion: any) => ({
        id: this.generateId(),
        type: suggestion.type || 'refactor',
        title: suggestion.title || 'Improvement suggestion',
        description: suggestion.description || 'No description provided',
        file: fileName,
        originalCode: suggestion.originalCode || '',
        suggestedCode: suggestion.suggestedCode || '',
        confidence: Math.min(Math.max(suggestion.confidence || 0.5, 0), 1),
        impact: suggestion.impact || 'medium',
        estimatedBenefit: suggestion.estimatedBenefit || 'Improved code quality'
      }));
    }

    // Normalize metrics
    if (parsed.metrics && typeof parsed.metrics === 'object') {
      result.metrics = {
        linesOfCode: Math.max(parsed.metrics.linesOfCode || 0, 0),
        complexity: Math.max(parsed.metrics.complexity || 0, 0),
        maintainability: Math.min(Math.max(parsed.metrics.maintainability || 5, 0), 10),
        performance: Math.min(Math.max(parsed.metrics.performance || 5, 0), 10)
      };
    }

    return result;
  }

  private parseImprovementResponse(
    content: string,
    fileName: string,
    type: string = 'refactor'
  ): CodeSuggestion | null {
    try {
      const trimmedContent = content.trim();
      let parsed: any;

      try {
        // Direct parsing for GPT-4.1 Nano clean JSON
        parsed = JSON.parse(trimmedContent);
      } catch (directParseError) {
        console.log('Direct improvement JSON parsing failed, trying extraction...');

        let jsonStr = this.extractJSON(content);
        if (!jsonStr) {
          console.warn('No valid JSON found in improvement response');
          return null;
        }

        try {
          parsed = JSON.parse(jsonStr);
        } catch (extractedParseError) {
          jsonStr = this.cleanupJSON(jsonStr);
          parsed = JSON.parse(jsonStr);
        }
      }

      // Normalize the improvement response
      return {
        id: this.generateId(),
        type: type as any,
        file: fileName,
        title: parsed.title || 'Code Improvement',
        description: parsed.description || 'No description provided',
        originalCode: parsed.originalCode || '',
        suggestedCode: parsed.suggestedCode || '',
        confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1),
        impact: parsed.impact || 'medium',
        estimatedBenefit: parsed.estimatedBenefit || 'Improved code quality'
      };
    } catch (error) {
      console.error('Error parsing improvement response:', error);
      console.error('Raw content preview:', content.substring(0, 200) + '...');
      return null;
    }
  }

  private createEmptyAnalysis(): CodeAnalysisResult {
    return {
      issues: [],
      suggestions: [],
      metrics: {
        linesOfCode: 0,
        complexity: 0,
        maintainability: 0,
        performance: 0
      }
    };
  }

  private createBasicAnalysis(code: string, fileName: string): CodeAnalysisResult {
    const lines = code.split('\n').length;
    const complexity = Math.min(Math.floor(lines / 10), 10);

    return {
      issues: [
        {
          type: 'info',
          message: 'AI analysis temporarily unavailable - showing basic metrics only',
          file: fileName,
          severity: 1
        }
      ],
      suggestions: [],
      metrics: {
        linesOfCode: lines,
        complexity: complexity,
        maintainability: Math.max(10 - complexity, 1),
        performance: 5
      }
    };
  }

  private createFallbackSuggestion(fileName: string, improvementType: string, content: string): CodeSuggestion {
    return {
      id: this.generateId(),
      type: improvementType as any,
      title: `${improvementType.charAt(0).toUpperCase() + improvementType.slice(1)} Improvement`,
      description: 'AI generated an improvement suggestion but the response format was invalid. Please review the raw suggestion.',
      file: fileName,
      originalCode: '// Original code section',
      suggestedCode: content.substring(0, 500) + (content.length > 500 ? '...' : ''),
      confidence: 0.3,
      impact: 'low',
      estimatedBenefit: 'Manual review required due to parsing issues'
    };
  }

  private getFileExtension(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'css':
        return 'css';
      case 'json':
        return 'json';
      default:
        return 'text';
    }
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

export const aiClient = new AIClient();
