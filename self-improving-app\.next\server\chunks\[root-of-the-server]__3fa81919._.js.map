{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/filesystem.ts"], "sourcesContent": ["import fs from 'fs-extra';\nimport path from 'path';\nimport { promisify } from 'util';\n\nexport interface FileInfo {\n  name: string;\n  path: string;\n  type: 'file' | 'directory';\n  size?: number;\n  lastModified?: Date;\n  content?: string;\n}\n\nexport interface BackupInfo {\n  id: string;\n  timestamp: Date;\n  files: string[];\n  description: string;\n}\n\nexport class FileSystemManager {\n  private projectRoot: string;\n  private backupDir: string;\n\n  constructor(projectRoot?: string) {\n    this.projectRoot = projectRoot || process.cwd();\n    this.backupDir = path.join(this.projectRoot, '.self-improving-backups');\n    this.ensureBackupDir();\n  }\n\n  private async ensureBackupDir(): Promise<void> {\n    await fs.ensureDir(this.backupDir);\n  }\n\n  async readFile(filePath: string): Promise<string> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    return await fs.readFile(fullPath, 'utf-8');\n  }\n\n  async writeFile(filePath: string, content: string): Promise<void> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    await fs.ensureDir(path.dirname(fullPath));\n    await fs.writeFile(fullPath, content, 'utf-8');\n  }\n\n  async fileExists(filePath: string): Promise<boolean> {\n    try {\n      const fullPath = path.resolve(this.projectRoot, filePath);\n      await fs.access(fullPath);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  async getFileInfo(filePath: string): Promise<FileInfo> {\n    const fullPath = path.resolve(this.projectRoot, filePath);\n    const stats = await fs.stat(fullPath);\n    \n    const info: FileInfo = {\n      name: path.basename(filePath),\n      path: filePath,\n      type: stats.isDirectory() ? 'directory' : 'file',\n      size: stats.size,\n      lastModified: stats.mtime,\n    };\n\n    if (info.type === 'file' && this.isTextFile(filePath)) {\n      try {\n        info.content = await this.readFile(filePath);\n      } catch (error) {\n        console.warn(`Could not read file content for ${filePath}:`, error);\n      }\n    }\n\n    return info;\n  }\n\n  async listDirectory(dirPath: string = '', recursive: boolean = false): Promise<FileInfo[]> {\n    const fullPath = path.resolve(this.projectRoot, dirPath);\n    const items = await fs.readdir(fullPath);\n    const fileInfos: FileInfo[] = [];\n\n    for (const item of items) {\n      // Skip node_modules, .git, and backup directories\n      if (this.shouldSkipPath(item)) continue;\n\n      const itemPath = path.join(dirPath, item);\n      try {\n        const info = await this.getFileInfo(itemPath);\n        fileInfos.push(info);\n\n        if (recursive && info.type === 'directory') {\n          const subItems = await this.listDirectory(itemPath, true);\n          fileInfos.push(...subItems);\n        }\n      } catch (error) {\n        console.warn(`Could not get info for ${itemPath}:`, error);\n      }\n    }\n\n    return fileInfos;\n  }\n\n  async createBackup(files: string[], description: string): Promise<string> {\n    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    const backupPath = path.join(this.backupDir, backupId);\n    \n    await fs.ensureDir(backupPath);\n\n    const backupInfo: BackupInfo = {\n      id: backupId,\n      timestamp: new Date(),\n      files: files,\n      description: description\n    };\n\n    // Copy files to backup directory\n    for (const file of files) {\n      try {\n        const sourcePath = path.resolve(this.projectRoot, file);\n        const targetPath = path.join(backupPath, file);\n        \n        await fs.ensureDir(path.dirname(targetPath));\n        await fs.copy(sourcePath, targetPath);\n      } catch (error) {\n        console.warn(`Could not backup file ${file}:`, error);\n      }\n    }\n\n    // Save backup metadata\n    await fs.writeJSON(path.join(backupPath, 'backup-info.json'), backupInfo, { spaces: 2 });\n\n    return backupId;\n  }\n\n  async restoreBackup(backupId: string): Promise<void> {\n    const backupPath = path.join(this.backupDir, backupId);\n    const backupInfoPath = path.join(backupPath, 'backup-info.json');\n    \n    if (!await fs.pathExists(backupInfoPath)) {\n      throw new Error(`Backup ${backupId} not found`);\n    }\n\n    const backupInfo: BackupInfo = await fs.readJSON(backupInfoPath);\n\n    for (const file of backupInfo.files) {\n      try {\n        const sourcePath = path.join(backupPath, file);\n        const targetPath = path.resolve(this.projectRoot, file);\n        \n        if (await fs.pathExists(sourcePath)) {\n          await fs.ensureDir(path.dirname(targetPath));\n          await fs.copy(sourcePath, targetPath);\n        }\n      } catch (error) {\n        console.warn(`Could not restore file ${file}:`, error);\n      }\n    }\n  }\n\n  async listBackups(): Promise<BackupInfo[]> {\n    const backups: BackupInfo[] = [];\n    \n    if (!await fs.pathExists(this.backupDir)) {\n      return backups;\n    }\n\n    const backupDirs = await fs.readdir(this.backupDir);\n    \n    for (const dir of backupDirs) {\n      try {\n        const backupInfoPath = path.join(this.backupDir, dir, 'backup-info.json');\n        if (await fs.pathExists(backupInfoPath)) {\n          const backupInfo = await fs.readJSON(backupInfoPath);\n          // Convert timestamp string back to Date object\n          if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {\n            backupInfo.timestamp = new Date(backupInfo.timestamp);\n          }\n          backups.push(backupInfo);\n        }\n      } catch (error) {\n        console.warn(`Could not read backup info for ${dir}:`, error);\n      }\n    }\n\n    return backups.sort((a, b) => {\n      // Handle case where timestamp might be missing or invalid\n      const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;\n      const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;\n      return timeB - timeA;\n    });\n  }\n\n  private isTextFile(filePath: string): boolean {\n    const textExtensions = [\n      '.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.txt', '.css', '.scss',\n      '.html', '.xml', '.yml', '.yaml', '.env', '.gitignore', '.eslintrc',\n      '.prettierrc', '.config', '.lock'\n    ];\n    \n    const ext = path.extname(filePath).toLowerCase();\n    return textExtensions.includes(ext) || !ext;\n  }\n\n  private shouldSkipPath(pathName: string): boolean {\n    const skipPatterns = [\n      'node_modules',\n      '.git',\n      '.next',\n      'dist',\n      'build',\n      '.self-improving-backups',\n      '.env.local',\n      '.DS_Store',\n      'Thumbs.db'\n    ];\n    \n    return skipPatterns.some(pattern => pathName.includes(pattern));\n  }\n\n  getProjectRoot(): string {\n    return this.projectRoot;\n  }\n}\n\nexport const fileSystem = new FileSystemManager();\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAmBO,MAAM;IACH,YAAoB;IACpB,UAAkB;IAE1B,YAAY,WAAoB,CAAE;QAChC,IAAI,CAAC,WAAW,GAAG,eAAe,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QAC7C,IAAI,CAAC,eAAe;IACtB;IAEA,MAAc,kBAAiC;QAC7C,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;IACnC;IAEA,MAAM,SAAS,QAAgB,EAAmB;QAChD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,OAAO,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,UAAU;IACrC;IAEA,MAAM,UAAU,QAAgB,EAAE,OAAe,EAAiB;QAChE,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,UAAU,SAAS;IACxC;IAEA,MAAM,WAAW,QAAgB,EAAoB;QACnD,IAAI;YACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAChD,MAAM,6IAAA,CAAA,UAAE,CAAC,MAAM,CAAC;YAChB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,YAAY,QAAgB,EAAqB;QACrD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,QAAQ,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC;QAE5B,MAAM,OAAiB;YACrB,MAAM,iGAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;YACpB,MAAM;YACN,MAAM,MAAM,WAAW,KAAK,cAAc;YAC1C,MAAM,MAAM,IAAI;YAChB,cAAc,MAAM,KAAK;QAC3B;QAEA,IAAI,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACrD,IAAI;gBACF,KAAK,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;YAC/D;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAE,EAAE,YAAqB,KAAK,EAAuB;QACzF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QAChD,MAAM,QAAQ,MAAM,6IAAA,CAAA,UAAE,CAAC,OAAO,CAAC;QAC/B,MAAM,YAAwB,EAAE;QAEhC,KAAK,MAAM,QAAQ,MAAO;YACxB,kDAAkD;YAClD,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO;YAE/B,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,SAAS;YACpC,IAAI;gBACF,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;gBACpC,UAAU,IAAI,CAAC;gBAEf,IAAI,aAAa,KAAK,IAAI,KAAK,aAAa;oBAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU;oBACpD,UAAU,IAAI,IAAI;gBACpB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,EAAE;YACtD;QACF;QAEA,OAAO;IACT;IAEA,MAAM,aAAa,KAAe,EAAE,WAAmB,EAAmB;QACxE,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAClF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAE7C,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC;QAEnB,MAAM,aAAyB;YAC7B,IAAI;YACJ,WAAW,IAAI;YACf,OAAO;YACP,aAAa;QACf;QAEA,iCAAiC;QACjC,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;gBAClD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;gBAEzC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC,YAAY;YAC5B,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC,EAAE;YACjD;QACF;QAEA,uBAAuB;QACvB,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY,qBAAqB,YAAY;YAAE,QAAQ;QAAE;QAEtF,OAAO;IACT;IAEA,MAAM,cAAc,QAAgB,EAAiB;QACnD,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7C,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;QAE7C,IAAI,CAAC,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;YACxC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,SAAS,UAAU,CAAC;QAChD;QAEA,MAAM,aAAyB,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;QAEjD,KAAK,MAAM,QAAQ,WAAW,KAAK,CAAE;YACnC,IAAI;gBACF,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,YAAY;gBACzC,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;gBAElD,IAAI,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,aAAa;oBACnC,MAAM,6IAAA,CAAA,UAAE,CAAC,SAAS,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAChC,MAAM,6IAAA,CAAA,UAAE,CAAC,IAAI,CAAC,YAAY;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,EAAE;YAClD;QACF;IACF;IAEA,MAAM,cAAqC;QACzC,MAAM,UAAwB,EAAE;QAEhC,IAAI,CAAC,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,GAAG;YACxC,OAAO;QACT;QAEA,MAAM,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS;QAElD,KAAK,MAAM,OAAO,WAAY;YAC5B,IAAI;gBACF,MAAM,iBAAiB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK;gBACtD,IAAI,MAAM,6IAAA,CAAA,UAAE,CAAC,UAAU,CAAC,iBAAiB;oBACvC,MAAM,aAAa,MAAM,6IAAA,CAAA,UAAE,CAAC,QAAQ,CAAC;oBACrC,+CAA+C;oBAC/C,IAAI,WAAW,SAAS,IAAI,OAAO,WAAW,SAAS,KAAK,UAAU;wBACpE,WAAW,SAAS,GAAG,IAAI,KAAK,WAAW,SAAS;oBACtD;oBACA,QAAQ,IAAI,CAAC;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC,EAAE;YACzD;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG;YACtB,0DAA0D;YAC1D,MAAM,QAAQ,EAAE,SAAS,YAAY,OAAO,EAAE,SAAS,CAAC,OAAO,KAAK;YACpE,MAAM,QAAQ,EAAE,SAAS,YAAY,OAAO,EAAE,SAAS,CAAC,OAAO,KAAK;YACpE,OAAO,QAAQ;QACjB;IACF;IAEQ,WAAW,QAAgB,EAAW;QAC5C,MAAM,iBAAiB;YACrB;YAAO;YAAQ;YAAO;YAAQ;YAAS;YAAO;YAAQ;YAAQ;YAC9D;YAAS;YAAQ;YAAQ;YAAS;YAAQ;YAAc;YACxD;YAAe;YAAW;SAC3B;QAED,MAAM,MAAM,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;QAC9C,OAAO,eAAe,QAAQ,CAAC,QAAQ,CAAC;IAC1C;IAEQ,eAAe,QAAgB,EAAW;QAChD,MAAM,eAAe;YACnB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,SAAS,QAAQ,CAAC;IACxD;IAEA,iBAAyB;QACvB,OAAO,IAAI,CAAC,WAAW;IACzB;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/ai-client.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nexport interface CodeAnalysisResult {\n  issues: CodeIssue[];\n  suggestions: CodeSuggestion[];\n  metrics: CodeMetrics;\n}\n\nexport interface CodeIssue {\n  type: 'error' | 'warning' | 'info';\n  message: string;\n  file: string;\n  line?: number;\n  column?: number;\n  severity: number; // 1-10\n}\n\nexport interface CodeSuggestion {\n  id: string;\n  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';\n  title: string;\n  description: string;\n  file: string;\n  originalCode: string;\n  suggestedCode: string;\n  confidence: number; // 0-1\n  impact: 'low' | 'medium' | 'high';\n  estimatedBenefit: string;\n}\n\nexport interface CodeMetrics {\n  linesOfCode: number;\n  complexity: number;\n  maintainability: number;\n  testCoverage?: number;\n  performance: number;\n}\n\nexport class AIClient {\n  private client: OpenAI;\n  private model: string;\n\n  constructor() {\n    if (!process.env.OPENROUTER_API_KEY) {\n      throw new Error('OPENROUTER_API_KEY environment variable is required');\n    }\n\n    this.client = new OpenAI({\n      baseURL: 'https://openrouter.ai/api/v1',\n      apiKey: process.env.OPENROUTER_API_KEY,\n      defaultHeaders: {\n        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n        'X-Title': process.env.NEXT_PUBLIC_APP_NAME || 'Self-Improving App',\n      },\n    });\n\n    // Default to GPT-4.1 Nano as specified\n    this.model = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-nano';\n  }\n\n  async analyzeCode(code: string, fileName: string): Promise<CodeAnalysisResult> {\n    const prompt = this.createAnalysisPrompt(code, fileName);\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert code analyzer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.2,\n        max_tokens: 3000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        console.warn('No response content from AI model');\n        return this.createEmptyAnalysis();\n      }\n\n      const result = this.parseAnalysisResponse(content, fileName);\n\n      // If parsing failed, create a basic analysis with some default metrics\n      if (result.issues.length === 0 && result.suggestions.length === 0 && result.metrics.linesOfCode === 0) {\n        return this.createBasicAnalysis(code, fileName);\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error analyzing code:', error);\n      return this.createBasicAnalysis(code, fileName);\n    }\n  }\n\n  async generateImprovement(\n    code: string,\n    fileName: string,\n    improvementType: string,\n    context?: string\n  ): Promise<CodeSuggestion | null> {\n    const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert software engineer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.3,\n        max_tokens: 2000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        console.warn('No response content from AI model for improvement generation');\n        return null;\n      }\n\n      const result = this.parseImprovementResponse(content, fileName, improvementType);\n\n      // If parsing failed but we have content, try to create a basic suggestion\n      if (!result && content.length > 10) {\n        console.warn('Failed to parse improvement response, creating fallback suggestion');\n        return this.createFallbackSuggestion(fileName, improvementType, content);\n      }\n\n      return result;\n    } catch (error) {\n      console.error('Error generating improvement:', error);\n      return null;\n    }\n  }\n\n  async generateNewFeature(\n    description: string,\n    existingCode: string,\n    fileName: string\n  ): Promise<CodeSuggestion | null> {\n    const prompt = `\nGenerate a new feature based on this description: \"${description}\"\n\nExisting code context:\n\\`\\`\\`${this.getFileExtension(fileName)}\n${existingCode}\n\\`\\`\\`\n\nPlease provide:\n1. A clear implementation plan\n2. The new code to add\n3. Any modifications needed to existing code\n4. Potential impacts and considerations\n\nReturn your response as JSON with this structure:\n{\n  \"title\": \"Feature title\",\n  \"description\": \"Detailed description\",\n  \"originalCode\": \"existing code that needs modification\",\n  \"suggestedCode\": \"new/modified code\",\n  \"confidence\": 0.8,\n  \"impact\": \"medium\",\n  \"estimatedBenefit\": \"description of benefits\"\n}\n`;\n\n    try {\n      const response = await this.client.chat.completions.create({\n        model: this.model,\n        messages: [\n          {\n            role: 'system',\n            content: 'You are an expert software engineer specializing in feature development.'\n          },\n          {\n            role: 'user',\n            content: prompt\n          }\n        ],\n        temperature: 0.5,\n        max_tokens: 2000,\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        return null;\n      }\n\n      return this.parseImprovementResponse(content, fileName, 'feature');\n    } catch (error) {\n      console.error('Error generating new feature:', error);\n      return null;\n    }\n  }\n\n  private createAnalysisPrompt(code: string, fileName: string): string {\n    return `\nAnalyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:\n\nFile: ${fileName}\n\\`\\`\\`${this.getFileExtension(fileName)}\n${code}\n\\`\\`\\`\n\nIMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text before or after the JSON.\n\nRequired JSON structure:\n{\n  \"issues\": [\n    {\n      \"type\": \"error\",\n      \"message\": \"Issue description\",\n      \"line\": 10,\n      \"severity\": 5\n    }\n  ],\n  \"suggestions\": [\n    {\n      \"type\": \"performance\",\n      \"title\": \"Suggestion title\",\n      \"description\": \"Detailed description\",\n      \"originalCode\": \"code to replace\",\n      \"suggestedCode\": \"replacement code\",\n      \"confidence\": 0.8,\n      \"impact\": \"medium\",\n      \"estimatedBenefit\": \"Description of benefits\"\n    }\n  ],\n  \"metrics\": {\n    \"linesOfCode\": 50,\n    \"complexity\": 3,\n    \"maintainability\": 8,\n    \"performance\": 7\n  }\n}\n\nEnsure all string values are properly quoted and the JSON is valid.`;\n  }\n\n  private createImprovementPrompt(\n    code: string,\n    fileName: string,\n    improvementType: string,\n    context?: string\n  ): string {\n    return `\nGenerate a ${improvementType} improvement for this code:\n\nFile: ${fileName}\n${context ? `Context: ${context}` : ''}\n\n\\`\\`\\`${this.getFileExtension(fileName)}\n${code}\n\\`\\`\\`\n\nIMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text.\n\nFocus on ${improvementType} improvements and return this exact JSON structure:\n{\n  \"title\": \"Improvement title\",\n  \"description\": \"Detailed description of the improvement\",\n  \"originalCode\": \"Exact code to be replaced\",\n  \"suggestedCode\": \"Improved replacement code\",\n  \"confidence\": 0.8,\n  \"impact\": \"medium\",\n  \"estimatedBenefit\": \"Description of expected benefits\"\n}\n\nEnsure all strings are properly quoted and the JSON is valid.`;\n  }\n\n  private parseAnalysisResponse(content: string, fileName: string): CodeAnalysisResult {\n    try {\n      // GPT-4.1 Nano returns clean JSON, so try direct parsing first\n      const trimmedContent = content.trim();\n      let parsed: any;\n\n      try {\n        // Direct parsing - this should work for GPT-4.1 Nano\n        parsed = JSON.parse(trimmedContent);\n        console.log('Direct JSON parsing successful');\n      } catch (directParseError) {\n        console.log('Direct JSON parsing failed, trying extraction methods...');\n        console.log('Parse error:', directParseError);\n\n        // If direct parsing fails, try to extract JSON from response\n        let jsonStr = this.extractJSON(content);\n        if (!jsonStr) {\n          console.warn('No valid JSON found in AI response, using fallback analysis');\n          return this.createBasicAnalysis(content, fileName);\n        }\n\n        try {\n          // Try parsing the extracted JSON without cleaning first\n          parsed = JSON.parse(jsonStr);\n          console.log('Extracted JSON parsing successful');\n        } catch (extractedParseError) {\n          console.log('Extracted JSON parsing failed, trying cleanup...');\n          console.log('Extracted parse error:', extractedParseError);\n          console.log('JSON string before cleanup:', jsonStr.substring(0, 100) + '...');\n\n          // Only clean if extraction also fails\n          jsonStr = this.cleanupJSON(jsonStr);\n          console.log('JSON string after cleanup:', jsonStr.substring(0, 100) + '...');\n          parsed = JSON.parse(jsonStr);\n          console.log('Cleaned JSON parsing successful');\n        }\n      }\n\n      // Validate and normalize the response structure\n      const normalized = this.normalizeAnalysisResponse(parsed, fileName);\n\n      return normalized;\n    } catch (error) {\n      console.error('Error parsing analysis response:', error);\n      console.error('Raw content preview:', content.substring(0, 500) + '...');\n\n      // Try to extract partial information if full JSON parsing fails\n      const fallback = this.extractPartialAnalysis(content, fileName);\n      if (fallback) {\n        console.log('Using fallback partial analysis');\n        return fallback;\n      }\n\n      console.log('Using basic analysis fallback');\n      return this.createBasicAnalysis(content, fileName);\n    }\n  }\n\n  private extractPartialAnalysis(content: string, fileName: string): CodeAnalysisResult | null {\n    try {\n      // Try to extract individual sections even if the full JSON is malformed\n      const result: CodeAnalysisResult = {\n        issues: [],\n        suggestions: [],\n        metrics: {\n          linesOfCode: 0,\n          complexity: 0,\n          maintainability: 5,\n          performance: 5\n        }\n      };\n\n      // Try to extract issues\n      const issuesMatch = content.match(/\"issues\"\\s*:\\s*\\[([\\s\\S]*?)\\]/);\n      if (issuesMatch) {\n        try {\n          const issuesJson = `[${issuesMatch[1]}]`;\n          const issues = JSON.parse(issuesJson);\n          if (Array.isArray(issues)) {\n            result.issues = issues.map((issue: any) => ({\n              type: issue.type || 'info',\n              message: issue.message || 'Issue detected',\n              file: fileName,\n              line: issue.line,\n              severity: issue.severity || 1\n            }));\n          }\n        } catch (e) {\n          console.warn('Could not parse issues section');\n        }\n      }\n\n      // Try to extract metrics\n      const metricsMatch = content.match(/\"metrics\"\\s*:\\s*\\{([^}]*)\\}/);\n      if (metricsMatch) {\n        try {\n          const metricsJson = `{${metricsMatch[1]}}`;\n          const metrics = JSON.parse(metricsJson);\n          result.metrics = {\n            linesOfCode: metrics.linesOfCode || 0,\n            complexity: metrics.complexity || 0,\n            maintainability: metrics.maintainability || 5,\n            performance: metrics.performance || 5\n          };\n        } catch (e) {\n          console.warn('Could not parse metrics section');\n        }\n      }\n\n      return result;\n    } catch (error) {\n      console.warn('Could not extract partial analysis');\n      return null;\n    }\n  }\n\n  private extractJSON(content: string): string | null {\n    // Try multiple patterns to find JSON\n    const patterns = [\n      /\\{[\\s\\S]*\\}/,  // Basic JSON object\n      /```json\\s*(\\{[\\s\\S]*?\\})\\s*```/,  // JSON in code blocks\n      /```\\s*(\\{[\\s\\S]*?\\})\\s*```/,  // JSON in generic code blocks\n    ];\n\n    for (const pattern of patterns) {\n      const match = content.match(pattern);\n      if (match) {\n        return match[1] || match[0];\n      }\n    }\n\n    return null;\n  }\n\n  private cleanupJSON(jsonStr: string): string {\n    // Remove common formatting issues\n    let cleaned = jsonStr\n      .replace(/,\\s*}/g, '}')  // Remove trailing commas in objects\n      .replace(/,\\s*]/g, ']')  // Remove trailing commas in arrays\n      .replace(/'/g, '\"')      // Replace single quotes with double quotes\n      .replace(/(\\w+):/g, '\"$1\":')  // Quote unquoted keys\n      .replace(/\"\\s*\"/g, '\"\"')  // Fix empty strings\n      .replace(/\\\\'/g, \"'\")    // Fix escaped single quotes\n      .replace(/\\\\\"/g, '\\\\\"')  // Ensure double quotes are properly escaped\n      .replace(/\\n/g, '\\\\n')   // Escape newlines in strings\n      .replace(/\\r/g, '\\\\r')   // Escape carriage returns\n      .replace(/\\t/g, '\\\\t')   // Escape tabs\n      .trim();\n\n    // Fix common issues with property names that got over-quoted\n    cleaned = cleaned.replace(/\"(\\w+)\":/g, '\"$1\":');\n\n    // Fix issues where quotes inside strings break JSON\n    // This is a simple approach - find strings and escape internal quotes\n    cleaned = this.fixQuotesInStrings(cleaned);\n\n    return cleaned;\n  }\n\n  private fixQuotesInStrings(jsonStr: string): string {\n    // This is a simplified approach to fix quotes in string values\n    // It's not perfect but handles most common cases\n    try {\n      // Try to parse as-is first\n      JSON.parse(jsonStr);\n      return jsonStr;\n    } catch (error) {\n      // If parsing fails, try to fix common quote issues\n      let fixed = jsonStr;\n\n      // Replace unescaped quotes in string values (basic heuristic)\n      fixed = fixed.replace(/\"([^\"]*)\"([^\"]*)\"([^\"]*)\":/g, '\"$1\\\\\"$2\\\\\"$3\":');\n      fixed = fixed.replace(/:\\s*\"([^\"]*)\"([^\"]*)\"([^\"]*)\"(?=\\s*[,}])/g, ': \"$1\\\\\"$2\\\\\"$3\"');\n\n      return fixed;\n    }\n  }\n\n  private normalizeAnalysisResponse(parsed: any, fileName: string): CodeAnalysisResult {\n    const result: CodeAnalysisResult = {\n      issues: [],\n      suggestions: [],\n      metrics: {\n        linesOfCode: 0,\n        complexity: 0,\n        maintainability: 0,\n        performance: 0\n      }\n    };\n\n    // Normalize issues\n    if (Array.isArray(parsed.issues)) {\n      result.issues = parsed.issues.map((issue: any) => ({\n        type: issue.type || 'info',\n        message: issue.message || 'No message provided',\n        file: fileName,\n        line: issue.line || undefined,\n        column: issue.column || undefined,\n        severity: issue.severity || 1\n      }));\n    }\n\n    // Normalize suggestions\n    if (Array.isArray(parsed.suggestions)) {\n      result.suggestions = parsed.suggestions.map((suggestion: any) => ({\n        id: this.generateId(),\n        type: suggestion.type || 'refactor',\n        title: suggestion.title || 'Improvement suggestion',\n        description: suggestion.description || 'No description provided',\n        file: fileName,\n        originalCode: suggestion.originalCode || '',\n        suggestedCode: suggestion.suggestedCode || '',\n        confidence: Math.min(Math.max(suggestion.confidence || 0.5, 0), 1),\n        impact: suggestion.impact || 'medium',\n        estimatedBenefit: suggestion.estimatedBenefit || 'Improved code quality'\n      }));\n    }\n\n    // Normalize metrics\n    if (parsed.metrics && typeof parsed.metrics === 'object') {\n      result.metrics = {\n        linesOfCode: Math.max(parsed.metrics.linesOfCode || 0, 0),\n        complexity: Math.max(parsed.metrics.complexity || 0, 0),\n        maintainability: Math.min(Math.max(parsed.metrics.maintainability || 5, 0), 10),\n        performance: Math.min(Math.max(parsed.metrics.performance || 5, 0), 10)\n      };\n    }\n\n    return result;\n  }\n\n  private parseImprovementResponse(\n    content: string,\n    fileName: string,\n    type: string = 'refactor'\n  ): CodeSuggestion | null {\n    try {\n      const trimmedContent = content.trim();\n      let parsed: any;\n\n      try {\n        // Direct parsing for GPT-4.1 Nano clean JSON\n        parsed = JSON.parse(trimmedContent);\n      } catch (directParseError) {\n        console.log('Direct improvement JSON parsing failed, trying extraction...');\n\n        let jsonStr = this.extractJSON(content);\n        if (!jsonStr) {\n          console.warn('No valid JSON found in improvement response');\n          return null;\n        }\n\n        try {\n          parsed = JSON.parse(jsonStr);\n        } catch (extractedParseError) {\n          jsonStr = this.cleanupJSON(jsonStr);\n          parsed = JSON.parse(jsonStr);\n        }\n      }\n\n      // Normalize the improvement response\n      return {\n        id: this.generateId(),\n        type: type as any,\n        file: fileName,\n        title: parsed.title || 'Code Improvement',\n        description: parsed.description || 'No description provided',\n        originalCode: parsed.originalCode || '',\n        suggestedCode: parsed.suggestedCode || '',\n        confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1),\n        impact: parsed.impact || 'medium',\n        estimatedBenefit: parsed.estimatedBenefit || 'Improved code quality'\n      };\n    } catch (error) {\n      console.error('Error parsing improvement response:', error);\n      console.error('Raw content preview:', content.substring(0, 200) + '...');\n      return null;\n    }\n  }\n\n  private createEmptyAnalysis(): CodeAnalysisResult {\n    return {\n      issues: [],\n      suggestions: [],\n      metrics: {\n        linesOfCode: 0,\n        complexity: 0,\n        maintainability: 0,\n        performance: 0\n      }\n    };\n  }\n\n  private createBasicAnalysis(code: string, fileName: string): CodeAnalysisResult {\n    const lines = code.split('\\n').length;\n    const complexity = Math.min(Math.floor(lines / 10), 10);\n\n    return {\n      issues: [\n        {\n          type: 'info',\n          message: 'AI analysis temporarily unavailable - showing basic metrics only',\n          file: fileName,\n          severity: 1\n        }\n      ],\n      suggestions: [],\n      metrics: {\n        linesOfCode: lines,\n        complexity: complexity,\n        maintainability: Math.max(10 - complexity, 1),\n        performance: 5\n      }\n    };\n  }\n\n  private createFallbackSuggestion(fileName: string, improvementType: string, content: string): CodeSuggestion {\n    return {\n      id: this.generateId(),\n      type: improvementType as any,\n      title: `${improvementType.charAt(0).toUpperCase() + improvementType.slice(1)} Improvement`,\n      description: 'AI generated an improvement suggestion but the response format was invalid. Please review the raw suggestion.',\n      file: fileName,\n      originalCode: '// Original code section',\n      suggestedCode: content.substring(0, 500) + (content.length > 500 ? '...' : ''),\n      confidence: 0.3,\n      impact: 'low',\n      estimatedBenefit: 'Manual review required due to parsing issues'\n    };\n  }\n\n  private getFileExtension(fileName: string): string {\n    const ext = fileName.split('.').pop()?.toLowerCase();\n    switch (ext) {\n      case 'ts':\n      case 'tsx':\n        return 'typescript';\n      case 'js':\n      case 'jsx':\n        return 'javascript';\n      case 'css':\n        return 'css';\n      case 'json':\n        return 'json';\n      default:\n        return 'text';\n    }\n  }\n\n  private generateId(): string {\n    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }\n}\n\nexport const aiClient = new AIClient();\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAsCO,MAAM;IACH,OAAe;IACf,MAAc;IAEtB,aAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YACnC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,MAAM,GAAG,IAAI,wKAAA,CAAA,UAAM,CAAC;YACvB,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACtC,gBAAgB;gBACd,gBAAgB,6DAAmC;gBACnD,WAAW,0DAAoC;YACjD;QACF;QAEA,uCAAuC;QACvC,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAC/C;IAEA,MAAM,YAAY,IAAY,EAAE,QAAgB,EAA+B;QAC7E,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,MAAM;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,mBAAmB;YACjC;YAEA,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,SAAS;YAEnD,uEAAuE;YACvE,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,KAAK,OAAO,WAAW,CAAC,MAAM,KAAK,KAAK,OAAO,OAAO,CAAC,WAAW,KAAK,GAAG;gBACrG,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACxC;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACxC;IACF;IAEA,MAAM,oBACJ,IAAY,EACZ,QAAgB,EAChB,eAAuB,EACvB,OAAgB,EACgB;QAChC,MAAM,SAAS,IAAI,CAAC,uBAAuB,CAAC,MAAM,UAAU,iBAAiB;QAE7E,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;YAEA,MAAM,SAAS,IAAI,CAAC,wBAAwB,CAAC,SAAS,UAAU;YAEhE,0EAA0E;YAC1E,IAAI,CAAC,UAAU,QAAQ,MAAM,GAAG,IAAI;gBAClC,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,wBAAwB,CAAC,UAAU,iBAAiB;YAClE;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEA,MAAM,mBACJ,WAAmB,EACnB,YAAoB,EACpB,QAAgB,EACgB;QAChC,MAAM,SAAS,CAAC;mDAC+B,EAAE,YAAY;;;MAG3D,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,aAAa;;;;;;;;;;;;;;;;;;;AAmBf,CAAC;QAEG,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,OAAO;YACT;YAEA,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,UAAU;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;QACT;IACF;IAEQ,qBAAqB,IAAY,EAAE,QAAgB,EAAU;QACnE,OAAO,CAAC;aACC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;;MAEzC,EAAE,SAAS;MACX,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mEAmC4D,CAAC;IAClE;IAEQ,wBACN,IAAY,EACZ,QAAgB,EAChB,eAAuB,EACvB,OAAgB,EACR;QACR,OAAO,CAAC;WACD,EAAE,gBAAgB;;MAEvB,EAAE,SAAS;AACjB,EAAE,UAAU,CAAC,SAAS,EAAE,SAAS,GAAG,GAAG;;MAEjC,EAAE,IAAI,CAAC,gBAAgB,CAAC,UAAU;AACxC,EAAE,KAAK;;;;;SAKE,EAAE,gBAAgB;;;;;;;;;;;6DAWkC,CAAC;IAC5D;IAEQ,sBAAsB,OAAe,EAAE,QAAgB,EAAsB;QACnF,IAAI;YACF,+DAA+D;YAC/D,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI;YAEJ,IAAI;gBACF,qDAAqD;gBACrD,SAAS,KAAK,KAAK,CAAC;gBACpB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,gBAAgB;gBAE5B,6DAA6D;gBAC7D,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC/B,IAAI,CAAC,SAAS;oBACZ,QAAQ,IAAI,CAAC;oBACb,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;gBAC3C;gBAEA,IAAI;oBACF,wDAAwD;oBACxD,SAAS,KAAK,KAAK,CAAC;oBACpB,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,qBAAqB;oBAC5B,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,0BAA0B;oBACtC,QAAQ,GAAG,CAAC,+BAA+B,QAAQ,SAAS,CAAC,GAAG,OAAO;oBAEvE,sCAAsC;oBACtC,UAAU,IAAI,CAAC,WAAW,CAAC;oBAC3B,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,SAAS,CAAC,GAAG,OAAO;oBACtE,SAAS,KAAK,KAAK,CAAC;oBACpB,QAAQ,GAAG,CAAC;gBACd;YACF;YAEA,gDAAgD;YAChD,MAAM,aAAa,IAAI,CAAC,yBAAyB,CAAC,QAAQ;YAE1D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAElE,gEAAgE;YAChE,MAAM,WAAW,IAAI,CAAC,sBAAsB,CAAC,SAAS;YACtD,IAAI,UAAU;gBACZ,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,IAAI,CAAC,mBAAmB,CAAC,SAAS;QAC3C;IACF;IAEQ,uBAAuB,OAAe,EAAE,QAAgB,EAA6B;QAC3F,IAAI;YACF,wEAAwE;YACxE,MAAM,SAA6B;gBACjC,QAAQ,EAAE;gBACV,aAAa,EAAE;gBACf,SAAS;oBACP,aAAa;oBACb,YAAY;oBACZ,iBAAiB;oBACjB,aAAa;gBACf;YACF;YAEA,wBAAwB;YACxB,MAAM,cAAc,QAAQ,KAAK,CAAC;YAClC,IAAI,aAAa;gBACf,IAAI;oBACF,MAAM,aAAa,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;oBACxC,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,IAAI,MAAM,OAAO,CAAC,SAAS;wBACzB,OAAO,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,QAAe,CAAC;gCAC1C,MAAM,MAAM,IAAI,IAAI;gCACpB,SAAS,MAAM,OAAO,IAAI;gCAC1B,MAAM;gCACN,MAAM,MAAM,IAAI;gCAChB,UAAU,MAAM,QAAQ,IAAI;4BAC9B,CAAC;oBACH;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,yBAAyB;YACzB,MAAM,eAAe,QAAQ,KAAK,CAAC;YACnC,IAAI,cAAc;gBAChB,IAAI;oBACF,MAAM,cAAc,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1C,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,OAAO,GAAG;wBACf,aAAa,QAAQ,WAAW,IAAI;wBACpC,YAAY,QAAQ,UAAU,IAAI;wBAClC,iBAAiB,QAAQ,eAAe,IAAI;wBAC5C,aAAa,QAAQ,WAAW,IAAI;oBACtC;gBACF,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;IACF;IAEQ,YAAY,OAAe,EAAiB;QAClD,qCAAqC;QACrC,MAAM,WAAW;YACf;YACA;YACA;SACD;QAED,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YAC7B;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,OAAe,EAAU;QAC3C,kCAAkC;QAClC,IAAI,UAAU,QACX,OAAO,CAAC,UAAU,KAAM,oCAAoC;SAC5D,OAAO,CAAC,UAAU,KAAM,mCAAmC;SAC3D,OAAO,CAAC,MAAM,KAAU,2CAA2C;SACnE,OAAO,CAAC,WAAW,SAAU,sBAAsB;SACnD,OAAO,CAAC,UAAU,MAAO,oBAAoB;SAC7C,OAAO,CAAC,QAAQ,KAAQ,4BAA4B;SACpD,OAAO,CAAC,QAAQ,OAAQ,4CAA4C;SACpE,OAAO,CAAC,OAAO,OAAS,6BAA6B;SACrD,OAAO,CAAC,OAAO,OAAS,0BAA0B;SAClD,OAAO,CAAC,OAAO,OAAS,cAAc;SACtC,IAAI;QAEP,6DAA6D;QAC7D,UAAU,QAAQ,OAAO,CAAC,aAAa;QAEvC,oDAAoD;QACpD,sEAAsE;QACtE,UAAU,IAAI,CAAC,kBAAkB,CAAC;QAElC,OAAO;IACT;IAEQ,mBAAmB,OAAe,EAAU;QAClD,+DAA+D;QAC/D,iDAAiD;QACjD,IAAI;YACF,2BAA2B;YAC3B,KAAK,KAAK,CAAC;YACX,OAAO;QACT,EAAE,OAAO,OAAO;YACd,mDAAmD;YACnD,IAAI,QAAQ;YAEZ,8DAA8D;YAC9D,QAAQ,MAAM,OAAO,CAAC,+BAA+B;YACrD,QAAQ,MAAM,OAAO,CAAC,6CAA6C;YAEnE,OAAO;QACT;IACF;IAEQ,0BAA0B,MAAW,EAAE,QAAgB,EAAsB;QACnF,MAAM,SAA6B;YACjC,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB;gBACjB,aAAa;YACf;QACF;QAEA,mBAAmB;QACnB,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG;YAChC,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,QAAe,CAAC;oBACjD,MAAM,MAAM,IAAI,IAAI;oBACpB,SAAS,MAAM,OAAO,IAAI;oBAC1B,MAAM;oBACN,MAAM,MAAM,IAAI,IAAI;oBACpB,QAAQ,MAAM,MAAM,IAAI;oBACxB,UAAU,MAAM,QAAQ,IAAI;gBAC9B,CAAC;QACH;QAEA,wBAAwB;QACxB,IAAI,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG;YACrC,OAAO,WAAW,GAAG,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,aAAoB,CAAC;oBAChE,IAAI,IAAI,CAAC,UAAU;oBACnB,MAAM,WAAW,IAAI,IAAI;oBACzB,OAAO,WAAW,KAAK,IAAI;oBAC3B,aAAa,WAAW,WAAW,IAAI;oBACvC,MAAM;oBACN,cAAc,WAAW,YAAY,IAAI;oBACzC,eAAe,WAAW,aAAa,IAAI;oBAC3C,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,WAAW,UAAU,IAAI,KAAK,IAAI;oBAChE,QAAQ,WAAW,MAAM,IAAI;oBAC7B,kBAAkB,WAAW,gBAAgB,IAAI;gBACnD,CAAC;QACH;QAEA,oBAAoB;QACpB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;YACxD,OAAO,OAAO,GAAG;gBACf,aAAa,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvD,YAAY,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,UAAU,IAAI,GAAG;gBACrD,iBAAiB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,eAAe,IAAI,GAAG,IAAI;gBAC5E,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,WAAW,IAAI,GAAG,IAAI;YACtE;QACF;QAEA,OAAO;IACT;IAEQ,yBACN,OAAe,EACf,QAAgB,EAChB,OAAe,UAAU,EACF;QACvB,IAAI;YACF,MAAM,iBAAiB,QAAQ,IAAI;YACnC,IAAI;YAEJ,IAAI;gBACF,6CAA6C;gBAC7C,SAAS,KAAK,KAAK,CAAC;YACtB,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,GAAG,CAAC;gBAEZ,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC;gBAC/B,IAAI,CAAC,SAAS;oBACZ,QAAQ,IAAI,CAAC;oBACb,OAAO;gBACT;gBAEA,IAAI;oBACF,SAAS,KAAK,KAAK,CAAC;gBACtB,EAAE,OAAO,qBAAqB;oBAC5B,UAAU,IAAI,CAAC,WAAW,CAAC;oBAC3B,SAAS,KAAK,KAAK,CAAC;gBACtB;YACF;YAEA,qCAAqC;YACrC,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,MAAM;gBACN,OAAO,OAAO,KAAK,IAAI;gBACvB,aAAa,OAAO,WAAW,IAAI;gBACnC,cAAc,OAAO,YAAY,IAAI;gBACrC,eAAe,OAAO,aAAa,IAAI;gBACvC,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,UAAU,IAAI,KAAK,IAAI;gBAC5D,QAAQ,OAAO,MAAM,IAAI;gBACzB,kBAAkB,OAAO,gBAAgB,IAAI;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,QAAQ,KAAK,CAAC,wBAAwB,QAAQ,SAAS,CAAC,GAAG,OAAO;YAClE,OAAO;QACT;IACF;IAEQ,sBAA0C;QAChD,OAAO;YACL,QAAQ,EAAE;YACV,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB;gBACjB,aAAa;YACf;QACF;IACF;IAEQ,oBAAoB,IAAY,EAAE,QAAgB,EAAsB;QAC9E,MAAM,QAAQ,KAAK,KAAK,CAAC,MAAM,MAAM;QACrC,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK;QAEpD,OAAO;YACL,QAAQ;gBACN;oBACE,MAAM;oBACN,SAAS;oBACT,MAAM;oBACN,UAAU;gBACZ;aACD;YACD,aAAa,EAAE;YACf,SAAS;gBACP,aAAa;gBACb,YAAY;gBACZ,iBAAiB,KAAK,GAAG,CAAC,KAAK,YAAY;gBAC3C,aAAa;YACf;QACF;IACF;IAEQ,yBAAyB,QAAgB,EAAE,eAAuB,EAAE,OAAe,EAAkB;QAC3G,OAAO;YACL,IAAI,IAAI,CAAC,UAAU;YACnB,MAAM;YACN,OAAO,GAAG,gBAAgB,MAAM,CAAC,GAAG,WAAW,KAAK,gBAAgB,KAAK,CAAC,GAAG,YAAY,CAAC;YAC1F,aAAa;YACb,MAAM;YACN,cAAc;YACd,eAAe,QAAQ,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,EAAE;YAC7E,YAAY;YACZ,QAAQ;YACR,kBAAkB;QACpB;IACF;IAEQ,iBAAiB,QAAgB,EAAU;QACjD,MAAM,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;QACvC,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEQ,aAAqB;QAC3B,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;IACvE;AACF;AAEO,MAAM,WAAW,IAAI", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/analyzer.ts"], "sourcesContent": ["import * as acorn from 'acorn';\nimport { parse as babelParse } from '@babel/parser';\nimport { fileSystem, FileInfo } from './filesystem';\nimport { aiClient, CodeAnalysisResult, CodeSuggestion } from './ai-client';\n\nexport interface ProjectAnalysis {\n  files: FileAnalysis[];\n  overallMetrics: ProjectMetrics;\n  suggestions: CodeSuggestion[];\n  lastAnalyzed: Date;\n}\n\nexport interface FileAnalysis {\n  file: FileInfo;\n  analysis: CodeAnalysisResult;\n  ast?: any;\n  dependencies: string[];\n  exports: string[];\n}\n\nexport interface ProjectMetrics {\n  totalFiles: number;\n  totalLinesOfCode: number;\n  averageComplexity: number;\n  averageMaintainability: number;\n  issueCount: number;\n  suggestionCount: number;\n  testCoverage?: number;\n}\n\nexport class CodeAnalyzer {\n  private analysisCache: Map<string, FileAnalysis> = new Map();\n\n  async analyzeProject(): Promise<ProjectAnalysis> {\n    console.log('Starting project analysis...');\n    \n    const files = await fileSystem.listDirectory('src', true);\n    const codeFiles = files.filter(file => \n      file.type === 'file' && this.isAnalyzableFile(file.name)\n    );\n\n    const fileAnalyses: FileAnalysis[] = [];\n    const allSuggestions: CodeSuggestion[] = [];\n\n    for (const file of codeFiles) {\n      try {\n        const analysis = await this.analyzeFile(file);\n        fileAnalyses.push(analysis);\n        allSuggestions.push(...analysis.analysis.suggestions);\n      } catch (error) {\n        console.error(`Error analyzing file ${file.path}:`, error);\n      }\n    }\n\n    const overallMetrics = this.calculateProjectMetrics(fileAnalyses);\n\n    return {\n      files: fileAnalyses,\n      overallMetrics,\n      suggestions: allSuggestions,\n      lastAnalyzed: new Date()\n    };\n  }\n\n  async analyzeFile(file: FileInfo): Promise<FileAnalysis> {\n    // Check cache first\n    const cacheKey = `${file.path}_${file.lastModified?.getTime()}`;\n    if (this.analysisCache.has(cacheKey)) {\n      return this.analysisCache.get(cacheKey)!;\n    }\n\n    console.log(`Analyzing file: ${file.path}`);\n\n    const content = file.content || await fileSystem.readFile(file.path);\n    \n    // Parse AST for JavaScript/TypeScript files\n    let ast: any = null;\n    let dependencies: string[] = [];\n    let exports: string[] = [];\n\n    if (this.isJavaScriptFile(file.name)) {\n      try {\n        ast = this.parseAST(content, file.name);\n        dependencies = this.extractDependencies(ast);\n        exports = this.extractExports(ast);\n      } catch (error) {\n        console.warn(`Could not parse AST for ${file.path}:`, error);\n        // Continue without AST - the AI analysis will still work\n        ast = null;\n        dependencies = [];\n        exports = [];\n      }\n    }\n\n    // Get AI analysis\n    const analysis = await aiClient.analyzeCode(content, file.path);\n\n    const fileAnalysis: FileAnalysis = {\n      file: { ...file, content },\n      analysis,\n      ast,\n      dependencies,\n      exports\n    };\n\n    // Cache the result\n    this.analysisCache.set(cacheKey, fileAnalysis);\n\n    return fileAnalysis;\n  }\n\n  async generateImprovements(\n    file: FileInfo, \n    improvementTypes: string[] = ['performance', 'refactor', 'style']\n  ): Promise<CodeSuggestion[]> {\n    const content = file.content || await fileSystem.readFile(file.path);\n    const suggestions: CodeSuggestion[] = [];\n\n    for (const type of improvementTypes) {\n      try {\n        const suggestion = await aiClient.generateImprovement(\n          content, \n          file.path, \n          type,\n          `Improve ${type} aspects of this code`\n        );\n        \n        if (suggestion) {\n          suggestions.push(suggestion);\n        }\n      } catch (error) {\n        console.error(`Error generating ${type} improvement for ${file.path}:`, error);\n      }\n    }\n\n    return suggestions;\n  }\n\n  async generateNewFeature(description: string, targetFile?: string): Promise<CodeSuggestion | null> {\n    let contextFile = targetFile;\n    let contextCode = '';\n\n    if (!contextFile) {\n      // Find the most relevant file based on the description\n      const files = await fileSystem.listDirectory('src', true);\n      const mainFiles = files.filter(f => \n        f.name.includes('page.tsx') || \n        f.name.includes('index.tsx') ||\n        f.name.includes('App.tsx')\n      );\n      \n      contextFile = mainFiles[0]?.path || 'src/app/page.tsx';\n    }\n\n    try {\n      contextCode = await fileSystem.readFile(contextFile);\n    } catch (error) {\n      console.warn(`Could not read context file ${contextFile}:`, error);\n    }\n\n    return await aiClient.generateNewFeature(description, contextCode, contextFile);\n  }\n\n  private parseAST(code: string, fileName: string): any {\n    const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');\n    const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');\n\n    if (isTypeScript) {\n      // Use Babel parser for TypeScript files\n      try {\n        return babelParse(code, {\n          sourceType: 'module',\n          allowImportExportEverywhere: true,\n          allowReturnOutsideFunction: true,\n          plugins: [\n            'typescript',\n            ...(isJSX ? ['jsx'] : []),\n            'decorators-legacy',\n            'classProperties',\n            'objectRestSpread',\n            'asyncGenerators',\n            'functionBind',\n            'exportDefaultFrom',\n            'exportNamespaceFrom',\n            'dynamicImport',\n            'nullishCoalescingOperator',\n            'optionalChaining'\n          ]\n        });\n      } catch (error) {\n        throw new Error(`Could not parse TypeScript file ${fileName}: ${error}`);\n      }\n    } else {\n      // Use Acorn for JavaScript files\n      try {\n        return acorn.parse(code, {\n          ecmaVersion: 2022,\n          sourceType: 'module',\n          allowImportExportEverywhere: true,\n          allowReturnOutsideFunction: true,\n        });\n      } catch (error) {\n        // If parsing fails, try with different options\n        try {\n          return acorn.parse(code, {\n            ecmaVersion: 2022,\n            sourceType: 'script',\n          });\n        } catch (secondError) {\n          throw new Error(`Could not parse JavaScript file ${fileName}: ${error}`);\n        }\n      }\n    }\n  }\n\n  private extractDependencies(ast: any): string[] {\n    const dependencies: string[] = [];\n    \n    if (!ast || !ast.body) return dependencies;\n\n    for (const node of ast.body) {\n      if (node.type === 'ImportDeclaration' && node.source?.value) {\n        dependencies.push(node.source.value);\n      }\n    }\n\n    return [...new Set(dependencies)]; // Remove duplicates\n  }\n\n  private extractExports(ast: any): string[] {\n    const exports: string[] = [];\n    \n    if (!ast || !ast.body) return exports;\n\n    for (const node of ast.body) {\n      if (node.type === 'ExportNamedDeclaration') {\n        if (node.declaration) {\n          if (node.declaration.type === 'FunctionDeclaration' && node.declaration.id) {\n            exports.push(node.declaration.id.name);\n          } else if (node.declaration.type === 'VariableDeclaration') {\n            for (const declarator of node.declaration.declarations) {\n              if (declarator.id?.name) {\n                exports.push(declarator.id.name);\n              }\n            }\n          }\n        }\n        \n        if (node.specifiers) {\n          for (const specifier of node.specifiers) {\n            if (specifier.exported?.name) {\n              exports.push(specifier.exported.name);\n            }\n          }\n        }\n      } else if (node.type === 'ExportDefaultDeclaration') {\n        exports.push('default');\n      }\n    }\n\n    return [...new Set(exports)]; // Remove duplicates\n  }\n\n  private calculateProjectMetrics(fileAnalyses: FileAnalysis[]): ProjectMetrics {\n    const totalFiles = fileAnalyses.length;\n    let totalLinesOfCode = 0;\n    let totalComplexity = 0;\n    let totalMaintainability = 0;\n    let issueCount = 0;\n    let suggestionCount = 0;\n\n    for (const analysis of fileAnalyses) {\n      const metrics = analysis.analysis.metrics;\n      totalLinesOfCode += metrics.linesOfCode;\n      totalComplexity += metrics.complexity;\n      totalMaintainability += metrics.maintainability;\n      issueCount += analysis.analysis.issues.length;\n      suggestionCount += analysis.analysis.suggestions.length;\n    }\n\n    return {\n      totalFiles,\n      totalLinesOfCode,\n      averageComplexity: totalFiles > 0 ? totalComplexity / totalFiles : 0,\n      averageMaintainability: totalFiles > 0 ? totalMaintainability / totalFiles : 0,\n      issueCount,\n      suggestionCount\n    };\n  }\n\n  private isAnalyzableFile(fileName: string): boolean {\n    const analyzableExtensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.json'];\n    return analyzableExtensions.some(ext => fileName.endsWith(ext));\n  }\n\n  private isJavaScriptFile(fileName: string): boolean {\n    return fileName.endsWith('.js') || \n           fileName.endsWith('.jsx') || \n           fileName.endsWith('.ts') || \n           fileName.endsWith('.tsx');\n  }\n\n  clearCache(): void {\n    this.analysisCache.clear();\n  }\n\n  getCacheSize(): number {\n    return this.analysisCache.size;\n  }\n}\n\nexport const codeAnalyzer = new CodeAnalyzer();\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AA2BO,MAAM;IACH,gBAA2C,IAAI,MAAM;IAE7D,MAAM,iBAA2C;QAC/C,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,OAAO;QACpD,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAC7B,KAAK,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAGzD,MAAM,eAA+B,EAAE;QACvC,MAAM,iBAAmC,EAAE;QAE3C,KAAK,MAAM,QAAQ,UAAW;YAC5B,IAAI;gBACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;gBACxC,aAAa,IAAI,CAAC;gBAClB,eAAe,IAAI,IAAI,SAAS,QAAQ,CAAC,WAAW;YACtD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YACtD;QACF;QAEA,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;QAEpD,OAAO;YACL,OAAO;YACP;YACA,aAAa;YACb,cAAc,IAAI;QACpB;IACF;IAEA,MAAM,YAAY,IAAc,EAAyB;QACvD,oBAAoB;QACpB,MAAM,WAAW,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,YAAY,EAAE,WAAW;QAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACpC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAChC;QAEA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QAE1C,MAAM,UAAU,KAAK,OAAO,IAAI,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK,IAAI;QAEnE,4CAA4C;QAC5C,IAAI,MAAW;QACf,IAAI,eAAyB,EAAE;QAC/B,IAAI,UAAoB,EAAE;QAE1B,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,IAAI,GAAG;YACpC,IAAI;gBACF,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,IAAI;gBACtC,eAAe,IAAI,CAAC,mBAAmB,CAAC;gBACxC,UAAU,IAAI,CAAC,cAAc,CAAC;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;gBACtD,yDAAyD;gBACzD,MAAM;gBACN,eAAe,EAAE;gBACjB,UAAU,EAAE;YACd;QACF;QAEA,kBAAkB;QAClB,MAAM,WAAW,MAAM,4HAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,SAAS,KAAK,IAAI;QAE9D,MAAM,eAA6B;YACjC,MAAM;gBAAE,GAAG,IAAI;gBAAE;YAAQ;YACzB;YACA;YACA;YACA;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU;QAEjC,OAAO;IACT;IAEA,MAAM,qBACJ,IAAc,EACd,mBAA6B;QAAC;QAAe;QAAY;KAAQ,EACtC;QAC3B,MAAM,UAAU,KAAK,OAAO,IAAI,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,KAAK,IAAI;QACnE,MAAM,cAAgC,EAAE;QAExC,KAAK,MAAM,QAAQ,iBAAkB;YACnC,IAAI;gBACF,MAAM,aAAa,MAAM,4HAAA,CAAA,WAAQ,CAAC,mBAAmB,CACnD,SACA,KAAK,IAAI,EACT,MACA,CAAC,QAAQ,EAAE,KAAK,qBAAqB,CAAC;gBAGxC,IAAI,YAAY;oBACd,YAAY,IAAI,CAAC;gBACnB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,KAAK,iBAAiB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YAC1E;QACF;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,WAAmB,EAAE,UAAmB,EAAkC;QACjG,IAAI,cAAc;QAClB,IAAI,cAAc;QAElB,IAAI,CAAC,aAAa;YAChB,uDAAuD;YACvD,MAAM,QAAQ,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,OAAO;YACpD,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,IAC7B,EAAE,IAAI,CAAC,QAAQ,CAAC,eAChB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAChB,EAAE,IAAI,CAAC,QAAQ,CAAC;YAGlB,cAAc,SAAS,CAAC,EAAE,EAAE,QAAQ;QACtC;QAEA,IAAI;YACF,cAAc,MAAM,0HAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;QAC9D;QAEA,OAAO,MAAM,4HAAA,CAAA,WAAQ,CAAC,kBAAkB,CAAC,aAAa,aAAa;IACrE;IAEQ,SAAS,IAAY,EAAE,QAAgB,EAAO;QACpD,MAAM,eAAe,SAAS,QAAQ,CAAC,UAAU,SAAS,QAAQ,CAAC;QACnE,MAAM,QAAQ,SAAS,QAAQ,CAAC,WAAW,SAAS,QAAQ,CAAC;QAE7D,IAAI,cAAc;YAChB,wCAAwC;YACxC,IAAI;gBACF,OAAO,CAAA,GAAA,mJAAA,CAAA,QAAU,AAAD,EAAE,MAAM;oBACtB,YAAY;oBACZ,6BAA6B;oBAC7B,4BAA4B;oBAC5B,SAAS;wBACP;2BACI,QAAQ;4BAAC;yBAAM,GAAG,EAAE;wBACxB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,EAAE,EAAE,OAAO;YACzE;QACF,OAAO;YACL,iCAAiC;YACjC,IAAI;gBACF,OAAO,CAAA,GAAA,yIAAA,CAAA,QAAW,AAAD,EAAE,MAAM;oBACvB,aAAa;oBACb,YAAY;oBACZ,6BAA6B;oBAC7B,4BAA4B;gBAC9B;YACF,EAAE,OAAO,OAAO;gBACd,+CAA+C;gBAC/C,IAAI;oBACF,OAAO,CAAA,GAAA,yIAAA,CAAA,QAAW,AAAD,EAAE,MAAM;wBACvB,aAAa;wBACb,YAAY;oBACd;gBACF,EAAE,OAAO,aAAa;oBACpB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,EAAE,EAAE,OAAO;gBACzE;YACF;QACF;IACF;IAEQ,oBAAoB,GAAQ,EAAY;QAC9C,MAAM,eAAyB,EAAE;QAEjC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,OAAO;QAE9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAE;YAC3B,IAAI,KAAK,IAAI,KAAK,uBAAuB,KAAK,MAAM,EAAE,OAAO;gBAC3D,aAAa,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK;YACrC;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAc,EAAE,oBAAoB;IACzD;IAEQ,eAAe,GAAQ,EAAY;QACzC,MAAM,UAAoB,EAAE;QAE5B,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,OAAO;QAE9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAE;YAC3B,IAAI,KAAK,IAAI,KAAK,0BAA0B;gBAC1C,IAAI,KAAK,WAAW,EAAE;oBACpB,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,yBAAyB,KAAK,WAAW,CAAC,EAAE,EAAE;wBAC1E,QAAQ,IAAI,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,IAAI;oBACvC,OAAO,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,uBAAuB;wBAC1D,KAAK,MAAM,cAAc,KAAK,WAAW,CAAC,YAAY,CAAE;4BACtD,IAAI,WAAW,EAAE,EAAE,MAAM;gCACvB,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI;4BACjC;wBACF;oBACF;gBACF;gBAEA,IAAI,KAAK,UAAU,EAAE;oBACnB,KAAK,MAAM,aAAa,KAAK,UAAU,CAAE;wBACvC,IAAI,UAAU,QAAQ,EAAE,MAAM;4BAC5B,QAAQ,IAAI,CAAC,UAAU,QAAQ,CAAC,IAAI;wBACtC;oBACF;gBACF;YACF,OAAO,IAAI,KAAK,IAAI,KAAK,4BAA4B;gBACnD,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,OAAO;eAAI,IAAI,IAAI;SAAS,EAAE,oBAAoB;IACpD;IAEQ,wBAAwB,YAA4B,EAAkB;QAC5E,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,mBAAmB;QACvB,IAAI,kBAAkB;QACtB,IAAI,uBAAuB;QAC3B,IAAI,aAAa;QACjB,IAAI,kBAAkB;QAEtB,KAAK,MAAM,YAAY,aAAc;YACnC,MAAM,UAAU,SAAS,QAAQ,CAAC,OAAO;YACzC,oBAAoB,QAAQ,WAAW;YACvC,mBAAmB,QAAQ,UAAU;YACrC,wBAAwB,QAAQ,eAAe;YAC/C,cAAc,SAAS,QAAQ,CAAC,MAAM,CAAC,MAAM;YAC7C,mBAAmB,SAAS,QAAQ,CAAC,WAAW,CAAC,MAAM;QACzD;QAEA,OAAO;YACL;YACA;YACA,mBAAmB,aAAa,IAAI,kBAAkB,aAAa;YACnE,wBAAwB,aAAa,IAAI,uBAAuB,aAAa;YAC7E;YACA;QACF;IACF;IAEQ,iBAAiB,QAAgB,EAAW;QAClD,MAAM,uBAAuB;YAAC;YAAO;YAAQ;YAAO;YAAQ;YAAQ;SAAQ;QAC5E,OAAO,qBAAqB,IAAI,CAAC,CAAA,MAAO,SAAS,QAAQ,CAAC;IAC5D;IAEQ,iBAAiB,QAAgB,EAAW;QAClD,OAAO,SAAS,QAAQ,CAAC,UAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,UAClB,SAAS,QAAQ,CAAC;IAC3B;IAEA,aAAmB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA,eAAuB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;AACF;AAEO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/safety.ts"], "sourcesContent": ["import { fileSystem, BackupInfo } from './filesystem';\nimport { CodeSuggestion } from './ai-client';\n\nexport interface SafetyCheck {\n  id: string;\n  type: 'syntax' | 'dependency' | 'breaking-change' | 'security';\n  passed: boolean;\n  message: string;\n  severity: 'low' | 'medium' | 'high' | 'critical';\n}\n\nexport interface ChangeValidation {\n  isValid: boolean;\n  checks: SafetyCheck[];\n  backupId?: string;\n  canProceed: boolean;\n  warnings: string[];\n  errors: string[];\n}\n\nexport interface AppliedChange {\n  id: string;\n  suggestion: CodeSuggestion;\n  appliedAt: Date;\n  backupId: string;\n  validationResult: ChangeValidation;\n  success: boolean;\n  error?: string;\n}\n\nexport class SafetyManager {\n  private appliedChanges: AppliedChange[] = [];\n  private maxBackups: number = 50;\n\n  async validateChange(suggestion: CodeSuggestion): Promise<ChangeValidation> {\n    console.log(`Validating change: ${suggestion.title}`);\n    \n    const checks: SafetyCheck[] = [];\n    const warnings: string[] = [];\n    const errors: string[] = [];\n\n    // Syntax validation\n    const syntaxCheck = await this.validateSyntax(suggestion);\n    checks.push(syntaxCheck);\n    if (!syntaxCheck.passed) {\n      errors.push(syntaxCheck.message);\n    }\n\n    // Dependency validation\n    const dependencyCheck = await this.validateDependencies(suggestion);\n    checks.push(dependencyCheck);\n    if (!dependencyCheck.passed) {\n      if (dependencyCheck.severity === 'critical' || dependencyCheck.severity === 'high') {\n        errors.push(dependencyCheck.message);\n      } else {\n        warnings.push(dependencyCheck.message);\n      }\n    }\n\n    // Breaking change detection\n    const breakingChangeCheck = await this.detectBreakingChanges(suggestion);\n    checks.push(breakingChangeCheck);\n    if (!breakingChangeCheck.passed) {\n      warnings.push(breakingChangeCheck.message);\n    }\n\n    // Security validation\n    const securityCheck = await this.validateSecurity(suggestion);\n    checks.push(securityCheck);\n    if (!securityCheck.passed) {\n      if (securityCheck.severity === 'critical' || securityCheck.severity === 'high') {\n        errors.push(securityCheck.message);\n      } else {\n        warnings.push(securityCheck.message);\n      }\n    }\n\n    const isValid = checks.every(check => check.passed || check.severity === 'low');\n    const canProceed = errors.length === 0;\n\n    return {\n      isValid,\n      checks,\n      canProceed,\n      warnings,\n      errors\n    };\n  }\n\n  async applyChange(suggestion: CodeSuggestion, force: boolean = false): Promise<AppliedChange> {\n    console.log(`Applying change: ${suggestion.title}`);\n    \n    // Validate the change first\n    const validation = await this.validateChange(suggestion);\n    \n    if (!validation.canProceed && !force) {\n      const appliedChange: AppliedChange = {\n        id: this.generateId(),\n        suggestion,\n        appliedAt: new Date(),\n        backupId: '',\n        validationResult: validation,\n        success: false,\n        error: 'Validation failed: ' + validation.errors.join(', ')\n      };\n      \n      this.appliedChanges.push(appliedChange);\n      return appliedChange;\n    }\n\n    // Create backup before applying changes\n    const backupId = await fileSystem.createBackup(\n      [suggestion.file],\n      `Before applying: ${suggestion.title}`\n    );\n\n    validation.backupId = backupId;\n\n    try {\n      // Read current file content\n      if (!await fileSystem.fileExists(suggestion.file)) { throw new Error('File does not exist'); }\n      \n      // Apply the change\n      let newContent: string;\n      if (suggestion.originalCode && suggestion.originalCode.trim()) {\n        // Replace specific code section\n        newContent = currentContent.replace(suggestion.originalCode, suggestion.suggestedCode);\n        \n        // Verify the replacement actually happened\n        if (newContent === currentContent) {\n          throw new Error('Original code not found in file - no changes made');\n        }\n      } else {\n        // If no original code specified, append or replace entire file\n        newContent = suggestion.suggestedCode;\n      }\n\n      // Write the new content\n      await fileSystem.writeFile(suggestion.file, newContent);\n\n      const appliedChange: AppliedChange = {\n        id: this.generateId(),\n        suggestion,\n        appliedAt: new Date(),\n        backupId,\n        validationResult: validation,\n        success: true\n      };\n\n      this.appliedChanges.push(appliedChange);\n      await this.cleanupOldBackups();\n      \n      console.log(`Successfully applied change: ${suggestion.title}`);\n      return appliedChange;\n\n    } catch (error) {\n      // If application failed, restore from backup\n      try {\n        await fileSystem.restoreBackup(backupId);\n      } catch (restoreError) {\n        console.error('Failed to restore backup after failed change:', restoreError);\n      }\n\n      const appliedChange: AppliedChange = {\n        id: this.generateId(),\n        suggestion,\n        appliedAt: new Date(),\n        backupId,\n        validationResult: validation,\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      this.appliedChanges.push(appliedChange);\n      return appliedChange;\n    }\n  }\n\n  async rollbackChange(changeId: string): Promise<boolean> {\n    const change = this.appliedChanges.find(c => c.id === changeId);\n    if (!change) {\n      throw new Error(`Change ${changeId} not found`);\n    }\n\n    if (!change.success) {\n      throw new Error('Cannot rollback a change that was not successfully applied');\n    }\n\n    try {\n      await fileSystem.restoreBackup(change.backupId);\n      console.log(`Successfully rolled back change: ${change.suggestion.title}`);\n      return true;\n    } catch (error) {\n      console.error(`Failed to rollback change ${changeId}:`, error);\n      return false;\n    }\n  }\n\n  private async validateSyntax(suggestion: CodeSuggestion): Promise<SafetyCheck> {\n    try {\n      // Basic syntax validation for JavaScript/TypeScript\n      if (this.isJavaScriptFile(suggestion.file)) {\n        // Try to parse the suggested code\n        const code = suggestion.suggestedCode;\n        \n        // Basic checks\n        if (!code || code.trim().length === 0) {\n          return {\n            id: this.generateId(),\n            type: 'syntax',\n            passed: false,\n            message: 'Suggested code is empty',\n            severity: 'high'\n          };\n        }\n\n        // Check for balanced brackets\n        if (!this.hasBalancedBrackets(code)) {\n          return {\n            id: this.generateId(),\n            type: 'syntax',\n            passed: false,\n            message: 'Unbalanced brackets in suggested code',\n            severity: 'high'\n          };\n        }\n      }\n\n      return {\n        id: this.generateId(),\n        type: 'syntax',\n        passed: true,\n        message: 'Syntax validation passed',\n        severity: 'low'\n      };\n    } catch (error) {\n      return {\n        id: this.generateId(),\n        type: 'syntax',\n        passed: false,\n        message: `Syntax validation failed: ${error}`,\n        severity: 'high'\n      };\n    }\n  }\n\n  private async validateDependencies(suggestion: CodeSuggestion): Promise<SafetyCheck> {\n    try {\n      const code = suggestion.suggestedCode;\n      \n      // Check for new imports that might not exist\n      const importRegex = /import\\s+.*?\\s+from\\s+['\"]([^'\"]+)['\"]/g;\n      const matches = Array.from(code.matchAll(importRegex));\n      \n      for (const match of matches) {\n        const importPath = match[1];\n        \n        // Check if it's a relative import\n        if (importPath.startsWith('./') || importPath.startsWith('../')) {\n          // TODO: Validate that the file exists\n          continue;\n        }\n        \n        // Check if it's a known package (basic check)\n        if (!this.isKnownPackage(importPath)) {\n          return {\n            id: this.generateId(),\n            type: 'dependency',\n            passed: false,\n            message: `Unknown dependency: ${importPath}`,\n            severity: 'medium'\n          };\n        }\n      }\n\n      return {\n        id: this.generateId(),\n        type: 'dependency',\n        passed: true,\n        message: 'Dependency validation passed',\n        severity: 'low'\n      };\n    } catch (error) {\n      return {\n        id: this.generateId(),\n        type: 'dependency',\n        passed: false,\n        message: `Dependency validation failed: ${error}`,\n        severity: 'medium'\n      };\n    }\n  }\n\n  private async detectBreakingChanges(suggestion: CodeSuggestion): Promise<SafetyCheck> {\n    try {\n      const originalCode = suggestion.originalCode;\n      const suggestedCode = suggestion.suggestedCode;\n\n      // Check for removed exports\n      const originalExports = this.extractExports(originalCode);\n      const suggestedExports = this.extractExports(suggestedCode);\n      \n      const removedExports = originalExports.filter(exp => !suggestedExports.includes(exp));\n      \n      if (removedExports.length > 0) {\n        return {\n          id: this.generateId(),\n          type: 'breaking-change',\n          passed: false,\n          message: `Potentially breaking change: removed exports ${removedExports.join(', ')}`,\n          severity: 'medium'\n        };\n      }\n\n      return {\n        id: this.generateId(),\n        type: 'breaking-change',\n        passed: true,\n        message: 'No breaking changes detected',\n        severity: 'low'\n      };\n    } catch (error) {\n      return {\n        id: this.generateId(),\n        type: 'breaking-change',\n        passed: true,\n        message: 'Breaking change detection skipped due to error',\n        severity: 'low'\n      };\n    }\n  }\n\n  private async validateSecurity(suggestion: CodeSuggestion): Promise<SafetyCheck> {\n    try {\n      const code = suggestion.suggestedCode;\n      \n      // Basic security checks\n      const securityIssues: string[] = [];\n      \n      // Check for eval usage\n      if (code.includes('eval(')) {\n        securityIssues.push('Usage of eval() detected');\n      }\n      \n      // Check for innerHTML usage\n      if (code.includes('innerHTML')) {\n        securityIssues.push('Usage of innerHTML detected (potential XSS risk)');\n      }\n      \n      // Check for document.write\n      if (code.includes('document.write')) {\n        securityIssues.push('Usage of document.write detected');\n      }\n\n      if (securityIssues.length > 0) {\n        return {\n          id: this.generateId(),\n          type: 'security',\n          passed: false,\n          message: `Security concerns: ${securityIssues.join(', ')}`,\n          severity: 'medium'\n        };\n      }\n\n      return {\n        id: this.generateId(),\n        type: 'security',\n        passed: true,\n        message: 'Security validation passed',\n        severity: 'low'\n      };\n    } catch (error) {\n      return {\n        id: this.generateId(),\n        type: 'security',\n        passed: true,\n        message: 'Security validation skipped due to error',\n        severity: 'low'\n      };\n    }\n  }\n\n  private hasBalancedBrackets(code: string): boolean {\n    const brackets = { '(': ')', '[': ']', '{': '}' };\n    const stack: string[] = [];\n    \n    for (const char of code) {\n      if (char in brackets) {\n        stack.push(char);\n      } else if (Object.values(brackets).includes(char)) {\n        const last = stack.pop();\n        if (!last || brackets[last as keyof typeof brackets] !== char) {\n          return false;\n        }\n      }\n    }\n    \n    return stack.length === 0;\n  }\n\n  private extractExports(code: string): string[] {\n    const exports: string[] = [];\n    const exportRegex = /export\\s+(?:default\\s+)?(?:function|class|const|let|var)\\s+(\\w+)/g;\n    \n    let match;\n    while ((match = exportRegex.exec(code)) !== null) {\n      exports.push(match[1]);\n    }\n    \n    return exports;\n  }\n\n  private isKnownPackage(packageName: string): boolean {\n    const knownPackages = [\n      'react', 'react-dom', 'next', 'typescript', 'tailwindcss',\n      'lucide-react', 'openai', 'fs-extra', 'acorn'\n    ];\n    \n    return knownPackages.some(pkg => packageName.startsWith(pkg));\n  }\n\n  private isJavaScriptFile(fileName: string): boolean {\n    return fileName.endsWith('.js') || \n           fileName.endsWith('.jsx') || \n           fileName.endsWith('.ts') || \n           fileName.endsWith('.tsx');\n  }\n\n  private async cleanupOldBackups(): Promise<void> {\n    try {\n      const backups = await fileSystem.listBackups();\n      if (backups.length > this.maxBackups) {\n        const oldBackups = backups.slice(this.maxBackups);\n        // TODO: Implement backup cleanup\n        console.log(`Should cleanup ${oldBackups.length} old backups`);\n      }\n    } catch (error) {\n      console.warn('Failed to cleanup old backups:', error);\n    }\n  }\n\n  private generateId(): string {\n    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  getAppliedChanges(): AppliedChange[] {\n    return [...this.appliedChanges];\n  }\n\n  getChangeById(id: string): AppliedChange | undefined {\n    return this.appliedChanges.find(change => change.id === id);\n  }\n}\n\nexport const safetyManager = new SafetyManager();\n"], "names": [], "mappings": ";;;;AAAA;;AA8BO,MAAM;IACH,iBAAkC,EAAE,CAAC;IACrC,aAAqB,GAAG;IAEhC,MAAM,eAAe,UAA0B,EAA6B;QAC1E,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,WAAW,KAAK,EAAE;QAEpD,MAAM,SAAwB,EAAE;QAChC,MAAM,WAAqB,EAAE;QAC7B,MAAM,SAAmB,EAAE;QAE3B,oBAAoB;QACpB,MAAM,cAAc,MAAM,IAAI,CAAC,cAAc,CAAC;QAC9C,OAAO,IAAI,CAAC;QACZ,IAAI,CAAC,YAAY,MAAM,EAAE;YACvB,OAAO,IAAI,CAAC,YAAY,OAAO;QACjC;QAEA,wBAAwB;QACxB,MAAM,kBAAkB,MAAM,IAAI,CAAC,oBAAoB,CAAC;QACxD,OAAO,IAAI,CAAC;QACZ,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,IAAI,gBAAgB,QAAQ,KAAK,cAAc,gBAAgB,QAAQ,KAAK,QAAQ;gBAClF,OAAO,IAAI,CAAC,gBAAgB,OAAO;YACrC,OAAO;gBACL,SAAS,IAAI,CAAC,gBAAgB,OAAO;YACvC;QACF;QAEA,4BAA4B;QAC5B,MAAM,sBAAsB,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAC7D,OAAO,IAAI,CAAC;QACZ,IAAI,CAAC,oBAAoB,MAAM,EAAE;YAC/B,SAAS,IAAI,CAAC,oBAAoB,OAAO;QAC3C;QAEA,sBAAsB;QACtB,MAAM,gBAAgB,MAAM,IAAI,CAAC,gBAAgB,CAAC;QAClD,OAAO,IAAI,CAAC;QACZ,IAAI,CAAC,cAAc,MAAM,EAAE;YACzB,IAAI,cAAc,QAAQ,KAAK,cAAc,cAAc,QAAQ,KAAK,QAAQ;gBAC9E,OAAO,IAAI,CAAC,cAAc,OAAO;YACnC,OAAO;gBACL,SAAS,IAAI,CAAC,cAAc,OAAO;YACrC;QACF;QAEA,MAAM,UAAU,OAAO,KAAK,CAAC,CAAA,QAAS,MAAM,MAAM,IAAI,MAAM,QAAQ,KAAK;QACzE,MAAM,aAAa,OAAO,MAAM,KAAK;QAErC,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,YAAY,UAA0B,EAAE,QAAiB,KAAK,EAA0B;QAC5F,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,WAAW,KAAK,EAAE;QAElD,4BAA4B;QAC5B,MAAM,aAAa,MAAM,IAAI,CAAC,cAAc,CAAC;QAE7C,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,OAAO;YACpC,MAAM,gBAA+B;gBACnC,IAAI,IAAI,CAAC,UAAU;gBACnB;gBACA,WAAW,IAAI;gBACf,UAAU;gBACV,kBAAkB;gBAClB,SAAS;gBACT,OAAO,wBAAwB,WAAW,MAAM,CAAC,IAAI,CAAC;YACxD;YAEA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACzB,OAAO;QACT;QAEA,wCAAwC;QACxC,MAAM,WAAW,MAAM,0HAAA,CAAA,aAAU,CAAC,YAAY,CAC5C;YAAC,WAAW,IAAI;SAAC,EACjB,CAAC,iBAAiB,EAAE,WAAW,KAAK,EAAE;QAGxC,WAAW,QAAQ,GAAG;QAEtB,IAAI;YACF,4BAA4B;YAC5B,IAAI,CAAC,MAAM,0HAAA,CAAA,aAAU,CAAC,UAAU,CAAC,WAAW,IAAI,GAAG;gBAAE,MAAM,IAAI,MAAM;YAAwB;YAE7F,mBAAmB;YACnB,IAAI;YACJ,IAAI,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,IAAI,IAAI;gBAC7D,gCAAgC;gBAChC,aAAa,eAAe,OAAO,CAAC,WAAW,YAAY,EAAE,WAAW,aAAa;gBAErF,2CAA2C;gBAC3C,IAAI,eAAe,gBAAgB;oBACjC,MAAM,IAAI,MAAM;gBAClB;YACF,OAAO;gBACL,+DAA+D;gBAC/D,aAAa,WAAW,aAAa;YACvC;YAEA,wBAAwB;YACxB,MAAM,0HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,WAAW,IAAI,EAAE;YAE5C,MAAM,gBAA+B;gBACnC,IAAI,IAAI,CAAC,UAAU;gBACnB;gBACA,WAAW,IAAI;gBACf;gBACA,kBAAkB;gBAClB,SAAS;YACX;YAEA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACzB,MAAM,IAAI,CAAC,iBAAiB;YAE5B,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,WAAW,KAAK,EAAE;YAC9D,OAAO;QAET,EAAE,OAAO,OAAO;YACd,6CAA6C;YAC7C,IAAI;gBACF,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC;YACjC,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,iDAAiD;YACjE;YAEA,MAAM,gBAA+B;gBACnC,IAAI,IAAI,CAAC,UAAU;gBACnB;gBACA,WAAW,IAAI;gBACf;gBACA,kBAAkB;gBAClB,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;YAEA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACzB,OAAO;QACT;IACF;IAEA,MAAM,eAAe,QAAgB,EAAoB;QACvD,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,SAAS,UAAU,CAAC;QAChD;QAEA,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,0HAAA,CAAA,aAAU,CAAC,aAAa,CAAC,OAAO,QAAQ;YAC9C,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,OAAO,UAAU,CAAC,KAAK,EAAE;YACzE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC,EAAE;YACxD,OAAO;QACT;IACF;IAEA,MAAc,eAAe,UAA0B,EAAwB;QAC7E,IAAI;YACF,oDAAoD;YACpD,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW,IAAI,GAAG;gBAC1C,kCAAkC;gBAClC,MAAM,OAAO,WAAW,aAAa;gBAErC,eAAe;gBACf,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,KAAK,GAAG;oBACrC,OAAO;wBACL,IAAI,IAAI,CAAC,UAAU;wBACnB,MAAM;wBACN,QAAQ;wBACR,SAAS;wBACT,UAAU;oBACZ;gBACF;gBAEA,8BAA8B;gBAC9B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO;oBACnC,OAAO;wBACL,IAAI,IAAI,CAAC,UAAU;wBACnB,MAAM;wBACN,QAAQ;wBACR,SAAS;wBACT,UAAU;oBACZ;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS,CAAC,0BAA0B,EAAE,OAAO;gBAC7C,UAAU;YACZ;QACF;IACF;IAEA,MAAc,qBAAqB,UAA0B,EAAwB;QACnF,IAAI;YACF,MAAM,OAAO,WAAW,aAAa;YAErC,6CAA6C;YAC7C,MAAM,cAAc;YACpB,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,QAAQ,CAAC;YAEzC,KAAK,MAAM,SAAS,QAAS;gBAC3B,MAAM,aAAa,KAAK,CAAC,EAAE;gBAE3B,kCAAkC;gBAClC,IAAI,WAAW,UAAU,CAAC,SAAS,WAAW,UAAU,CAAC,QAAQ;oBAE/D;gBACF;gBAEA,8CAA8C;gBAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa;oBACpC,OAAO;wBACL,IAAI,IAAI,CAAC,UAAU;wBACnB,MAAM;wBACN,QAAQ;wBACR,SAAS,CAAC,oBAAoB,EAAE,YAAY;wBAC5C,UAAU;oBACZ;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS,CAAC,8BAA8B,EAAE,OAAO;gBACjD,UAAU;YACZ;QACF;IACF;IAEA,MAAc,sBAAsB,UAA0B,EAAwB;QACpF,IAAI;YACF,MAAM,eAAe,WAAW,YAAY;YAC5C,MAAM,gBAAgB,WAAW,aAAa;YAE9C,4BAA4B;YAC5B,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC;YAC5C,MAAM,mBAAmB,IAAI,CAAC,cAAc,CAAC;YAE7C,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAA,MAAO,CAAC,iBAAiB,QAAQ,CAAC;YAEhF,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,OAAO;oBACL,IAAI,IAAI,CAAC,UAAU;oBACnB,MAAM;oBACN,QAAQ;oBACR,SAAS,CAAC,6CAA6C,EAAE,eAAe,IAAI,CAAC,OAAO;oBACpF,UAAU;gBACZ;YACF;YAEA,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA,MAAc,iBAAiB,UAA0B,EAAwB;QAC/E,IAAI;YACF,MAAM,OAAO,WAAW,aAAa;YAErC,wBAAwB;YACxB,MAAM,iBAA2B,EAAE;YAEnC,uBAAuB;YACvB,IAAI,KAAK,QAAQ,CAAC,UAAU;gBAC1B,eAAe,IAAI,CAAC;YACtB;YAEA,4BAA4B;YAC5B,IAAI,KAAK,QAAQ,CAAC,cAAc;gBAC9B,eAAe,IAAI,CAAC;YACtB;YAEA,2BAA2B;YAC3B,IAAI,KAAK,QAAQ,CAAC,mBAAmB;gBACnC,eAAe,IAAI,CAAC;YACtB;YAEA,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,OAAO;oBACL,IAAI,IAAI,CAAC,UAAU;oBACnB,MAAM;oBACN,QAAQ;oBACR,SAAS,CAAC,mBAAmB,EAAE,eAAe,IAAI,CAAC,OAAO;oBAC1D,UAAU;gBACZ;YACF;YAEA,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEQ,oBAAoB,IAAY,EAAW;QACjD,MAAM,WAAW;YAAE,KAAK;YAAK,KAAK;YAAK,KAAK;QAAI;QAChD,MAAM,QAAkB,EAAE;QAE1B,KAAK,MAAM,QAAQ,KAAM;YACvB,IAAI,QAAQ,UAAU;gBACpB,MAAM,IAAI,CAAC;YACb,OAAO,IAAI,OAAO,MAAM,CAAC,UAAU,QAAQ,CAAC,OAAO;gBACjD,MAAM,OAAO,MAAM,GAAG;gBACtB,IAAI,CAAC,QAAQ,QAAQ,CAAC,KAA8B,KAAK,MAAM;oBAC7D,OAAO;gBACT;YACF;QACF;QAEA,OAAO,MAAM,MAAM,KAAK;IAC1B;IAEQ,eAAe,IAAY,EAAY;QAC7C,MAAM,UAAoB,EAAE;QAC5B,MAAM,cAAc;QAEpB,IAAI;QACJ,MAAO,CAAC,QAAQ,YAAY,IAAI,CAAC,KAAK,MAAM,KAAM;YAChD,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;QACvB;QAEA,OAAO;IACT;IAEQ,eAAe,WAAmB,EAAW;QACnD,MAAM,gBAAgB;YACpB;YAAS;YAAa;YAAQ;YAAc;YAC5C;YAAgB;YAAU;YAAY;SACvC;QAED,OAAO,cAAc,IAAI,CAAC,CAAA,MAAO,YAAY,UAAU,CAAC;IAC1D;IAEQ,iBAAiB,QAAgB,EAAW;QAClD,OAAO,SAAS,QAAQ,CAAC,UAClB,SAAS,QAAQ,CAAC,WAClB,SAAS,QAAQ,CAAC,UAClB,SAAS,QAAQ,CAAC;IAC3B;IAEA,MAAc,oBAAmC;QAC/C,IAAI;YACF,MAAM,UAAU,MAAM,0HAAA,CAAA,aAAU,CAAC,WAAW;YAC5C,IAAI,QAAQ,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;gBACpC,MAAM,aAAa,QAAQ,KAAK,CAAC,IAAI,CAAC,UAAU;gBAChD,iCAAiC;gBACjC,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW,MAAM,CAAC,YAAY,CAAC;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,kCAAkC;QACjD;IACF;IAEQ,aAAqB;QAC3B,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACnE;IAEA,oBAAqC;QACnC,OAAO;eAAI,IAAI,CAAC,cAAc;SAAC;IACjC;IAEA,cAAc,EAAU,EAA6B;QACnD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;IAC1D;AACF;AAEO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/lib/improver.ts"], "sourcesContent": ["import { codeA<PERSON>yzer, ProjectAnalysis } from './analyzer';\nimport { safety<PERSON>anager, AppliedChange } from './safety';\nimport { CodeSuggestion } from './ai-client';\nimport { fileSystem } from './filesystem';\n\nexport interface ImprovementSession {\n  id: string;\n  startedAt: Date;\n  completedAt?: Date;\n  analysis: ProjectAnalysis;\n  appliedChanges: AppliedChange[];\n  status: 'running' | 'completed' | 'failed' | 'cancelled';\n  settings: ImprovementSettings;\n}\n\nexport interface ImprovementSettings {\n  autoApprove: boolean;\n  maxChangesPerSession: number;\n  improvementTypes: string[];\n  minConfidence: number;\n  excludeFiles: string[];\n  requireManualApproval: boolean;\n}\n\nexport interface ImprovementStats {\n  totalSessions: number;\n  totalChangesApplied: number;\n  successRate: number;\n  averageImprovementsPerSession: number;\n  lastImprovement: Date | null;\n  topImprovementTypes: { type: string; count: number }[];\n}\n\nexport class SelfImprover {\n  private currentSession: ImprovementSession | null = null;\n  private sessions: ImprovementSession[] = [];\n  private defaultSettings: ImprovementSettings = {\n    autoApprove: false,\n    maxChangesPerSession: 10,\n    improvementTypes: ['performance', 'refactor', 'style', 'bug-fix'],\n    minConfidence: 0.7,\n    excludeFiles: ['node_modules', '.git', 'dist', 'build'],\n    requireManualApproval: true\n  };\n\n  async startImprovementSession(settings?: Partial<ImprovementSettings>): Promise<ImprovementSession> {\n    if (this.currentSession && this.currentSession.status === 'running') {\n      throw new Error('An improvement session is already running');\n    }\n\n    const sessionSettings = { ...this.defaultSettings, ...settings };\n    \n    console.log('Starting new improvement session...');\n    \n    // Analyze the current project\n    const analysis = await codeAnalyzer.analyzeProject();\n    \n    const session: ImprovementSession = {\n      id: this.generateSessionId(),\n      startedAt: new Date(),\n      analysis,\n      appliedChanges: [],\n      status: 'running',\n      settings: sessionSettings\n    };\n\n    this.currentSession = session;\n    this.sessions.push(session);\n\n    // Start the improvement process\n    this.runImprovementProcess(session);\n\n    return session;\n  }\n\n  private async runImprovementProcess(session: ImprovementSession): Promise<void> {\n    try {\n      console.log(`Running improvement process for session ${session.id}`);\n      \n      // Debug: Log the analysis results\n      console.log(`Analysis completed with ${session.analysis.suggestions.length} total suggestions`);\n      console.log('Session settings:', {\n        minConfidence: session.settings.minConfidence,\n        improvementTypes: session.settings.improvementTypes,\n        excludeFiles: session.settings.excludeFiles\n      });\n\n      // Get high-confidence suggestions from the analysis\n      const allSuggestions = session.analysis.suggestions;\n      console.log('All suggestions:', allSuggestions.map(s => ({\n        title: s.title,\n        confidence: s.confidence,\n        type: s.type,\n        file: s.file\n      })));\n\n      const confidenceFiltered = allSuggestions.filter(s => s.confidence >= session.settings.minConfidence);\n      console.log(`After confidence filter (>= ${session.settings.minConfidence}):`, confidenceFiltered.length);\n\n      const typeFiltered = confidenceFiltered.filter(s => session.settings.improvementTypes.includes(s.type));\n      console.log(`After type filter:`, typeFiltered.length);\n\n      const suggestions = typeFiltered\n        .filter(s => !this.isFileExcluded(s.file, session.settings.excludeFiles))\n        .sort((a, b) => b.confidence - a.confidence) // Sort by confidence descending\n        .slice(0, session.settings.maxChangesPerSession);\n\n      console.log(`Found ${suggestions.length} improvement suggestions after all filters`);\n\n      for (const suggestion of suggestions) {\n        if (session.status !== 'running') {\n          break; // Session was cancelled or failed\n        }\n\n        try {\n          await this.processSuggestion(suggestion, session);\n        } catch (error) {\n          console.error(`Error processing suggestion ${suggestion.id}:`, error);\n        }\n      }\n\n      // Mark session as completed\n      session.completedAt = new Date();\n      session.status = 'completed';\n      this.currentSession = null;\n\n      console.log(`Improvement session ${session.id} completed with ${session.appliedChanges.length} changes applied`);\n\n    } catch (error) {\n      console.error(`Improvement session ${session.id} failed:`, error);\n      session.status = 'failed';\n      session.completedAt = new Date();\n      this.currentSession = null;\n    }\n  }\n\n  private async processSuggestion(suggestion: CodeSuggestion, session: ImprovementSession): Promise<void> {\n    console.log(`Processing suggestion: ${suggestion.title}`);\n\n    // Apply the change through the safety manager\n    const appliedChange = await safetyManager.applyChange(\n      suggestion, \n      session.settings.autoApprove\n    );\n\n    session.appliedChanges.push(appliedChange);\n\n    if (appliedChange.success) {\n      console.log(`Successfully applied: ${suggestion.title}`);\n    } else {\n      console.warn(`Failed to apply: ${suggestion.title} - ${appliedChange.error}`);\n    }\n  }\n\n  async generateCustomImprovement(\n    description: string, \n    targetFile?: string\n  ): Promise<CodeSuggestion | null> {\n    console.log(`Generating custom improvement: ${description}`);\n    \n    return await codeAnalyzer.generateNewFeature(description, targetFile);\n  }\n\n  async applyCustomImprovement(\n    suggestion: CodeSuggestion, \n    force: boolean = false\n  ): Promise<AppliedChange> {\n    console.log(`Applying custom improvement: ${suggestion.title}`);\n    \n    return await safetyManager.applyChange(suggestion, force);\n  }\n\n  async rollbackChange(changeId: string): Promise<boolean> {\n    console.log(`Rolling back change: ${changeId}`);\n    \n    const success = await safetyManager.rollbackChange(changeId);\n    \n    // Update session records\n    for (const session of this.sessions) {\n      const change = session.appliedChanges.find(c => c.id === changeId);\n      if (change) {\n        // Mark as rolled back (we could add a rollback flag to AppliedChange)\n        break;\n      }\n    }\n    \n    return success;\n  }\n\n  cancelCurrentSession(): boolean {\n    if (this.currentSession && this.currentSession.status === 'running') {\n      this.currentSession.status = 'cancelled';\n      this.currentSession.completedAt = new Date();\n      this.currentSession = null;\n      console.log('Current improvement session cancelled');\n      return true;\n    }\n    return false;\n  }\n\n  getCurrentSession(): ImprovementSession | null {\n    return this.currentSession;\n  }\n\n  getAllSessions(): ImprovementSession[] {\n    return [...this.sessions];\n  }\n\n  getSessionById(sessionId: string): ImprovementSession | undefined {\n    return this.sessions.find(s => s.id === sessionId);\n  }\n\n  getStats(): ImprovementStats {\n    const totalSessions = this.sessions.length;\n    const completedSessions = this.sessions.filter(s => s.status === 'completed');\n    \n    const totalChangesApplied = this.sessions.reduce(\n      (total, session) => total + session.appliedChanges.filter(c => c.success).length, \n      0\n    );\n    \n    const totalChangesAttempted = this.sessions.reduce(\n      (total, session) => total + session.appliedChanges.length, \n      0\n    );\n    \n    const successRate = totalChangesAttempted > 0 ? totalChangesApplied / totalChangesAttempted : 0;\n    \n    const averageImprovementsPerSession = completedSessions.length > 0 \n      ? totalChangesApplied / completedSessions.length \n      : 0;\n    \n    const lastImprovement = this.sessions\n      .flatMap(s => s.appliedChanges)\n      .filter(c => c.success)\n      .sort((a, b) => b.appliedAt.getTime() - a.appliedAt.getTime())[0]?.appliedAt || null;\n    \n    // Calculate top improvement types\n    const typeCount: { [key: string]: number } = {};\n    this.sessions.forEach(session => {\n      session.appliedChanges\n        .filter(c => c.success)\n        .forEach(change => {\n          typeCount[change.suggestion.type] = (typeCount[change.suggestion.type] || 0) + 1;\n        });\n    });\n    \n    const topImprovementTypes = Object.entries(typeCount)\n      .map(([type, count]) => ({ type, count }))\n      .sort((a, b) => b.count - a.count)\n      .slice(0, 5);\n\n    return {\n      totalSessions,\n      totalChangesApplied,\n      successRate,\n      averageImprovementsPerSession,\n      lastImprovement,\n      topImprovementTypes\n    };\n  }\n\n  async exportSession(sessionId: string): Promise<string> {\n    const session = this.getSessionById(sessionId);\n    if (!session) {\n      throw new Error(`Session ${sessionId} not found`);\n    }\n\n    const exportData = {\n      session,\n      exportedAt: new Date(),\n      version: '1.0.0'\n    };\n\n    const exportPath = `exports/session_${sessionId}_${Date.now()}.json`;\n    await fileSystem.writeFile(exportPath, JSON.stringify(exportData, null, 2));\n    \n    return exportPath;\n  }\n\n  private isFileExcluded(filePath: string, excludePatterns: string[]): boolean {\n    return excludePatterns.some(pattern => filePath.includes(pattern));\n  }\n\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n  }\n\n  updateSettings(newSettings: Partial<ImprovementSettings>): void {\n    this.defaultSettings = { ...this.defaultSettings, ...newSettings };\n  }\n\n  getSettings(): ImprovementSettings {\n    return { ...this.defaultSettings };\n  }\n}\n\nexport const selfImprover = new SelfImprover();\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AA8BO,MAAM;IACH,iBAA4C,KAAK;IACjD,WAAiC,EAAE,CAAC;IACpC,kBAAuC;QAC7C,aAAa;QACb,sBAAsB;QACtB,kBAAkB;YAAC;YAAe;YAAY;YAAS;SAAU;QACjE,eAAe;QACf,cAAc;YAAC;YAAgB;YAAQ;YAAQ;SAAQ;QACvD,uBAAuB;IACzB,EAAE;IAEF,MAAM,wBAAwB,QAAuC,EAA+B;QAClG,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,WAAW;YACnE,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,kBAAkB;YAAE,GAAG,IAAI,CAAC,eAAe;YAAE,GAAG,QAAQ;QAAC;QAE/D,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,WAAW,MAAM,wHAAA,CAAA,eAAY,CAAC,cAAc;QAElD,MAAM,UAA8B;YAClC,IAAI,IAAI,CAAC,iBAAiB;YAC1B,WAAW,IAAI;YACf;YACA,gBAAgB,EAAE;YAClB,QAAQ;YACR,UAAU;QACZ;QAEA,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEnB,gCAAgC;QAChC,IAAI,CAAC,qBAAqB,CAAC;QAE3B,OAAO;IACT;IAEA,MAAc,sBAAsB,OAA2B,EAAiB;QAC9E,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,QAAQ,EAAE,EAAE;YAEnE,kCAAkC;YAClC,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,kBAAkB,CAAC;YAC9F,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,eAAe,QAAQ,QAAQ,CAAC,aAAa;gBAC7C,kBAAkB,QAAQ,QAAQ,CAAC,gBAAgB;gBACnD,cAAc,QAAQ,QAAQ,CAAC,YAAY;YAC7C;YAEA,oDAAoD;YACpD,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,WAAW;YACnD,QAAQ,GAAG,CAAC,oBAAoB,eAAe,GAAG,CAAC,CAAA,IAAK,CAAC;oBACvD,OAAO,EAAE,KAAK;oBACd,YAAY,EAAE,UAAU;oBACxB,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;gBACd,CAAC;YAED,MAAM,qBAAqB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,QAAQ,QAAQ,CAAC,aAAa;YACpG,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,QAAQ,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE,mBAAmB,MAAM;YAExG,MAAM,eAAe,mBAAmB,MAAM,CAAC,CAAA,IAAK,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,IAAI;YACrG,QAAQ,GAAG,CAAC,CAAC,kBAAkB,CAAC,EAAE,aAAa,MAAM;YAErD,MAAM,cAAc,aACjB,MAAM,CAAC,CAAA,IAAK,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,QAAQ,QAAQ,CAAC,YAAY,GACtE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAAE,gCAAgC;aAC5E,KAAK,CAAC,GAAG,QAAQ,QAAQ,CAAC,oBAAoB;YAEjD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,YAAY,MAAM,CAAC,0CAA0C,CAAC;YAEnF,KAAK,MAAM,cAAc,YAAa;gBACpC,IAAI,QAAQ,MAAM,KAAK,WAAW;oBAChC,OAAO,kCAAkC;gBAC3C;gBAEA,IAAI;oBACF,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY;gBAC3C,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE;gBACjE;YACF;YAEA,4BAA4B;YAC5B,QAAQ,WAAW,GAAG,IAAI;YAC1B,QAAQ,MAAM,GAAG;YACjB,IAAI,CAAC,cAAc,GAAG;YAEtB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,gBAAgB,EAAE,QAAQ,cAAc,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAEjH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE;YAC3D,QAAQ,MAAM,GAAG;YACjB,QAAQ,WAAW,GAAG,IAAI;YAC1B,IAAI,CAAC,cAAc,GAAG;QACxB;IACF;IAEA,MAAc,kBAAkB,UAA0B,EAAE,OAA2B,EAAiB;QACtG,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,KAAK,EAAE;QAExD,8CAA8C;QAC9C,MAAM,gBAAgB,MAAM,sHAAA,CAAA,gBAAa,CAAC,WAAW,CACnD,YACA,QAAQ,QAAQ,CAAC,WAAW;QAG9B,QAAQ,cAAc,CAAC,IAAI,CAAC;QAE5B,IAAI,cAAc,OAAO,EAAE;YACzB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,WAAW,KAAK,EAAE;QACzD,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,WAAW,KAAK,CAAC,GAAG,EAAE,cAAc,KAAK,EAAE;QAC9E;IACF;IAEA,MAAM,0BACJ,WAAmB,EACnB,UAAmB,EACa;QAChC,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,aAAa;QAE3D,OAAO,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,aAAa;IAC5D;IAEA,MAAM,uBACJ,UAA0B,EAC1B,QAAiB,KAAK,EACE;QACxB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,WAAW,KAAK,EAAE;QAE9D,OAAO,MAAM,sHAAA,CAAA,gBAAa,CAAC,WAAW,CAAC,YAAY;IACrD;IAEA,MAAM,eAAe,QAAgB,EAAoB;QACvD,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,UAAU;QAE9C,MAAM,UAAU,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC;QAEnD,yBAAyB;QACzB,KAAK,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAE;YACnC,MAAM,SAAS,QAAQ,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACzD,IAAI,QAAQ;gBAEV;YACF;QACF;QAEA,OAAO;IACT;IAEA,uBAAgC;QAC9B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,WAAW;YACnE,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG;YAC7B,IAAI,CAAC,cAAc,CAAC,WAAW,GAAG,IAAI;YACtC,IAAI,CAAC,cAAc,GAAG;YACtB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QACA,OAAO;IACT;IAEA,oBAA+C;QAC7C,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA,iBAAuC;QACrC,OAAO;eAAI,IAAI,CAAC,QAAQ;SAAC;IAC3B;IAEA,eAAe,SAAiB,EAAkC;QAChE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC1C;IAEA,WAA6B;QAC3B,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,MAAM;QAC1C,MAAM,oBAAoB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAEjE,MAAM,sBAAsB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAC9C,CAAC,OAAO,UAAY,QAAQ,QAAQ,cAAc,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM,EAChF;QAGF,MAAM,wBAAwB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAChD,CAAC,OAAO,UAAY,QAAQ,QAAQ,cAAc,CAAC,MAAM,EACzD;QAGF,MAAM,cAAc,wBAAwB,IAAI,sBAAsB,wBAAwB;QAE9F,MAAM,gCAAgC,kBAAkB,MAAM,GAAG,IAC7D,sBAAsB,kBAAkB,MAAM,GAC9C;QAEJ,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAClC,OAAO,CAAC,CAAA,IAAK,EAAE,cAAc,EAC7B,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,EAAE,EAAE,aAAa;QAElF,kCAAkC;QAClC,MAAM,YAAuC,CAAC;QAC9C,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACpB,QAAQ,cAAc,CACnB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,OAAO,CAAC,CAAA;gBACP,SAAS,CAAC,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACjF;QACJ;QAEA,MAAM,sBAAsB,OAAO,OAAO,CAAC,WACxC,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;gBAAE;gBAAM;YAAM,CAAC,GACvC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;QAEZ,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAM,cAAc,SAAiB,EAAmB;QACtD,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC;QACpC,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,UAAU,CAAC;QAClD;QAEA,MAAM,aAAa;YACjB;YACA,YAAY,IAAI;YAChB,SAAS;QACX;QAEA,MAAM,aAAa,CAAC,gBAAgB,EAAE,UAAU,CAAC,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QACpE,MAAM,0HAAA,CAAA,aAAU,CAAC,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY,MAAM;QAExE,OAAO;IACT;IAEQ,eAAe,QAAgB,EAAE,eAAyB,EAAW;QAC3E,OAAO,gBAAgB,IAAI,CAAC,CAAA,UAAW,SAAS,QAAQ,CAAC;IAC3D;IAEQ,oBAA4B;QAClC,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;IAC/E;IAEA,eAAe,WAAyC,EAAQ;QAC9D,IAAI,CAAC,eAAe,GAAG;YAAE,GAAG,IAAI,CAAC,eAAe;YAAE,GAAG,WAAW;QAAC;IACnE;IAEA,cAAmC;QACjC,OAAO;YAAE,GAAG,IAAI,CAAC,eAAe;QAAC;IACnC;AACF;AAEO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/src/app/api/improve/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { selfImprover } from '@/lib/improver';\nimport { safetyManager } from '@/lib/safety';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action');\n\n    switch (action) {\n      case 'current-session':\n        const currentSession = selfImprover.getCurrentSession();\n        return NextResponse.json({\n          success: true,\n          data: currentSession\n        });\n\n      case 'sessions':\n        const sessions = selfImprover.getAllSessions();\n        return NextResponse.json({\n          success: true,\n          data: sessions\n        });\n\n      case 'stats':\n        const stats = selfImprover.getStats();\n        return NextResponse.json({\n          success: true,\n          data: stats\n        });\n\n      case 'settings':\n        const settings = selfImprover.getSettings();\n        return NextResponse.json({\n          success: true,\n          data: settings\n        });\n\n      case 'applied-changes':\n        const changes = safetyManager.getAppliedChanges();\n        return NextResponse.json({\n          success: true,\n          data: changes\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Unknown action'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Improve GET error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, data } = body;\n\n    switch (action) {\n      case 'start-session':\n        const { settings } = data || {};\n        const session = await selfImprover.startImprovementSession(settings);\n        \n        return NextResponse.json({\n          success: true,\n          data: session\n        });\n\n      case 'cancel-session':\n        const cancelled = selfImprover.cancelCurrentSession();\n        \n        return NextResponse.json({\n          success: true,\n          data: { cancelled }\n        });\n\n      case 'apply-suggestion':\n        const { suggestion, force } = data;\n        const appliedChange = await selfImprover.applyCustomImprovement(suggestion, force);\n        \n        return NextResponse.json({\n          success: true,\n          data: appliedChange\n        });\n\n      case 'rollback-change':\n        const { changeId } = data;\n        const rollbackSuccess = await selfImprover.rollbackChange(changeId);\n        \n        return NextResponse.json({\n          success: true,\n          data: { success: rollbackSuccess }\n        });\n\n      case 'generate-custom':\n        const { description, targetFile } = data;\n        const customSuggestion = await selfImprover.generateCustomImprovement(description, targetFile);\n        \n        return NextResponse.json({\n          success: true,\n          data: customSuggestion\n        });\n\n      case 'update-settings':\n        const { newSettings } = data;\n        selfImprover.updateSettings(newSettings);\n        \n        return NextResponse.json({\n          success: true,\n          message: 'Settings updated'\n        });\n\n      case 'export-session':\n        const { sessionId } = data;\n        const exportPath = await selfImprover.exportSession(sessionId);\n        \n        return NextResponse.json({\n          success: true,\n          data: { exportPath }\n        });\n\n      case 'validate-change':\n        const { suggestionToValidate } = data;\n        const validation = await safetyManager.validateChange(suggestionToValidate);\n        \n        return NextResponse.json({\n          success: true,\n          data: validation\n        });\n\n      default:\n        return NextResponse.json({\n          success: false,\n          error: 'Unknown action'\n        }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Improve POST error:', error);\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,MAAM,iBAAiB,wHAAA,CAAA,eAAY,CAAC,iBAAiB;gBACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,WAAW,wHAAA,CAAA,eAAY,CAAC,cAAc;gBAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,QAAQ,wHAAA,CAAA,eAAY,CAAC,QAAQ;gBACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,WAAW,wHAAA,CAAA,eAAY,CAAC,WAAW;gBACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,UAAU,sHAAA,CAAA,gBAAa,CAAC,iBAAiB;gBAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC;gBAC9B,MAAM,UAAU,MAAM,wHAAA,CAAA,eAAY,CAAC,uBAAuB,CAAC;gBAE3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,YAAY,wHAAA,CAAA,eAAY,CAAC,oBAAoB;gBAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE;oBAAU;gBACpB;YAEF,KAAK;gBACH,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG;gBAC9B,MAAM,gBAAgB,MAAM,wHAAA,CAAA,eAAY,CAAC,sBAAsB,CAAC,YAAY;gBAE5E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,EAAE,QAAQ,EAAE,GAAG;gBACrB,MAAM,kBAAkB,MAAM,wHAAA,CAAA,eAAY,CAAC,cAAc,CAAC;gBAE1D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE,SAAS;oBAAgB;gBACnC;YAEF,KAAK;gBACH,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG;gBACpC,MAAM,mBAAmB,MAAM,wHAAA,CAAA,eAAY,CAAC,yBAAyB,CAAC,aAAa;gBAEnF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF,KAAK;gBACH,MAAM,EAAE,WAAW,EAAE,GAAG;gBACxB,wHAAA,CAAA,eAAY,CAAC,cAAc,CAAC;gBAE5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,SAAS;gBACX;YAEF,KAAK;gBACH,MAAM,EAAE,SAAS,EAAE,GAAG;gBACtB,MAAM,aAAa,MAAM,wHAAA,CAAA,eAAY,CAAC,aAAa,CAAC;gBAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;wBAAE;oBAAW;gBACrB;YAEF,KAAK;gBACH,MAAM,EAAE,oBAAoB,EAAE,GAAG;gBACjC,MAAM,aAAa,MAAM,sHAAA,CAAA,gBAAa,CAAC,cAAc,CAAC;gBAEtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;gBACR;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,OAAO;gBACT,GAAG;oBAAE,QAAQ;gBAAI;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}