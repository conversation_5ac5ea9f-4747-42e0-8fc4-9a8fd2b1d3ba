module.exports = {

"[project]/.next-internal/server/app/api/analyze/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/constants [external] (constants, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("constants", () => require("constants"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/filesystem.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FileSystemManager": (()=>FileSystemManager),
    "fileSystem": (()=>fileSystem)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fs-extra/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
class FileSystemManager {
    projectRoot;
    backupDir;
    constructor(projectRoot){
        this.projectRoot = projectRoot || process.cwd();
        this.backupDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.projectRoot, '.self-improving-backups');
        this.ensureBackupDir();
    }
    async ensureBackupDir() {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(this.backupDir);
    }
    async readFile(filePath) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        return await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readFile(fullPath, 'utf-8');
    }
    async writeFile(filePath, content) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(fullPath));
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].writeFile(fullPath, content, 'utf-8');
    }
    async fileExists(filePath) {
        try {
            const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].access(fullPath);
            return true;
        } catch  {
            return false;
        }
    }
    async getFileInfo(filePath) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, filePath);
        const stats = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].stat(fullPath);
        const info = {
            name: __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].basename(filePath),
            path: filePath,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            lastModified: stats.mtime
        };
        if (info.type === 'file' && this.isTextFile(filePath)) {
            try {
                info.content = await this.readFile(filePath);
            } catch (error) {
                console.warn(`Could not read file content for ${filePath}:`, error);
            }
        }
        return info;
    }
    async listDirectory(dirPath = '', recursive = false) {
        const fullPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, dirPath);
        const items = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readdir(fullPath);
        const fileInfos = [];
        for (const item of items){
            // Skip node_modules, .git, and backup directories
            if (this.shouldSkipPath(item)) continue;
            const itemPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(dirPath, item);
            try {
                const info = await this.getFileInfo(itemPath);
                fileInfos.push(info);
                if (recursive && info.type === 'directory') {
                    const subItems = await this.listDirectory(itemPath, true);
                    fileInfos.push(...subItems);
                }
            } catch (error) {
                console.warn(`Could not get info for ${itemPath}:`, error);
            }
        }
        return fileInfos;
    }
    async createBackup(files, description) {
        const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, backupId);
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(backupPath);
        const backupInfo = {
            id: backupId,
            timestamp: new Date(),
            files: files,
            description: description
        };
        // Copy files to backup directory
        for (const file of files){
            try {
                const sourcePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, file);
                const targetPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, file);
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(targetPath));
                await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].copy(sourcePath, targetPath);
            } catch (error) {
                console.warn(`Could not backup file ${file}:`, error);
            }
        }
        // Save backup metadata
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].writeJSON(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, 'backup-info.json'), backupInfo, {
            spaces: 2
        });
        return backupId;
    }
    async restoreBackup(backupId) {
        const backupPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, backupId);
        const backupInfoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, 'backup-info.json');
        if (!await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(backupInfoPath)) {
            throw new Error(`Backup ${backupId} not found`);
        }
        const backupInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readJSON(backupInfoPath);
        for (const file of backupInfo.files){
            try {
                const sourcePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(backupPath, file);
                const targetPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].resolve(this.projectRoot, file);
                if (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(sourcePath)) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].ensureDir(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(targetPath));
                    await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].copy(sourcePath, targetPath);
                }
            } catch (error) {
                console.warn(`Could not restore file ${file}:`, error);
            }
        }
    }
    async listBackups() {
        const backups = [];
        if (!await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(this.backupDir)) {
            return backups;
        }
        const backupDirs = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readdir(this.backupDir);
        for (const dir of backupDirs){
            try {
                const backupInfoPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.backupDir, dir, 'backup-info.json');
                if (await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].pathExists(backupInfoPath)) {
                    const backupInfo = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fs$2d$extra$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].readJSON(backupInfoPath);
                    // Convert timestamp string back to Date object
                    if (backupInfo.timestamp && typeof backupInfo.timestamp === 'string') {
                        backupInfo.timestamp = new Date(backupInfo.timestamp);
                    }
                    backups.push(backupInfo);
                }
            } catch (error) {
                console.warn(`Could not read backup info for ${dir}:`, error);
            }
        }
        return backups.sort((a, b)=>{
            // Handle case where timestamp might be missing or invalid
            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : 0;
            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : 0;
            return timeB - timeA;
        });
    }
    isTextFile(filePath) {
        const textExtensions = [
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.json',
            '.md',
            '.txt',
            '.css',
            '.scss',
            '.html',
            '.xml',
            '.yml',
            '.yaml',
            '.env',
            '.gitignore',
            '.eslintrc',
            '.prettierrc',
            '.config',
            '.lock'
        ];
        const ext = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].extname(filePath).toLowerCase();
        return textExtensions.includes(ext) || !ext;
    }
    shouldSkipPath(pathName) {
        const skipPatterns = [
            'node_modules',
            '.git',
            '.next',
            'dist',
            'build',
            '.self-improving-backups',
            '.env.local',
            '.DS_Store',
            'Thumbs.db'
        ];
        return skipPatterns.some((pattern)=>pathName.includes(pattern));
    }
    getProjectRoot() {
        return this.projectRoot;
    }
}
const fileSystem = new FileSystemManager();
}}),
"[project]/src/lib/ai-client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AIClient": (()=>AIClient),
    "aiClient": (()=>aiClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
class AIClient {
    client;
    model;
    constructor(){
        if (!process.env.OPENROUTER_API_KEY) {
            throw new Error('OPENROUTER_API_KEY environment variable is required');
        }
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            baseURL: 'https://openrouter.ai/api/v1',
            apiKey: process.env.OPENROUTER_API_KEY,
            defaultHeaders: {
                'HTTP-Referer': ("TURBOPACK compile-time value", "http://localhost:3000") || 'http://localhost:3000',
                'X-Title': ("TURBOPACK compile-time value", "Self-Improving App") || 'Self-Improving App'
            }
        });
        // Default to GPT-4.1 Nano as specified
        this.model = process.env.OPENROUTER_MODEL || 'openai/gpt-4.1-nano';
    }
    async analyzeCode(code, fileName) {
        const prompt = this.createAnalysisPrompt(code, fileName);
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert code analyzer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.2,
                max_tokens: 3000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                console.warn('No response content from AI model');
                return this.createEmptyAnalysis();
            }
            const result = this.parseAnalysisResponse(content, fileName);
            // If parsing failed, create a basic analysis with some default metrics
            if (result.issues.length === 0 && result.suggestions.length === 0 && result.metrics.linesOfCode === 0) {
                return this.createBasicAnalysis(code, fileName);
            }
            return result;
        } catch (error) {
            console.error('Error analyzing code:', error);
            return this.createBasicAnalysis(code, fileName);
        }
    }
    async generateImprovement(code, fileName, improvementType, context) {
        const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert software engineer. You must respond with valid JSON only. Do not include any explanatory text before or after the JSON response. CRITICAL: Ensure all code you generate has balanced brackets and valid syntax. Double-check your suggestedCode before responding.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                console.warn('No response content from AI model for improvement generation');
                return null;
            }
            const result = this.parseImprovementResponse(content, fileName, improvementType);
            // If parsing failed but we have content, try to create a basic suggestion
            if (!result && content.length > 10) {
                console.warn('Failed to parse improvement response, creating fallback suggestion');
                return this.createFallbackSuggestion(fileName, improvementType, content);
            }
            return result;
        } catch (error) {
            console.error('Error generating improvement:', error);
            return null;
        }
    }
    async generateNewFeature(description, existingCode, fileName) {
        const prompt = `
Generate a new feature based on this description: "${description}"

Existing code context:
\`\`\`${this.getFileExtension(fileName)}
${existingCode}
\`\`\`

Please provide:
1. A clear implementation plan
2. The new code to add
3. Any modifications needed to existing code
4. Potential impacts and considerations

Return your response as JSON with this structure:
{
  "title": "Feature title",
  "description": "Detailed description",
  "originalCode": "existing code that needs modification",
  "suggestedCode": "new/modified code",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "description of benefits"
}
`;
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert software engineer specializing in feature development.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.5,
                max_tokens: 2000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                return null;
            }
            return this.parseImprovementResponse(content, fileName, 'feature');
        } catch (error) {
            console.error('Error generating new feature:', error);
            return null;
        }
    }
    createAnalysisPrompt(code, fileName) {
        return `
Analyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:

File: ${fileName}
\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

IMPORTANT: Respond with ONLY valid JSON. Do not include any explanatory text before or after the JSON.

Required JSON structure:
{
  "issues": [
    {
      "type": "error",
      "message": "Issue description",
      "line": 10,
      "severity": 5
    }
  ],
  "suggestions": [
    {
      "type": "performance",
      "title": "Suggestion title",
      "description": "Detailed description",
      "originalCode": "code to replace",
      "suggestedCode": "replacement code",
      "confidence": 0.8,
      "impact": "medium",
      "estimatedBenefit": "Description of benefits"
    }
  ],
  "metrics": {
    "linesOfCode": 50,
    "complexity": 3,
    "maintainability": 8,
    "performance": 7
  }
}

Ensure all string values are properly quoted and the JSON is valid.`;
    }
    createImprovementPrompt(code, fileName, improvementType, context) {
        return `
Generate a ${improvementType} improvement for this code:

File: ${fileName}
${context ? `Context: ${context}` : ''}

\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

CRITICAL REQUIREMENTS:
1. Respond with ONLY valid JSON - no explanatory text before or after
2. Ensure all brackets (), [], {} are properly balanced in the suggestedCode
3. Escape all quotes and special characters properly in JSON strings
4. The originalCode must be an exact substring from the input code
5. The suggestedCode must be syntactically valid ${this.getFileExtension(fileName)} code

Return this exact JSON structure:
{
  "title": "Improvement title",
  "description": "Detailed description of the improvement",
  "originalCode": "Exact code to be replaced (must exist in input)",
  "suggestedCode": "Improved replacement code (must be syntactically valid)",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "Description of expected benefits"
}

Double-check that your suggestedCode has balanced brackets and valid syntax before responding.`;
    }
    parseAnalysisResponse(content, fileName) {
        try {
            // GPT-4.1 Nano returns clean JSON, so try direct parsing first
            const trimmedContent = content.trim();
            let parsed;
            try {
                // Direct parsing - this should work for GPT-4.1 Nano
                parsed = JSON.parse(trimmedContent);
                console.log('Direct JSON parsing successful');
            } catch (directParseError) {
                console.log('Direct JSON parsing failed, trying extraction methods...');
                console.log('Parse error:', directParseError);
                // If direct parsing fails, try to extract JSON from response
                let jsonStr = this.extractJSON(content);
                if (!jsonStr) {
                    console.warn('No valid JSON found in AI response, using fallback analysis');
                    return this.createBasicAnalysis(content, fileName);
                }
                try {
                    // Try parsing the extracted JSON without cleaning first
                    parsed = JSON.parse(jsonStr);
                    console.log('Extracted JSON parsing successful');
                } catch (extractedParseError) {
                    console.log('Extracted JSON parsing failed, trying cleanup...');
                    console.log('Extracted parse error:', extractedParseError);
                    console.log('JSON string before cleanup:', jsonStr.substring(0, 100) + '...');
                    // Only clean if extraction also fails
                    jsonStr = this.cleanupJSON(jsonStr);
                    console.log('JSON string after cleanup:', jsonStr.substring(0, 100) + '...');
                    parsed = JSON.parse(jsonStr);
                    console.log('Cleaned JSON parsing successful');
                }
            }
            // Validate and normalize the response structure
            const normalized = this.normalizeAnalysisResponse(parsed, fileName);
            return normalized;
        } catch (error) {
            console.error('Error parsing analysis response:', error);
            console.error('Raw content preview:', content.substring(0, 500) + '...');
            // Try to extract partial information if full JSON parsing fails
            const fallback = this.extractPartialAnalysis(content, fileName);
            if (fallback) {
                console.log('Using fallback partial analysis');
                return fallback;
            }
            console.log('Using basic analysis fallback');
            return this.createBasicAnalysis(content, fileName);
        }
    }
    extractPartialAnalysis(content, fileName) {
        try {
            // Try to extract individual sections even if the full JSON is malformed
            const result = {
                issues: [],
                suggestions: [],
                metrics: {
                    linesOfCode: 0,
                    complexity: 0,
                    maintainability: 5,
                    performance: 5
                }
            };
            // Try to extract issues
            const issuesMatch = content.match(/"issues"\s*:\s*\[([\s\S]*?)\]/);
            if (issuesMatch) {
                try {
                    const issuesJson = `[${issuesMatch[1]}]`;
                    const issues = JSON.parse(issuesJson);
                    if (Array.isArray(issues)) {
                        result.issues = issues.map((issue)=>({
                                type: issue.type || 'info',
                                message: issue.message || 'Issue detected',
                                file: fileName,
                                line: issue.line,
                                severity: issue.severity || 1
                            }));
                    }
                } catch (e) {
                    console.warn('Could not parse issues section');
                }
            }
            // Try to extract metrics
            const metricsMatch = content.match(/"metrics"\s*:\s*\{([^}]*)\}/);
            if (metricsMatch) {
                try {
                    const metricsJson = `{${metricsMatch[1]}}`;
                    const metrics = JSON.parse(metricsJson);
                    result.metrics = {
                        linesOfCode: metrics.linesOfCode || 0,
                        complexity: metrics.complexity || 0,
                        maintainability: metrics.maintainability || 5,
                        performance: metrics.performance || 5
                    };
                } catch (e) {
                    console.warn('Could not parse metrics section');
                }
            }
            return result;
        } catch (error) {
            console.warn('Could not extract partial analysis');
            return null;
        }
    }
    extractJSON(content) {
        // Try multiple patterns to find JSON
        const patterns = [
            /\{[\s\S]*\}/,
            /```json\s*(\{[\s\S]*?\})\s*```/,
            /```\s*(\{[\s\S]*?\})\s*```/
        ];
        for (const pattern of patterns){
            const match = content.match(pattern);
            if (match) {
                return match[1] || match[0];
            }
        }
        return null;
    }
    cleanupJSON(jsonStr) {
        // Remove common formatting issues
        let cleaned = jsonStr.replace(/,\s*}/g, '}') // Remove trailing commas in objects
        .replace(/,\s*]/g, ']') // Remove trailing commas in arrays
        .replace(/'/g, '"') // Replace single quotes with double quotes
        .replace(/(\w+):/g, '"$1":') // Quote unquoted keys
        .replace(/"\s*"/g, '""') // Fix empty strings
        .replace(/\\'/g, "'") // Fix escaped single quotes
        .replace(/\\"/g, '\\"') // Ensure double quotes are properly escaped
        .replace(/\n/g, '\\n') // Escape newlines in strings
        .replace(/\r/g, '\\r') // Escape carriage returns
        .replace(/\t/g, '\\t') // Escape tabs
        .trim();
        // Fix common issues with property names that got over-quoted
        cleaned = cleaned.replace(/"(\w+)":/g, '"$1":');
        // Fix issues where quotes inside strings break JSON
        // This is a simple approach - find strings and escape internal quotes
        cleaned = this.fixQuotesInStrings(cleaned);
        return cleaned;
    }
    fixQuotesInStrings(jsonStr) {
        // This is a simplified approach to fix quotes in string values
        // It's not perfect but handles most common cases
        try {
            // Try to parse as-is first
            JSON.parse(jsonStr);
            return jsonStr;
        } catch (error) {
            // If parsing fails, try to fix common quote issues
            let fixed = jsonStr;
            // Replace unescaped quotes in string values (basic heuristic)
            fixed = fixed.replace(/"([^"]*)"([^"]*)"([^"]*)":/g, '"$1\\"$2\\"$3":');
            fixed = fixed.replace(/:\s*"([^"]*)"([^"]*)"([^"]*)"(?=\s*[,}])/g, ': "$1\\"$2\\"$3"');
            return fixed;
        }
    }
    normalizeAnalysisResponse(parsed, fileName) {
        const result = {
            issues: [],
            suggestions: [],
            metrics: {
                linesOfCode: 0,
                complexity: 0,
                maintainability: 0,
                performance: 0
            }
        };
        // Normalize issues
        if (Array.isArray(parsed.issues)) {
            result.issues = parsed.issues.map((issue)=>({
                    type: issue.type || 'info',
                    message: issue.message || 'No message provided',
                    file: fileName,
                    line: issue.line || undefined,
                    column: issue.column || undefined,
                    severity: issue.severity || 1
                }));
        }
        // Normalize suggestions
        if (Array.isArray(parsed.suggestions)) {
            result.suggestions = parsed.suggestions.map((suggestion)=>({
                    id: this.generateId(),
                    type: suggestion.type || 'refactor',
                    title: suggestion.title || 'Improvement suggestion',
                    description: suggestion.description || 'No description provided',
                    file: fileName,
                    originalCode: suggestion.originalCode || '',
                    suggestedCode: suggestion.suggestedCode || '',
                    confidence: Math.min(Math.max(suggestion.confidence || 0.5, 0), 1),
                    impact: suggestion.impact || 'medium',
                    estimatedBenefit: suggestion.estimatedBenefit || 'Improved code quality'
                }));
        }
        // Normalize metrics
        if (parsed.metrics && typeof parsed.metrics === 'object') {
            result.metrics = {
                linesOfCode: Math.max(parsed.metrics.linesOfCode || 0, 0),
                complexity: Math.max(parsed.metrics.complexity || 0, 0),
                maintainability: Math.min(Math.max(parsed.metrics.maintainability || 5, 0), 10),
                performance: Math.min(Math.max(parsed.metrics.performance || 5, 0), 10)
            };
        }
        return result;
    }
    parseImprovementResponse(content, fileName, type = 'refactor') {
        try {
            const trimmedContent = content.trim();
            let parsed;
            try {
                // Direct parsing for GPT-4.1 Nano clean JSON
                parsed = JSON.parse(trimmedContent);
            } catch (directParseError) {
                console.log('Direct improvement JSON parsing failed, trying extraction...');
                let jsonStr = this.extractJSON(content);
                if (!jsonStr) {
                    console.warn('No valid JSON found in improvement response');
                    return null;
                }
                try {
                    parsed = JSON.parse(jsonStr);
                } catch (extractedParseError) {
                    jsonStr = this.cleanupJSON(jsonStr);
                    parsed = JSON.parse(jsonStr);
                }
            }
            // Normalize the improvement response
            return {
                id: this.generateId(),
                type: type,
                file: fileName,
                title: parsed.title || 'Code Improvement',
                description: parsed.description || 'No description provided',
                originalCode: parsed.originalCode || '',
                suggestedCode: parsed.suggestedCode || '',
                confidence: Math.min(Math.max(parsed.confidence || 0.7, 0), 1),
                impact: parsed.impact || 'medium',
                estimatedBenefit: parsed.estimatedBenefit || 'Improved code quality'
            };
        } catch (error) {
            console.error('Error parsing improvement response:', error);
            console.error('Raw content preview:', content.substring(0, 200) + '...');
            return null;
        }
    }
    createEmptyAnalysis() {
        return {
            issues: [],
            suggestions: [],
            metrics: {
                linesOfCode: 0,
                complexity: 0,
                maintainability: 0,
                performance: 0
            }
        };
    }
    createBasicAnalysis(code, fileName) {
        const lines = code.split('\n').length;
        const complexity = Math.min(Math.floor(lines / 10), 10);
        return {
            issues: [
                {
                    type: 'info',
                    message: 'AI analysis temporarily unavailable - showing basic metrics only',
                    file: fileName,
                    severity: 1
                }
            ],
            suggestions: [],
            metrics: {
                linesOfCode: lines,
                complexity: complexity,
                maintainability: Math.max(10 - complexity, 1),
                performance: 5
            }
        };
    }
    createFallbackSuggestion(fileName, improvementType, content) {
        return {
            id: this.generateId(),
            type: improvementType,
            title: `${improvementType.charAt(0).toUpperCase() + improvementType.slice(1)} Improvement`,
            description: 'AI generated an improvement suggestion but the response format was invalid. Please review the raw suggestion.',
            file: fileName,
            originalCode: '// Original code section',
            suggestedCode: content.substring(0, 500) + (content.length > 500 ? '...' : ''),
            confidence: 0.3,
            impact: 'low',
            estimatedBenefit: 'Manual review required due to parsing issues'
        };
    }
    getFileExtension(fileName) {
        const ext = fileName.split('.').pop()?.toLowerCase();
        switch(ext){
            case 'ts':
            case 'tsx':
                return 'typescript';
            case 'js':
            case 'jsx':
                return 'javascript';
            case 'css':
                return 'css';
            case 'json':
                return 'json';
            default:
                return 'text';
        }
    }
    generateId() {
        return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
}
const aiClient = new AIClient();
}}),
"[project]/src/lib/analyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CodeAnalyzer": (()=>CodeAnalyzer),
    "codeAnalyzer": (()=>codeAnalyzer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$acorn$2f$dist$2f$acorn$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/acorn/dist/acorn.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$parser$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/parser/lib/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/filesystem.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-client.ts [app-route] (ecmascript)");
;
;
;
;
class CodeAnalyzer {
    analysisCache = new Map();
    async analyzeProject() {
        console.log('Starting project analysis...');
        const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory('src', true);
        const codeFiles = files.filter((file)=>file.type === 'file' && this.isAnalyzableFile(file.name));
        const fileAnalyses = [];
        const allSuggestions = [];
        for (const file of codeFiles){
            try {
                const analysis = await this.analyzeFile(file);
                fileAnalyses.push(analysis);
                allSuggestions.push(...analysis.analysis.suggestions);
            } catch (error) {
                console.error(`Error analyzing file ${file.path}:`, error);
            }
        }
        const overallMetrics = this.calculateProjectMetrics(fileAnalyses);
        return {
            files: fileAnalyses,
            overallMetrics,
            suggestions: allSuggestions,
            lastAnalyzed: new Date()
        };
    }
    async analyzeFile(file) {
        // Check cache first
        const cacheKey = `${file.path}_${file.lastModified?.getTime()}`;
        if (this.analysisCache.has(cacheKey)) {
            return this.analysisCache.get(cacheKey);
        }
        console.log(`Analyzing file: ${file.path}`);
        const content = file.content || await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(file.path);
        // Parse AST for JavaScript/TypeScript files
        let ast = null;
        let dependencies = [];
        let exports = [];
        if (this.isJavaScriptFile(file.name)) {
            try {
                ast = this.parseAST(content, file.name);
                dependencies = this.extractDependencies(ast);
                exports = this.extractExports(ast);
            } catch (error) {
                console.warn(`Could not parse AST for ${file.path}:`, error);
                // Continue without AST - the AI analysis will still work
                ast = null;
                dependencies = [];
                exports = [];
            }
        }
        // Get AI analysis
        const analysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiClient"].analyzeCode(content, file.path);
        const fileAnalysis = {
            file: {
                ...file,
                content
            },
            analysis,
            ast,
            dependencies,
            exports
        };
        // Cache the result
        this.analysisCache.set(cacheKey, fileAnalysis);
        return fileAnalysis;
    }
    async generateImprovements(file, improvementTypes = [
        'performance',
        'refactor',
        'style'
    ]) {
        const content = file.content || await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(file.path);
        const suggestions = [];
        for (const type of improvementTypes){
            try {
                const suggestion = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiClient"].generateImprovement(content, file.path, type, `Improve ${type} aspects of this code`);
                if (suggestion) {
                    suggestions.push(suggestion);
                }
            } catch (error) {
                console.error(`Error generating ${type} improvement for ${file.path}:`, error);
            }
        }
        return suggestions;
    }
    async generateNewFeature(description, targetFile) {
        let contextFile = targetFile;
        let contextCode = '';
        if (!contextFile) {
            // Find the most relevant file based on the description
            const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory('src', true);
            const mainFiles = files.filter((f)=>f.name.includes('page.tsx') || f.name.includes('index.tsx') || f.name.includes('App.tsx'));
            contextFile = mainFiles[0]?.path || 'src/app/page.tsx';
        }
        try {
            contextCode = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].readFile(contextFile);
        } catch (error) {
            console.warn(`Could not read context file ${contextFile}:`, error);
        }
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiClient"].generateNewFeature(description, contextCode, contextFile);
    }
    parseAST(code, fileName) {
        const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
        const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');
        if (isTypeScript) {
            // Use Babel parser for TypeScript files
            try {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$parser$2f$lib$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(code, {
                    sourceType: 'module',
                    allowImportExportEverywhere: true,
                    allowReturnOutsideFunction: true,
                    plugins: [
                        'typescript',
                        ...isJSX ? [
                            'jsx'
                        ] : [],
                        'decorators-legacy',
                        'classProperties',
                        'objectRestSpread',
                        'asyncGenerators',
                        'functionBind',
                        'exportDefaultFrom',
                        'exportNamespaceFrom',
                        'dynamicImport',
                        'nullishCoalescingOperator',
                        'optionalChaining'
                    ]
                });
            } catch (error) {
                throw new Error(`Could not parse TypeScript file ${fileName}: ${error}`);
            }
        } else {
            // Use Acorn for JavaScript files
            try {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$acorn$2f$dist$2f$acorn$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(code, {
                    ecmaVersion: 2022,
                    sourceType: 'module',
                    allowImportExportEverywhere: true,
                    allowReturnOutsideFunction: true
                });
            } catch (error) {
                // If parsing fails, try with different options
                try {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$acorn$2f$dist$2f$acorn$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parse"])(code, {
                        ecmaVersion: 2022,
                        sourceType: 'script'
                    });
                } catch (secondError) {
                    throw new Error(`Could not parse JavaScript file ${fileName}: ${error}`);
                }
            }
        }
    }
    extractDependencies(ast) {
        const dependencies = [];
        if (!ast || !ast.body) return dependencies;
        for (const node of ast.body){
            if (node.type === 'ImportDeclaration' && node.source?.value) {
                dependencies.push(node.source.value);
            }
        }
        return [
            ...new Set(dependencies)
        ]; // Remove duplicates
    }
    extractExports(ast) {
        const exports = [];
        if (!ast || !ast.body) return exports;
        for (const node of ast.body){
            if (node.type === 'ExportNamedDeclaration') {
                if (node.declaration) {
                    if (node.declaration.type === 'FunctionDeclaration' && node.declaration.id) {
                        exports.push(node.declaration.id.name);
                    } else if (node.declaration.type === 'VariableDeclaration') {
                        for (const declarator of node.declaration.declarations){
                            if (declarator.id?.name) {
                                exports.push(declarator.id.name);
                            }
                        }
                    }
                }
                if (node.specifiers) {
                    for (const specifier of node.specifiers){
                        if (specifier.exported?.name) {
                            exports.push(specifier.exported.name);
                        }
                    }
                }
            } else if (node.type === 'ExportDefaultDeclaration') {
                exports.push('default');
            }
        }
        return [
            ...new Set(exports)
        ]; // Remove duplicates
    }
    calculateProjectMetrics(fileAnalyses) {
        const totalFiles = fileAnalyses.length;
        let totalLinesOfCode = 0;
        let totalComplexity = 0;
        let totalMaintainability = 0;
        let issueCount = 0;
        let suggestionCount = 0;
        for (const analysis of fileAnalyses){
            const metrics = analysis.analysis.metrics;
            totalLinesOfCode += metrics.linesOfCode;
            totalComplexity += metrics.complexity;
            totalMaintainability += metrics.maintainability;
            issueCount += analysis.analysis.issues.length;
            suggestionCount += analysis.analysis.suggestions.length;
        }
        return {
            totalFiles,
            totalLinesOfCode,
            averageComplexity: totalFiles > 0 ? totalComplexity / totalFiles : 0,
            averageMaintainability: totalFiles > 0 ? totalMaintainability / totalFiles : 0,
            issueCount,
            suggestionCount
        };
    }
    isAnalyzableFile(fileName) {
        const analyzableExtensions = [
            '.js',
            '.jsx',
            '.ts',
            '.tsx',
            '.css',
            '.json'
        ];
        return analyzableExtensions.some((ext)=>fileName.endsWith(ext));
    }
    isJavaScriptFile(fileName) {
        return fileName.endsWith('.js') || fileName.endsWith('.jsx') || fileName.endsWith('.ts') || fileName.endsWith('.tsx');
    }
    clearCache() {
        this.analysisCache.clear();
    }
    getCacheSize() {
        return this.analysisCache.size;
    }
}
const codeAnalyzer = new CodeAnalyzer();
}}),
"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analyzer.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/filesystem.ts [app-route] (ecmascript)");
;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const file = searchParams.get('file');
        const full = searchParams.get('full') === 'true';
        if (file) {
            // Analyze specific file
            const cacheKey = `fileInfo:${file}`;
            const cache = new Map(); // move this line to module scope, outside the GET function
            const cachedFileInfo = cache.get(cacheKey);
            if (cachedFileInfo) {
            // use cached
            } else {
                const fileInfo1 = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].getFileInfo(file);
                cache.set(cacheKey, fileInfo1);
            }
            const analysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].analyzeFile(fileInfo);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: analysis
            });
        } else if (full) {
            // Full project analysis
            const analysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].analyzeProject();
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: analysis
            });
        } else {
            // Quick project overview
            const files = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].listDirectory('src', true);
            const codeFiles = files.filter((f)=>f.type === 'file' && (f.name.endsWith('.ts') || f.name.endsWith('.tsx') || f.name.endsWith('.js') || f.name.endsWith('.jsx')));
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    totalFiles: codeFiles.length,
                    files: codeFiles.map((f)=>({
                            name: f.name,
                            path: f.path,
                            size: f.size,
                            lastModified: f.lastModified
                        }))
                }
            });
        }
    } catch (error) {
        console.error('Analysis error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { action, data } = body;
        switch(action){
            case 'generate-improvements':
                const { file, types } = data;
                const fileInfo1 = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$filesystem$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fileSystem"].getFileInfo(file);
                const improvements = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].generateImprovements(fileInfo1, types);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: improvements
                });
            case 'generate-feature':
                const { description, targetFile } = data;
                const feature = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].generateNewFeature(description, targetFile);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    data: feature
                });
            case 'clear-cache':
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["codeAnalyzer"].clearCache();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: true,
                    message: 'Analysis cache cleared'
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: 'Unknown action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Analysis POST error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__21ca7e64._.js.map