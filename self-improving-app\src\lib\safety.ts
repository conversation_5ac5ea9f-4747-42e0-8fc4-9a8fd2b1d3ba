import { fileSystem } from './filesystem';
import { CodeSuggestion } from './ai-client';

export interface SafetyCheck {
  id: string;
  type: 'syntax' | 'dependency' | 'breaking-change' | 'security';
  passed: boolean;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ChangeValidation {
  isValid: boolean;
  checks: SafetyCheck[];
  backupId?: string;
  canProceed: boolean;
  warnings: string[];
  errors: string[];
}

export interface AppliedChange {
  id: string;
  suggestion: CodeSuggestion;
  appliedAt: Date;
  backupId: string;
  validationResult: ChangeValidation;
  success: boolean;
  error?: string;
}

export class SafetyManager {
  private appliedChanges: AppliedChange[] = [];
  private maxBackups: number = 50;

  async validateChange(suggestion: CodeSuggestion): Promise<ChangeValidation> {
    console.log(`Validating change: ${suggestion.title}`);
    
    const checks: SafetyCheck[] = [];
    const warnings: string[] = [];
    const errors: string[] = [];

    // Syntax validation
    const syntaxCheck = await this.validateSyntax(suggestion);
    checks.push(syntaxCheck);
    if (!syntaxCheck.passed) {
      errors.push(syntaxCheck.message);
    }

    // Dependency validation
    const dependencyCheck = await this.validateDependencies(suggestion);
    checks.push(dependencyCheck);
    if (!dependencyCheck.passed) {
      if (dependencyCheck.severity === 'critical' || dependencyCheck.severity === 'high') {
        errors.push(dependencyCheck.message);
      } else {
        warnings.push(dependencyCheck.message);
      }
    }

    // Breaking change detection
    const breakingChangeCheck = await this.detectBreakingChanges(suggestion);
    checks.push(breakingChangeCheck);
    if (!breakingChangeCheck.passed) {
      warnings.push(breakingChangeCheck.message);
    }

    // Security validation
    const securityCheck = await this.validateSecurity(suggestion);
    checks.push(securityCheck);
    if (!securityCheck.passed) {
      if (securityCheck.severity === 'critical' || securityCheck.severity === 'high') {
        errors.push(securityCheck.message);
      } else {
        warnings.push(securityCheck.message);
      }
    }

    const isValid = checks.every(check => check.passed || check.severity === 'low');
    const canProceed = errors.length === 0;

    return {
      isValid,
      checks,
      canProceed,
      warnings,
      errors
    };
  }

  async applyChange(suggestion: CodeSuggestion, force: boolean = false): Promise<AppliedChange> {
    console.log(`Applying change: ${suggestion.title}`);
    
    // Validate the change first
    const validation = await this.validateChange(suggestion);
    
    if (!validation.canProceed && !force) {
      const appliedChange: AppliedChange = {
        id: this.generateId(),
        suggestion,
        appliedAt: new Date(),
        backupId: '',
        validationResult: validation,
        success: false,
        error: 'Validation failed: ' + validation.errors.join(', ')
      };
      
      this.appliedChanges.push(appliedChange);
      return appliedChange;
    }

    // Create backup before applying changes
    const backupId = await fileSystem.createBackup(
      [suggestion.file],
      `Before applying: ${suggestion.title}`
    );

    validation.backupId = backupId;

    try {
      // Read current file content
      const currentContent = await fileSystem.readFile(suggestion.file);
      
      // Apply the change
      let newContent: string;
      if (suggestion.originalCode && suggestion.originalCode.trim()) {
        // Replace specific code section
        newContent = currentContent.replace(suggestion.originalCode, suggestion.suggestedCode);
        
        // Verify the replacement actually happened
        if (newContent === currentContent) {
          throw new Error('Original code not found in file - no changes made');
        }
      } else {
        // If no original code specified, append or replace entire file
        newContent = suggestion.suggestedCode;
      }

      // Write the new content
      await fileSystem.writeFile(suggestion.file, newContent);

      const appliedChange: AppliedChange = {
        id: this.generateId(),
        suggestion,
        appliedAt: new Date(),
        backupId,
        validationResult: validation,
        success: true
      };

      this.appliedChanges.push(appliedChange);
      await this.cleanupOldBackups();
      
      console.log(`Successfully applied change: ${suggestion.title}`);
      return appliedChange;

    } catch (error) {
      // If application failed, restore from backup
      try {
        await fileSystem.restoreBackup(backupId);
      } catch (restoreError) {
        console.error('Failed to restore backup after failed change:', restoreError);
      }

      const appliedChange: AppliedChange = {
        id: this.generateId(),
        suggestion,
        appliedAt: new Date(),
        backupId,
        validationResult: validation,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      this.appliedChanges.push(appliedChange);
      return appliedChange;
    }
  }

  async rollbackChange(changeId: string): Promise<boolean> {
    const change = this.appliedChanges.find(c => c.id === changeId);
    if (!change) {
      throw new Error(`Change ${changeId} not found`);
    }

    if (!change.success) {
      throw new Error('Cannot rollback a change that was not successfully applied');
    }

    try {
      await fileSystem.restoreBackup(change.backupId);
      console.log(`Successfully rolled back change: ${change.suggestion.title}`);
      return true;
    } catch (error) {
      console.error(`Failed to rollback change ${changeId}:`, error);
      return false;
    }
  }

  private async validateSyntax(suggestion: CodeSuggestion): Promise<SafetyCheck> {
    try {
      // Basic syntax validation for JavaScript/TypeScript
      if (this.isJavaScriptFile(suggestion.file)) {
        // Try to parse the suggested code
        const code = suggestion.suggestedCode;
        
        // Basic checks
        if (!code || code.trim().length === 0) {
          return {
            id: this.generateId(),
            type: 'syntax',
            passed: false,
            message: 'Suggested code is empty',
            severity: 'high'
          };
        }

        // Check for balanced brackets
        if (!this.hasBalancedBrackets(code)) {
          return {
            id: this.generateId(),
            type: 'syntax',
            passed: false,
            message: 'Unbalanced brackets in suggested code',
            severity: 'high'
          };
        }
      }

      return {
        id: this.generateId(),
        type: 'syntax',
        passed: true,
        message: 'Syntax validation passed',
        severity: 'low'
      };
    } catch (error) {
      return {
        id: this.generateId(),
        type: 'syntax',
        passed: false,
        message: `Syntax validation failed: ${error}`,
        severity: 'high'
      };
    }
  }

  private async validateDependencies(suggestion: CodeSuggestion): Promise<SafetyCheck> {
    try {
      const code = suggestion.suggestedCode;
      
      // Check for new imports that might not exist
      const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
      const matches = Array.from(code.matchAll(importRegex));
      
      for (const match of matches) {
        const importPath = match[1];
        
        // Check if it's a relative import
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
          // TODO: Validate that the file exists
          continue;
        }
        
        // Check if it's a known package (basic check)
        if (!this.isKnownPackage(importPath)) {
          return {
            id: this.generateId(),
            type: 'dependency',
            passed: false,
            message: `Unknown dependency: ${importPath}`,
            severity: 'medium'
          };
        }
      }

      return {
        id: this.generateId(),
        type: 'dependency',
        passed: true,
        message: 'Dependency validation passed',
        severity: 'low'
      };
    } catch (error) {
      return {
        id: this.generateId(),
        type: 'dependency',
        passed: false,
        message: `Dependency validation failed: ${error}`,
        severity: 'medium'
      };
    }
  }

  private async detectBreakingChanges(suggestion: CodeSuggestion): Promise<SafetyCheck> {
    try {
      const originalCode = suggestion.originalCode;
      const suggestedCode = suggestion.suggestedCode;

      // Check for removed exports
      const originalExports = this.extractExports(originalCode);
      const suggestedExports = this.extractExports(suggestedCode);
      
      const removedExports = originalExports.filter(exp => !suggestedExports.includes(exp));
      
      if (removedExports.length > 0) {
        return {
          id: this.generateId(),
          type: 'breaking-change',
          passed: false,
          message: `Potentially breaking change: removed exports ${removedExports.join(', ')}`,
          severity: 'medium'
        };
      }

      return {
        id: this.generateId(),
        type: 'breaking-change',
        passed: true,
        message: 'No breaking changes detected',
        severity: 'low'
      };
    } catch (error) {
      return {
        id: this.generateId(),
        type: 'breaking-change',
        passed: true,
        message: 'Breaking change detection skipped due to error',
        severity: 'low'
      };
    }
  }

  private async validateSecurity(suggestion: CodeSuggestion): Promise<SafetyCheck> {
    try {
      const code = suggestion.suggestedCode;
      
      // Basic security checks
      const securityIssues: string[] = [];
      
      // Check for eval usage
      if (code.includes('eval(')) {
        securityIssues.push('Usage of eval() detected');
      }
      
      // Check for innerHTML usage
      if (code.includes('innerHTML')) {
        securityIssues.push('Usage of innerHTML detected (potential XSS risk)');
      }
      
      // Check for document.write
      if (code.includes('document.write')) {
        securityIssues.push('Usage of document.write detected');
      }

      if (securityIssues.length > 0) {
        return {
          id: this.generateId(),
          type: 'security',
          passed: false,
          message: `Security concerns: ${securityIssues.join(', ')}`,
          severity: 'medium'
        };
      }

      return {
        id: this.generateId(),
        type: 'security',
        passed: true,
        message: 'Security validation passed',
        severity: 'low'
      };
    } catch (error) {
      return {
        id: this.generateId(),
        type: 'security',
        passed: true,
        message: 'Security validation skipped due to error',
        severity: 'low'
      };
    }
  }

  private hasBalancedBrackets(code: string): boolean {
    const brackets = { '(': ')', '[': ']', '{': '}' };
    const stack: string[] = [];
    let inString = false;
    let stringChar = '';
    let escaped = false;

    for (let i = 0; i < code.length; i++) {
      const char = code[i];

      // Handle escape sequences
      if (escaped) {
        escaped = false;
        continue;
      }

      if (char === '\\') {
        escaped = true;
        continue;
      }

      // Handle string boundaries
      if ((char === '"' || char === "'" || char === '`') && !inString) {
        inString = true;
        stringChar = char;
        continue;
      } else if (char === stringChar && inString) {
        inString = false;
        stringChar = '';
        continue;
      }

      // Skip bracket checking inside strings
      if (inString) {
        continue;
      }

      // Check brackets
      if (char in brackets) {
        stack.push(char);
      } else if (Object.values(brackets).includes(char)) {
        const last = stack.pop();
        if (!last || brackets[last as keyof typeof brackets] !== char) {
          return false;
        }
      }
    }

    return stack.length === 0;
  }

  private extractExports(code: string): string[] {
    const exports: string[] = [];
    const exportRegex = /export\s+(?:default\s+)?(?:function|class|const|let|var)\s+(\w+)/g;
    
    let match;
    while ((match = exportRegex.exec(code)) !== null) {
      exports.push(match[1]);
    }
    
    return exports;
  }

  private isKnownPackage(packageName: string): boolean {
    const knownPackages = [
      'react', 'react-dom', 'next', 'typescript', 'tailwindcss',
      'lucide-react', 'openai', 'fs-extra', 'acorn'
    ];
    
    return knownPackages.some(pkg => packageName.startsWith(pkg));
  }

  private isJavaScriptFile(fileName: string): boolean {
    return fileName.endsWith('.js') || 
           fileName.endsWith('.jsx') || 
           fileName.endsWith('.ts') || 
           fileName.endsWith('.tsx');
  }

  private async cleanupOldBackups(): Promise<void> {
    try {
      const backups = await fileSystem.listBackups();
      if (backups.length > this.maxBackups) {
        const oldBackups = backups.slice(this.maxBackups);
        // TODO: Implement backup cleanup
        console.log(`Should cleanup ${oldBackups.length} old backups`);
      }
    } catch (error) {
      console.warn('Failed to cleanup old backups:', error);
    }
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  getAppliedChanges(): AppliedChange[] {
    return [...this.appliedChanges];
  }

  getChangeById(id: string): AppliedChange | undefined {
    return this.appliedChanges.find(change => change.id === id);
  }
}

export const safetyManager = new SafetyManager();
